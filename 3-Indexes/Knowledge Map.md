---
created: {{date}} {{time}}
type: index
tags: [知识地图, 索引]
---

# 知识地图

## 1. 软件测试
### 测试工具
- [[Pytest]] - Python测试框架
- [[Selenium]] - Web自动化测试
- [[UIAutoTest]] - UI自动化框架
- [[request]] - HTTP请求库
- [[Postman]] - 接口测试工具

### 测试类型
- [[功能测试]] - 功能测试方法
- [[性能测试]] - 性能测试技术
- [[接口测试]] - 接口测试实践
- [[系统测试]] - 系统测试方法

## 2. 开发技术
### 编程基础
- [[learnpython]] - Python编程基础
- [[3-Tags/Java]] - Java开发基础
- [[Shell]] - Shell脚本编程
- [[HTML]] - 网页开发基础

### 数据库技术
- [[MySQL]] - MySQL数据库
- [[Redis]] - Redis缓存
- [[SQL]] - SQL语言基础

## 3. 运维技术
### 系统管理
- [[Linux]] - Linux系统管理
- [[Docker]] - 容器化技术
- [[命令行]] - 命令行操作

### 中间件服务
- [[Nginx]] - Web服务器
- [[RabbitMQ]] - 消息队列
- [[MinIO]] - 对象存储

## 学习路径
1. 基础阶段
   - 学习测试理论
   - 掌握[[learnpython|Python编程基础]]
   - 了解Linux系统

2. 进阶阶段
   - 自动化测试框架
   - 接口测试技术
   - 数据库操作

3. 高级阶段
   - 性能测试
   - 容器化部署
   - 持续集成

## 技能关联
```mermaid
graph TD
    A[软件测试] --> B[自动化测试]
    A --> C[性能测试]
    A --> D[接口测试]
    B --> E[Selenium]
    B --> F[Pytest]
    D --> G[Postman]
    D --> H[request]
```

软件开发和测试领域的知识体系总览，包含测试技术、开发技术和运维技术三大方向。