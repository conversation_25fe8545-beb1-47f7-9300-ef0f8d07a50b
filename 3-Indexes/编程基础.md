---
created: {{date}} {{time}}
type: index
tags: [编程技术, 索引]
---

# 编程基础知识索引

## 1. 编程语言
### Python基础
- [[learnpython]] - Python基础教程
- [[Python爬虫]] - 网络爬虫实践
- [[request]] - HTTP请求库

### 其他语言
- [[3-Tags/Java]] - Java编程基础
- [[Shell]] - Shell脚本编程
- [[HTML]] - 网页开发基础

## 2. 编程实践
### 基础练习
- [[编程练习]] - 编程基础练习
- [[算法]] - 算法设计与实现
- [[100题]] - 编程题目练习

### 实战项目
- [[Shell脚本编程：猜数字游戏实现]] - Shell编程实践
- [[Linux环境下的Java项目部署]] - Java项目部署

## 3. 开发环境
### 命令行工具
- [[命令行]] - 命令行基础
- [[Linux基础命令和软件测试入门]] - Linux命令入门

### 开发工具
- [[IDE]] - 开发环境工具
- [[git配置教程]] - 版本控制工具

## 学习路径
1. 基础入门
   - 编程语言基础
   - 算法与数据结构
   - 开发工具使用

2. 实践提升
   - 项目实战练习
   - 代码优化技巧
   - 开发规范学习

编程基础涵盖了编程语言、算法、数据结构等基本知识，是进行软件开发的必备技能。
  