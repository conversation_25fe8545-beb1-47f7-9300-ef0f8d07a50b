---
created: {{date}} {{time}}
type: index
tags: [测试技术, 索引]
---

# 软件测试知识索引

## 1. 测试基础
### 理论知识
- [[软件测试]] - 测试基础理论
- [[测试理论]] - 测试方法论
- [[测试流程]] - 标准测试流程
- [[测试用例]] - 用例设计方法

### 测试类型
- [[功能测试]] - 功能测试方法
- [[性能测试]] - 性能测试技术
- [[接口测试]] - 接口测试实践
- [[系统测试]] - 系统测试方法

## 2. 自动化测试
### 框架工具
- [[Pytest]] - Python测试框架
- [[Selenium]] - Web自动化测试
- [[UIAutoTest]] - UI自动化框架
- [[request]] - 接口测试工具

### 实践案例
- [[APP测试]] - 移动应用测试
- [[Web测试]] - 网页测试实践
- [[接口测试]] - 接口测试案例

## 3. 项目实践
### 功能测试
- [[车辆管理]] - 车辆管理系统测试
- [[购物车测试]] - 购物车功能测试
- [[微信支付测试]] - 支付功能测试

### 系统测试
- [[车辆预订模块测试]] - 预订系统测试
- [[库存管理系统登录功能测试]] - 登录功能测试

## 4. 测试工具
### 开发工具
- [[learnpython]] - Python基础与测试脚本开发
- [[Postman]] - 接口测试工具
- [[Selenium]] - Web自动化工具

### 测试环境
- [[Docker]] - 容器化测试环境
- [[Linux]] - 测试环境部署

## 相关资源
- 测试框架文档：[[Pytest]], [[Selenium]]
- 实践指南：[[软件测试]], [[自动化测试]]
- 项目案例：[[车辆管理]], [[购物车测试]]

## 学习路径
1. 基础入门
   - 测试理论基础
   - 测试用例设计
   - 测试流程规范

2. 工具掌握
   - [[learnpython|Python编程基础]]
   - 自动化测试框架
   - 测试工具使用

3. 实践提升
   - 项目实战练习
   - 问题分析解决
   - 测试报告编写

### 接口测试
- [[接口测试]] - 接口测试理论与实践
- [[HTTP协议]] - HTTP协议基础
- [[RESTful API]] - REST接口设计
- [[Postman]] - 接口测试工具
- [[learnpython]] - Python基础与测试脚本
- [[Pytest]] - 测试框架
- [[金融项目测试实战]] - 实战案例
