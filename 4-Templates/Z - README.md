# 模板文件 (Templates)

这个文件夹用于存放各类笔记和文档的模板，确保内容的规范性和一致性。

[[模板使用指南]]

## 模板分类
### 日常记录
1. Daily Template
   - 日常学习记录
   - 工作内容记录
   - 待办事项管理

2. Review Templates
   - 周复盘模板
   - 月度总结模板
   - 年度回顾模板

3. Quick Templates
   - 快速笔记模板
   - 临时记录模板
   - 灵感记录模板

### 知识管理
1. Note Templates
   - 粗略笔记模板
   - 完整笔记模板
   - 源材料模板

2. Knowledge Templates
   - 知识地图模板
   - 学习路径模板
   - 知识回顾模板

3. Project Templates
   - 项目文档模板
   - 技术方案模板
   - 总结报告模板

## 模板规范
### 基本要求
- 清晰的结构层次
- 统一的格式标准
- 必要的说明注解
- 合理的空间布局

### 使用建议
- 根据需求选择合适的模板
- 保持模板的简洁实用
- 适当自定义和调整
- 注意版本的统一性

## 维护管理
### 更新建议
- 定期检查模板使用情况
- 收集用户反馈和建议
- 优化模板结构和内容
- 添加新的模板类型

### 版本控制
- 记录模板版本信息
- 说明更新内容
- 保持向后兼容
- 及时发布更新说明

## 注意事项
- 遵循统一的命名规范
- 保持模板的可维护性
- 注意模板间的关联性
- 提供必要的使用说明 

# 模板使用指南

## 模板说明
1. `Full Note.md`: 完整笔记模板，用于详细的知识点记录
2. `Quick Note.md`: 快速笔记模板，用于临时想法和简短记录
3. `Daily.md`: 每日笔记模板，用于记录日常学习内容
4. `Weekly Review.md`: 每周回顾模板，用于总结一周所学
5. `Monthly Review.md`: 每月回顾模板，用于月度学习总结
6. `Yearly Review.md`: 年度回顾模板，用于年度学习回顾
7. `Project Template.md`: 项目模板，用于项目相关记录
8. `Knowledge Map Template.md`: 知识地图模板，用于知识体系梳理
9. `Review Template.md`: 复习模板，用于定期复习记录
10. `Source Material.md`: 资料模板，用于记录学习资源

## 新知识整合步骤指南

### 1. 选择合适的模板
- 学习新知识点 → Full Note.md
- 项目实践 → Project Template.md
- 日常记录 → Daily.md
- 阶段总结 → Review Template.md
- 临时想法 → Quick Note.md

### 2. 内容组织方式
- 使用统一的 frontmatter
- 添加状态标签
- 关联相关标签
- 按模板结构填写内容
- 保持格式一致性

### 3. 知识关联建立
- 在 3-tags 中创建或更新标签
- 在 4-Indexes 中更新索引
- 添加双向链接
- 维护知识地图

### 4. 复习计划制定
- 创建复习记录
- 设置复习周期
- 记录重要知识点
- 更新学习进度

### 5. 实践要点
- 保持结构清晰
- 及时更新关联
- 定期整理归档
- 维护知识体系

## 使用建议
1. 先了解现有知识结构
2. 选择合适的模板
3. 遵循统一的格式
4. 建立有效的关联
5. 保持持续更新

## 注意事项
1. 确保 frontmatter 格式正确
2. 使用统一的标签格式
3. 保持目录结构清晰
4. 定期检查和更新链接
5. 避免重复创建类似内容 

实例：
将 Flask 登录界面学习整合到笔记体系的步骤：

1. 创建详细笔记
   位置：6-Full Notes/Flask登录界面实践.md
   内容结构：
   - frontmatter (created, aliases)
   - 状态标签
   - 关联标签：[[Web开发]] [[Flask]] [[learnpython]] [[前端开发]] [[后端开发]] [[登录认证]]
   - 具体内容：
     * 项目概述
     * 技术栈
     * 实现步骤
     * 关键点
     * 遇到的问题
     * 改进方向
     * 相关资源

2. 创建或更新相关标签
   位置：3-tags/
   需要的标签文件：
   - Flask.md
   - Web开发.md
   - 前端开发.md
   - 后端开发.md
   - 登录认证.md

3. 更新索引文件
   位置：4-Indexes/
   需要更新的索引：
   - 编程基础.md（添加 Flask 相关内容）
   - Knowledge Map.md（在开发技术部分添加 Web 开发相关内容）

4. 关联已有笔记
   - 在 learnpython.md 中添加 Flask 相关内容
   - 在 Web开发相关笔记中添加登录界面实践的引用
   - 在后端开发笔记中添加 Flask 框架相关内容

5. 标签结构建议
   Flask.md:
   - 关联：[[Flask登录界面实践]] [[learnpython]] [[Web开发]] [[后端开发]] [[Python框架]] [[登录认证]]
   
   Web开发.md:
   - 关联：[[Flask]] [[前端开发]] [[后端开发]] [[HTML]] [[登录认证]]
   
   登录认证.md:
   - 关联：[[Flask登录界面实践]] [[Web开发]] [[安全测试]] [[权限管理]]

6. 可选补充
   - 添加每日学习记录到 6-Reviews/定期复习
   - 创建相关练习和改进计划
   - 记录遇到的问题和解决方案