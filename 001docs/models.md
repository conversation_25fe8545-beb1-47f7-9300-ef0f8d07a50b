# Available Models - DeepTask AI Providers

DeepTask支持三个高性价比的AI服务商，专注于中国AI生态系统。

## 支持的AI服务商

### DeepSeek (官方SDK)
- **官网**: [platform.deepseek.com](https://platform.deepseek.com/)
- **特点**: 高性价比推理模型，支持深度思考
- **SDK**: @ai-sdk/deepseek

### SiliconFlow (硅基流动)
- **官网**: [cloud.siliconflow.cn](https://cloud.siliconflow.cn/)
- **特点**: OpenAI兼容接口，成本优化
- **SDK**: OpenAI兼容接口

### Groq
- **官网**: [console.groq.com](https://console.groq.com/)
- **特点**: 超高速推理，适合快速响应场景
- **SDK**: @ai-sdk/groq

## 主要模型 (Main Models)

| 供应商 | 模型名称 | SWE评分 | 输入成本 | 输出成本 | 最大Token |
|--------|----------|---------|----------|----------|-----------|
| deepseek | deepseek-chat | 0.5 | 0.14 | 0.28 | 64000 |
| deepseek | deepseek-coder | 0.6 | 0.14 | 0.28 | 64000 |
| deepseek | deepseek-reasoner | 0.65 | 0.55 | 2.19 | 64000 |
| siliconflow | deepseek-chat | 0.5 | 0.1 | 0.2 | 64000 |
| siliconflow | qwen-max | 0.45 | 0.12 | 0.24 | 32000 |
| siliconflow | qwen-plus | 0.4 | 0.08 | 0.16 | 32000 |
| siliconflow | claude-3-5-sonnet-20241022 | 0.49 | 0.3 | 1.5 | 64000 |
| groq | llama-3.3-70b-versatile | 0.4 | 0.59 | 0.79 | 32768 |
| groq | mixtral-8x7b-32768 | 0.35 | 0.24 | 0.24 | 32768 |
| groq | llama-3.1-70b-versatile | 0.38 | 0.59 | 0.79 | 32768 |
| groq | gemma2-9b-it | 0.25 | 0.2 | 0.2 | 8192 |

## 研究模型 (Research Models)

研究模型适用于复杂分析和深度思考任务：

| 供应商 | 模型名称 | SWE评分 | 输入成本 | 输出成本 | 最大Token |
|--------|----------|---------|----------|----------|-----------|
| deepseek | deepseek-reasoner | 0.65 | 0.55 | 2.19 | 64000 |
| siliconflow | deepseek-chat | 0.5 | 0.1 | 0.2 | 64000 |
| siliconflow | qwen-max | 0.45 | 0.12 | 0.24 | 32000 |
| groq | llama-3.3-70b-versatile | 0.4 | 0.59 | 0.79 | 32768 |

## 备用模型 (Fallback Models)

备用模型在主模型失败时自动启用：

| 供应商 | 模型名称 | SWE评分 | 输入成本 | 输出成本 | 最大Token |
|--------|----------|---------|----------|----------|-----------|
| deepseek | deepseek-chat | 0.5 | 0.14 | 0.28 | 64000 |
| deepseek | deepseek-coder | 0.6 | 0.14 | 0.28 | 64000 |
| siliconflow | deepseek-chat | 0.5 | 0.1 | 0.2 | 64000 |
| siliconflow | qwen-plus | 0.4 | 0.08 | 0.16 | 32000 |
| groq | mixtral-8x7b-32768 | 0.35 | 0.24 | 0.24 | 32768 |
| groq | gemma2-9b-it | 0.25 | 0.2 | 0.2 | 8192 |

## 成本说明

- **成本单位**: 美元/百万Token
- **SWE评分**: Software Engineering评分，越高表示编程能力越强
- **推荐配置**:
  - 主模型: deepseek-coder (高编程能力)
  - 研究模型: deepseek-reasoner (深度思考)
  - 备用模型: siliconflow/deepseek-chat (成本优化)

## 模型配置

使用以下命令配置模型：

```bash
# 交互式配置
deeptask models --setup

# 直接设置主模型
deeptask models --set-main=deepseek-coder

# 设置研究模型
deeptask models --set-research=deepseek-reasoner

# 设置备用模型
deeptask models --set-fallback=siliconflow/deepseek-chat
```

## API密钥获取

### DeepSeek
1. 访问 [platform.deepseek.com](https://platform.deepseek.com/)
2. 注册账号并完成实名认证
3. 在API密钥页面创建新密钥
4. 格式: `sk-...`

### SiliconFlow
1. 访问 [cloud.siliconflow.cn](https://cloud.siliconflow.cn/)
2. 注册账号并完成认证
3. 在控制台创建API密钥
4. 格式: `sk-...`

### Groq
1. 访问 [console.groq.com](https://console.groq.com/)
2. 使用Google或GitHub账号登录
3. 在API Keys页面创建新密钥
4. 格式: `gsk_...`

## 最佳实践

1. **成本优化**: 优先使用SiliconFlow的模型，成本最低
2. **性能优先**: 需要高质量输出时使用DeepSeek原生模型
3. **速度优先**: 需要快速响应时使用Groq模型
4. **混合使用**: 配置不同角色使用不同供应商，实现成本和性能的平衡
