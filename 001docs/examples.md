# Example Cursor AI Interactions

Here are some common interactions with Cursor AI when using DeepTask:

## Starting a new project

```
I've just initialized a new project with DeepTask. I have a PRD at .taskmaster/docs/prd.txt.
Can you help me parse it and set up the initial tasks?
```

## Working on tasks

```
What's the next task I should work on? Please consider dependencies and priorities.
```

## Implementing a specific task

```
I'd like to implement task 4. Can you help me understand what needs to be done and how to approach it?
```

## Managing subtasks

```
I need to regenerate the subtasks for task 3 with a different approach. Can you help me clear and regenerate them?
```

## Handling changes

```
I've decided to use MongoDB instead of PostgreSQL. Can you update all future tasks to reflect this change?
```

## Completing work

```
I've finished implementing the authentication system described in task 2. All tests are passing.
Please mark it as complete and tell me what I should work on next.
```

## Reorganizing tasks

```
I think subtask 5.2 would fit better as part of task 7. Can you move it there?
```

(Agent runs: `deeptask move --from=5.2 --to=7.3`)

```
Task 8 should actually be a subtask of task 4. Can you reorganize this?
```

(Agent runs: `deeptask move --from=8 --to=4.1`)

```
I just merged the main branch and there's a conflict in tasks.json. My teammates created tasks 10-15 on their branch while I created tasks 10-12 on my branch. Can you help me resolve this by moving my tasks?
```

(Agent runs:

```bash
deeptask move --from=10 --to=16
deeptask move --from=11 --to=17
deeptask move --from=12 --to=18
```

)

## Analyzing complexity

```
Can you analyze the complexity of our tasks to help me understand which ones need to be broken down further?
```

## Viewing complexity report

```
Can you show me the complexity report in a more readable format?
```

### Breaking Down Complex Tasks

```
Task 5 seems complex. Can you break it down into subtasks?
```

(Agent runs: `deeptask expand --id=5`)

```
Please break down task 5 using research-backed generation.
```

(Agent runs: `deeptask expand --id=5 --research`)

### Updating Tasks with Research

```
We need to update task 15 based on the latest React Query v5 changes. Can you research this and update the task?
```

(Agent runs: `deeptask update-task --id=15 --prompt="Update based on React Query v5 changes" --research`)

### Adding Tasks with Research

```
Please add a new task to implement user profile image uploads using Cloudinary, research the best approach.
```

(Agent runs: `deeptask add-task --prompt="Implement user profile image uploads using Cloudinary" --research`)

---

# 🔄 完整项目工作流程示例

本示例演示了使用DeepTask开发一个"智能任务管理平台"的完整流程，展示所有24个工具的实际使用。

## 项目背景

我们要开发一个名为"TaskHub"的智能任务管理平台，包含用户管理、任务管理、团队协作、数据分析等功能。

## 第一阶段：项目初始化

### 1. 初始化项目 (`initialize_project`)

```
请帮我初始化一个新的DeepTask项目，项目名称为"TaskHub"，描述为"智能任务管理平台"
```

Agent执行：
```bash
deeptask init --name "TaskHub" --description "智能任务管理平台" --version "0.1.0"
```

### 2. 配置AI模型 (`models`)

```
请帮我配置AI模型，使用DeepSeek作为主模型，SiliconFlow作为研究模型
```

Agent执行：
```bash
deeptask models --set-main deepseek-chat --set-research siliconflow-qwen-2.5-32b-instruct
```

### 3. 解析PRD文档 (`parse_prd`)

创建PRD文档 `.taskmaster/docs/taskhub-prd.txt`：

```
# TaskHub - 智能任务管理平台 PRD

## 项目概述
TaskHub是一个面向现代团队的智能任务管理平台，旨在提高团队协作效率和项目管理质量。

## 核心功能

### 用户管理系统
- 用户注册登录（邮箱/第三方登录）
- 用户资料管理
- 权限管理（管理员/普通用户）

### 任务管理核心
- 任务创建、编辑、删除
- 任务状态管理（待办/进行中/已完成/已取消）
- 任务优先级设置
- 任务标签和分类
- 任务依赖关系管理

### 团队协作功能
- 团队创建和管理
- 成员邀请和权限分配
- 任务分配和协作
- 评论和讨论系统

### 数据分析和报告
- 任务完成统计
- 团队效率分析
- 项目进度可视化
- 导出报告功能

## 技术要求
- 前端：React + TypeScript + Tailwind CSS
- 后端：Node.js + Express + MongoDB
- 认证：JWT + OAuth2.0
- 实时通信：WebSocket
- 部署：Docker + AWS

## 性能要求
- 页面加载时间 < 2秒
- 支持1000+并发用户
- 99.9%可用性
```

```
请解析我的PRD文档生成初始任务
```

Agent执行：
```bash
deeptask parse-prd .taskmaster/docs/taskhub-prd.txt --num-tasks 12
```

## 第二阶段：任务分析和规划

### 4.  (`get_tasks`)

```
显示所有任务的概览
```

Agent执行：
```bash
deeptask list --with-subtasks
```

### 5. 分析项目复杂度 (`analyze_project_complexity`)

```
请分析项目的复杂度，帮助我了解哪些任务需要进一步分解
```

Agent执行：
```bash
deeptask analyze-complexity --research --threshold 7
```

### 6. 查看复杂度报告 (`complexity_report`)

```
显示复杂度分析报告的可读格式
```

Agent执行：
```bash
deeptask complexity-report
```

### 7. 批量分解复杂任务 (`expand_all`)

```
请将所有复杂任务分解为子任务，使用研究模式获得更好的分解质量
```

Agent执行：
```bash
deeptask expand --all --research --force
```

## 第三阶段：开发执行

### 8. 获取下一个任务 (`next_task`)

```
告诉我下一个应该工作的任务是什么
```

Agent执行：
```bash
deeptask next
```

### 9. 查看具体任务详情 (`get_task`)

```
显示任务3的详细信息
```

Agent执行：
```bash
deeptask show 3
```

### 10. 设置任务状态 (`set_task_status`)

```
我开始工作任务3了，请将状态设置为进行中
```

Agent执行：
```bash
deeptask set-status --id 3 --status in-progress
```

### 11. 在开发过程中更新子任务 (`update_subtask`)

```
我在实现子任务3.2时发现需要使用bcrypt来加密密码，请记录这个发现
```

Agent执行：
```bash
deeptask update-subtask --id 3.2 --prompt "发现需要使用bcrypt进行密码加密，已安装bcrypt依赖包。实现了密码哈希和验证功能。"
```

## 第四阶段：任务管理和调整

### 12. 添加新发现的任务 (`add_task`)

```
我需要添加一个Redis缓存优化任务，这个任务依赖于任务5，优先级设为高
```

Agent执行：
```bash
deeptask add-task --prompt "实现Redis缓存优化，提高API响应速度" --dependencies 5 --priority high --research
```

### 13. 添加子任务 (`add_subtask`)

```
任务8需要增加一个API文档生成的子任务
```

Agent执行：
```bash
deeptask add-subtask --parent 8 --title "生成API文档" --description "使用Swagger生成完整的API文档"
```

### 14. 更新特定任务 (`update_task`)

```
由于技术选型变更，我们决定使用Prisma替代直接的MongoDB驱动，请更新任务4
```

Agent执行：
```bash
deeptask update-task --id 4 --prompt "技术选型变更：使用Prisma ORM替代直接MongoDB驱动，需要配置Prisma schema和迁移脚本" --research
```

### 15. 批量更新相关任务 (`update`)

```
从任务7开始，所有数据库相关的任务都需要适配Prisma ORM
```

Agent执行：
```bash
deeptask update --from 7 --prompt "适配Prisma ORM：所有数据库操作需要使用Prisma客户端，更新查询语法和数据模型定义" --research
```

## 第五阶段：依赖关系管理

### 16. 添加任务依赖 (`add_dependency`)

```
任务10（前端开发）应该依赖于任务6（API开发），请添加这个依赖关系
```

Agent执行：
```bash
deeptask add-dependency --id 10 --depends-on 6
```

### 17. 移除任务依赖 (`remove_dependency`)

```
任务12不再需要依赖任务5，请移除这个依赖
```

Agent执行：
```bash
deeptask remove-dependency --id 12 --depends-on 5
```

### 18. 验证依赖关系 (`validate_dependencies`)

```
请检查所有任务的依赖关系是否正确，有没有循环依赖或无效依赖
```

Agent执行：
```bash
deeptask validate-dependencies
```

### 19. 修复依赖问题 (`fix_dependencies`)

```
如果发现依赖问题，请自动修复
```

Agent执行：
```bash
deeptask fix-dependencies
```

## 第六阶段：任务重组和优化

### 20. 移动任务位置 (`move_task`)

```
我觉得子任务8.3更适合作为任务11的子任务，请移动它
```

Agent执行：
```bash
deeptask move --from 8.3 --to 11.1
```

### 21. 清理子任务 (`clear_subtasks`)

```
任务9的子任务规划有问题，请清空它的所有子任务
```

Agent执行：
```bash
deeptask clear-subtasks --id 9
```

### 22. 重新分解任务 (`expand_task`)

```
现在请重新分解任务9，生成5个子任务
```

Agent执行：
```bash
deeptask expand --id 9 --num 5 --research --force
```

### 23. 删除不需要的子任务 (`remove_subtask`)

```
子任务7.4已经不需要了，请删除它
```

Agent执行：
```bash
deeptask remove-subtask --id 7.4
```

### 24. 删除完整任务 (`remove_task`)

```
任务15已经不在项目范围内，请完全删除它
```

Agent执行：
```bash
deeptask remove-task --id 15 --yes
```

## 第七阶段：项目完成和文档生成

### 25. 生成任务文件 (`generate`)

```
请生成所有任务的单独Markdown文件，方便查看和分享
```

Agent执行：
```bash
deeptask generate
```

### 26. 完成任务标记

```
我已经完成了任务3及其所有子任务，请将它们标记为完成
```

Agent执行：
```bash
deeptask set-status --id 3,3.1,3.2,3.3,3.4 --status done
```

## 项目总结

通过这个完整的工作流程，我们演示了DeepTask的24个核心工具：

### 🚀 项目启动工具
1. `initialize_project` - 项目初始化
2. `parse_prd` - PRD文档解析
3. `models` - AI模型配置

### 📋 任务查看工具
4. `get_tasks` - 任务列表查看
5. `next_task` - 获取下一个任务
6. `get_task` - 查看任务详情

### ✏️ 任务编辑工具
7. `add_task` - 添加新任务
8. `add_subtask` - 添加子任务
9. `update` - 批量更新任务
10. `update_task` - 更新单个任务
11. `update_subtask` - 更新子任务详情
12. `set_task_status` - 设置任务状态
13. `remove_task` - 删除任务

### 🔧 任务结构工具
14. `expand_task` - 分解单个任务
15. `expand_all` - 批量分解任务
16. `clear_subtasks` - 清空子任务
17. `remove_subtask` - 删除子任务
18. `move_task` - 移动任务位置

### 🔗 依赖管理工具
19. `add_dependency` - 添加依赖
20. `remove_dependency` - 删除依赖
21. `validate_dependencies` - 验证依赖
22. `fix_dependencies` - 修复依赖

### 📊 分析报告工具
23. `analyze_project_complexity` - 复杂度分析
24. `complexity_report` - 复杂度报告

### 📁 文件管理工具
25. `generate` - 生成任务文件

这个完整的工作流程展示了如何使用DeepTask管理从项目初始化到完成的整个开发生命周期，每个工具都在特定的场景中发挥重要作用，帮助开发团队更高效地管理任务和项目进度。
