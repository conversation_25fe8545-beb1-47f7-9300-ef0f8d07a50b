# DeepTask Command Reference

Here's a comprehensive reference of all available commands for DeepTask:

## Parse PRD

```bash
# Parse a PRD file and generate tasks
deeptask parse-prd <prd-file.txt>

# Limit the number of tasks generated
deeptask parse-prd <prd-file.txt> --num-tasks=10
```

## List Tasks

```bash
# List all tasks
deeptask list

# List tasks with a specific status
deeptask list --status=<status>

# List tasks with subtasks
deeptask list --with-subtasks

# List tasks with a specific status and include subtasks
deeptask list --status=<status> --with-subtasks
```

## Show Next Task

```bash
# Show the next task to work on based on dependencies and status
deeptask next
```

## Show Specific Task

```bash
# Show details of a specific task
deeptask show <id>
# or
deeptask show --id=<id>

# View a specific subtask (e.g., subtask 2 of task 1)
deeptask show 1.2
```

## Update Tasks

```bash
# Update tasks from a specific ID and provide context
deeptask update --from=<id> --prompt="<prompt>"

# Update tasks using research role
deeptask update --from=<id> --prompt="<prompt>" --research
```

## Update a Specific Task

```bash
# Update a single task by ID with new information
deeptask update-task --id=<id> --prompt="<prompt>"

# Use research-backed updates
deeptask update-task --id=<id> --prompt="<prompt>" --research
```

## Update a Subtask

```bash
# Append additional information to a specific subtask
deeptask update-subtask --id=<parentId.subtaskId> --prompt="<prompt>"

# Example: Add details about API rate limiting to subtask 2 of task 5
deeptask update-subtask --id=5.2 --prompt="Add rate limiting of 100 requests per minute"

# Use research-backed updates
deeptask update-subtask --id=<parentId.subtaskId> --prompt="<prompt>" --research
```

Unlike the `update-task` command which replaces task information, the `update-subtask` command _appends_ new information to the existing subtask details, marking it with a timestamp. This is useful for iteratively enhancing subtasks while preserving the original content.

## Generate Task Files

```bash
# Generate individual task files from tasks.json
deeptask generate
```

## Set Task Status

```bash
# Set status of a single task
deeptask set-status --id=<id> --status=<status>

# Set status for multiple tasks
deeptask set-status --id=1,2,3 --status=<status>

# Set status for subtasks
deeptask set-status --id=1.1,1.2 --status=<status>
```

When marking a task as "done", all of its subtasks will automatically be marked as "done" as well.

## Expand Tasks

```bash
# Expand a specific task with subtasks
deeptask expand --id=<id> --num=<number>

# Expand with additional context
deeptask expand --id=<id> --prompt="<context>"

# Expand all pending tasks
deeptask expand --all

# Force regeneration of subtasks for tasks that already have them
deeptask expand --all --force

# Research-backed subtask generation for a specific task
deeptask expand --id=<id> --research

# Research-backed generation for all tasks
deeptask expand --all --research
```

## Clear Subtasks

```bash
# Clear subtasks from a specific task
deeptask clear-subtasks --id=<id>

# Clear subtasks from multiple tasks
deeptask clear-subtasks --id=1,2,3

# Clear subtasks from all tasks
deeptask clear-subtasks --all
```

## Analyze Task Complexity

```bash
# Analyze complexity of all tasks
deeptask analyze-complexity

# Save report to a custom location
deeptask analyze-complexity --output=my-report.json

# Use DeepSeek for research-backed complexity analysis
deeptask analyze-complexity --research

# Set a custom complexity threshold (1-10)
deeptask analyze-complexity --threshold=6

# Use an alternative tasks file
deeptask analyze-complexity --file=custom-tasks.json

# Use Perplexity AI for research-backed complexity analysis
deeptask analyze-complexity --research
```

## View Complexity Report

```bash
# Display the task complexity analysis report
deeptask complexity-report

# View a report at a custom location
deeptask complexity-report --file=my-report.json
```

## Managing Task Dependencies

```bash
# Add a dependency to a task
deeptask add-dependency --id=<id> --depends-on=<id>

# Remove a dependency from a task
deeptask remove-dependency --id=<id> --depends-on=<id>

# Validate dependencies without fixing them
deeptask validate-dependencies

# Find and fix invalid dependencies automatically
deeptask fix-dependencies
```

## Move Tasks

```bash
# Move a task or subtask to a new position
deeptask move --from=<id> --to=<id>

# Examples:
# Move task to become a subtask
deeptask move --from=5 --to=7

# Move subtask to become a standalone task
deeptask move --from=5.2 --to=7

# Move subtask to a different parent
deeptask move --from=5.2 --to=7.3

# Reorder subtasks within the same parent
deeptask move --from=5.2 --to=5.4

# Move a task to a new ID position (creates placeholder if doesn't exist)
deeptask move --from=5 --to=25

# Move multiple tasks at once (must have the same number of IDs)
deeptask move --from=10,11,12 --to=16,17,18
```

## Add a New Task

```bash
# Add a new task using AI (main role)
deeptask add-task --prompt="Description of the new task"

# Add a new task using AI (research role)
deeptask add-task --prompt="Description of the new task" --research

# Add a task with dependencies
deeptask add-task --prompt="Description" --dependencies=1,2,3

# Add a task with priority
deeptask add-task --prompt="Description" --priority=high
```

## Initialize a Project

```bash
# Initialize a new project with Task Master structure
deeptask init
```

## Configure AI Models

```bash
# View current AI model configuration and API key status
deeptask models

# Set the primary model for generation/updates
deeptask models --set-main=deepseek-coder

# Set the research model
deeptask models --set-research=deepseek-reasoner

# Set the fallback model
deeptask models --set-fallback=siliconflow/deepseek-chat

# Set the fallback model
deeptask models --set-fallback=claude-3-haiku-20240307

# Set a custom Ollama model for the main role
deeptask models --set-main=my-local-llama --ollama

# Set a custom OpenRouter model for the research role
deeptask models --set-research=google/gemini-pro --openrouter

# Run interactive setup to configure models, including custom ones
deeptask models --setup
```

Configuration is stored in `.taskmasterconfig` in your project root. API keys are still managed via `.env` or MCP configuration. Use `deeptask models` without flags to see available built-in models. Use `--setup` for a guided experience.
