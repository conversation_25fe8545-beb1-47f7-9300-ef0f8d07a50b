# 库存管理系统登录功能测试闪卡

### 库存管理系统登录功能的功能测试包括哪些内容？
1. 用户名测试：
   - 验证用户名合法，其他参数正确，登录成功
   - 验证用户名为空，其他参数正确，登录失败
   - 验证用户名未注册，登录失败
   - 验证用户名超过最大长度，登录失败
   - 验证用户名是否大小写敏感

2. 密码测试：
   - 验证用户名正确，密码错误，登录失败
   - 验证用户名正确，密码为空，登录失败
   - 验证用户名正确，密码为字母+数字+字符，登录成功

### 库存管理系统登录功能的性能测试包括哪些内容？
1. 响应时间要求：
   - 单个用户登录时，系统响应时间要求在3s以内
   - 1000个用户同时登录，系统响应时间在3s以内

2. 资源占用：
   - 性能测试1000个用户同时登录，后台服务器CPU、内存、磁盘、网络资源占用情况不超过限定值

3. 稳定性：
   - 大量用户频繁登录/退出登录，运行一段时间后，是否有内存泄露

### 库存管理系统登录功能的界面UI测试包���哪些内容？
1. 界面适配：
   - 浏览器放大、缩小，界面是否会错位

2. 交互功能：
   - 界面UI一些常用的快捷键，比如tab键、回车是否能正常使用

3. 视觉规范：
   - 界面颜色、布局、字体大小、风格是否统一

### 库存管理系统登录功能的兼容性测试包括哪些内容？
1. 浏览器兼容性：
   - Chrome常见的版本上，功能是否正常
   - Edge常见的版本上，功能是否正常
   - Firefox常见的版本上，功能是否正常

### 库存管理系统登录功能的安全测试包括哪些内容？
1. 登录安全：
   - 退出登录后，点击浏览器的前进按钮，是否可以继续进行操作
   - 密码存储在数据库时，是否加密了，加密算法是否太简单
   - 密码不支持复制粘贴，密码不可见

2. 攻击防护：
   - XSS攻击防护
   - SQL注入防护

### 库存管理系统登录功能的异常测试包括哪些内容？
1. 网络异常：
   - 弱网环境下，进行登录
   - 弱网情况下，多次点击登录按钮
   - 无网络时，提示信息是否正常 