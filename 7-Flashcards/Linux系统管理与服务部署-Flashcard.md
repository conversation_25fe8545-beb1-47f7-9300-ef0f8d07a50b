# Linux系统管理与服务部署闪卡

### systemctl命令有哪些常用操作？
1. 服务状态查看：
   - systemctl status：查看服务状态

2. 服务控制：
   - start：启动服务
   - stop：停止服务
   - restart：重启服务
   - enable：设置开机自启
   - disable：禁止开机自启

### Linux中如何管理进程？
1. 进程查找：
   - ps -ef：查看所有进程
   - ps -ef | grep 程序名：检索特定程序
   - find | grep：使用管道符过滤结果

2. 进程控制：
   - kill -9 pid：强制结束进程
   - ss -lnp | grep：查看端口号

### 服务访问异常如何处理？
1. 网络检查：
   - ping服务IP地址
   - 确认网络连通性

2. 端口检查：
   - ss -lnp | grep 端口号：查看端口占用
   - 确认进程名是否正确

3. 服务状态检查：
   - systemctl查看服务状态
   - 重启服务
   - 查看服务日志

### Linux权限管理包括哪些内容？
1. 用户管理：
   - useradd：添加用户
   - usermod：修改用户属性

2. 组管理：
   - groupadd：添加组
   - usermod -aG：将用户添加到组
   - groups：查看组
   - chgrp：修改文件/目录的组

3. 权限分类：
   - a (all)：所有用户
   - g (group)：组用户
   - u (user)：当前用户
   - o (other)：其他用户

4. 权限操作：
   - chmod：修改权限
   - +：添加权限
   - -：删除权限
   - g+w：给组添加写权限 