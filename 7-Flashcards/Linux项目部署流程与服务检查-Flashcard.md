# Linux项目部署流程与服务检查闪卡

### Linux项目部署前需要检查哪些服务？
1. Java服务：
   - 检查命令：java -version

2. Redis服务：
   - 检查命令：systemctl status redis

3. MySQL服务：
   - 检查命令：systemctl status mysqld

4. MinIO服务：
   - 检查命令：systemctl status minio
   - 启动命令：/opt/minio/minio server /var/lib/minio
   - 后台运行：nohup /opt/minio/minio server /var/lib/minio > /var/log/minio.log &

5. RabbitMQ服务：
   - 检查命令：systemctl status rabbitmq-server
   - 需要在浏览器验证

6. Nginx服务：
   - 检查命令：systemctl status nginx

### 如何设置项目配置文件？
1. SELinux配置：
   - 永久关闭SELinux
   - 修改/etc/selinux/config文件
   - 将SELINUX=enforcing改为SELINUX=disabled

2. 项目文件部署：
   - 使用moba将文件传到/opt/animal
   - 配置application-test.properties文件
   - 修改密码和MySQL文件名

### 项目部署步骤是什么？
1. 文件上传：
   - 上传SQL文件
   - 上传JAR文件
   - 上传配置文件

2. 数据库配置：
   - 使用Navicat连接数据库
   - 新建数据库
   - 运行SQL文件

3. 项目启动：
   - 使用java -jar命令
   - 指定配置文件位置
   - 指定JAR文件位置

### sed命令如何使用？
1. 基本语法：
   - 命令格式：sed [options] 'command' file(s)
   - s：替换文本中的字符串
   - 示例：sed 's/book/books/' file

2. 高级用法：
   - 使用正则表达式
   - 使用管道符 |
   - 示例：echo this is a test line | sed 's/\w/[$]/g'
   - 作用：将每个字符加上[]，输出给$ 