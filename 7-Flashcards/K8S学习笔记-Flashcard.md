# Kubernetes学习笔记闪卡

### 为什么需要K8S？
1. 容器管理需求：
   - 容器故障快速替补
   - 应用规模扩缩容
   - 解决容器编排问题

2. 主流容器编排工具：
   - K8S：谷歌开源（源自Borg系统）
   - Mesos：Apache组织提供
   - Swarm：Docker官方提供

### K8S的基本架构是什么？
1. 集群角色：
   - master（领导）
   - node（工人）

2. 部署方式：
   - 一对多：一个master多个node（测试环境）
   - 多对多：多个master多个node（生产环境）
   - 一对一：适合学习环境

### K8S有哪些主要功能？
1. 自动化管理：
   - 容器自动部署
   - 自动扩容缩容
   - 跨主机管理
   - 故障自动恢复

2. 系统功能：
   - 监控和日志收集
   - 负载均衡
   - 支持灰度发布、金丝雀发布、蓝绿发布
   - 支持多种开发语言

### kubectl命令如何使用？
1. 基本语法：
   - kubectl [command] [type] [name] [可选参数]

2. 常用command：
   - create：创建
   - delete：删除
   - describe：查看详细信息
   - get：查询
   - apply：应用

3. 常用资源类型：
   - node：节点
   - namespace/ns：命名空间
   - pod：容器组
   - deployment/deploy：发布
   - service/svc：服务
   - log：日志

### Pod是什么？如何管理？
1. Pod概念：
   - K8S中管理的最小单位
   - 可运行多个容器

2. Pod管理命令：
   - kubectl get pod -n：查看命名空间中的pod
   - kubectl delete pod：删除pod
   - kubectl exec -it pod名称 -- /bin/bash：进入pod

### 如何部署应用？
1. 使用deployment：
   - 创建：kubectl create deployment --image --replicas
   - 暴露服务：kubectl expose deployment --port --type
   - 扩缩容：kubectl scale deployment --replicas

2. 服务暴露方式：
   - ClusterIP：集群内部访问（默认）
   - ExternalName：依赖外部服务
   - LoadBalancer：使用云服务商负载均衡
   - NodePort：内外部都能访问

### K8S架构中master包含哪些组件？
1. 核心组件：
   - kube-apiserver：操作集群的唯一入口
   - kube-controller-manager：控制器管理器
   - deployment controller：发布控制器
   - namespace controller：命名空间控制器

2. 管理职责：
   - 管理node
   - 监控集群
   - 编排调度
   - 故障处理 