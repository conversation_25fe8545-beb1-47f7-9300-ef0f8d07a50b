# 软件测试基础理论闪卡

### 软件研发流程包括哪些阶段？
1. 需求阶段：
   - 收集用户需求（SE）
   - 输出需求文档
   - 需求澄清会议/评审会议
   - 需求变更管理（CR）

2. 设计阶段：
   - 开发设计（概要设计和详细设计）
   - 测试分析
   - 串讲和反串讲

3. 开发阶段：
   - 编码实现
   - 自验证
   - 交叉代码检视
   - 代码扫描

4. 测试阶段：
   - 测试设计/用例设计
   - 环境搭建
   - 冒烟测试
   - 测试执行
   - 测试报告

5. 发布阶段：
   - 版本发布
   - 用户验收

### 测试人员在需求评审会议中应关注哪些点？
1. 功能规格：
   - 具体功能列表
   - 功能的详细规格（如字数限制、数量限制等）

2. 性能指标：
   - 响应时间要求
   - 并发量要求

3. 兼容性要求：
   - 支持的操作系统
   - 支持的浏览器
   - 支持的设备

### 软件测试的主要类型有哪些？
1. 按照是否关心软件内部结构：
   - 白盒测试
   - 黑盒测试
   - 灰盒测试

2. 按照是否运行程序：
   - 静���测试（代码检视、文档测试、代码扫描）
   - 动态测试（运行产品后测试）

3. 按照自动化程度：
   - 手工测试
   - 自动化测试

4. 按照开发阶段：
   - 单元测试（Unit Test）
   - 集成测试（Integration Test）
   - 冒烟测试（Smoking Test）
   - 系统测试（System Test）
   - 回归测试（Regress Test）
   - 阿尔法测试（Alpha Test）
   - 贝塔测试（Beta Test）
   - 用户验收测试（UAT）

### 测试计划应包含哪些内容？
1. 基本信息：
   - 测试目的
   - 测试范围
   - 测试策略

2. 资源规划：
   - 测试环境
   - 测试人员
   - 测试工具

3. 管理机制：
   - 测试准入机制
   - 测试退出机制
   - 测试风险管理

### 如何编写测试用例？
1. 测试用例的组成：
   - 测试点
   - 测试步骤
   - 预期结果
   - 实际结果

2. 用例设计方法：
   - 等价类划分
   - 边界值分析
   - 场景法
   - 错误推测法

3. 用例评审：
   - 内部评审
   - 评审标准
   - 评审记录

### 如何处理软件缺陷？
1. 缺陷定义：
   - 功能缺失
   - 需求不一致
   - 用户体验问题
   - 异常场景未处理

2. 缺陷管理：
   - 缺陷提交
   - 缺陷跟踪
   - 缺陷验证

3. 争议处理：
   - 与��发沟通
   - 寻找依据
   - 向上反馈
   - 记录沟通过程

4. 偶发缺陷处理：
   - 保留现场信息
   - 定期复现
   - 缺陷降级
   - 评审关闭

### 测试报告包含哪些内容？
1. 版本测试报告：
   - 用例执行情况（按模块、执行人、结果统计）
   - 缺陷解决情况（按模块、提交人、状态、严重程度统计）
   - 测试结论（能否发布）

2. 测试日报：
   - 工作内容
   - 用例执行数量
   - 发现缺陷数量
   - 代码编写情况
   - 疑难问题
   - 风险提示 