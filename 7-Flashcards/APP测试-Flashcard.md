# APP测试闪卡

### Android架构分层包括哪些？
1. 应用程序层：
   - 如微信、支付宝等应用

2. 应用程序框架层：
   - 提供API和工具

3. 核心层：
   - C/C++库
   - 图形渲染
   - 数据库支持
   - 网络功能

4. 运行时环境：
   - 早期使用虚拟机
   - Android 5.0后使用ART

5. 硬件抽象层：
   - 统一硬件设备接口

6. Linux内核：
   - 提供底层驱动

### Android四大组件是什么？
1. Activity（活动）：
   - 用户界面
   - 响应用户操作
   - 主Activity为应用入口

2. Service（后台服务）：
   - 后台运行的服务
   - 如音乐播放

3. Content Provider（内容供应组件）：
   - 管理用户数据
   - 如通讯录、数据库

4. Broadcast Receivers（广播接收组件）：
   - 接收系统广播
   - 处理广播事件

### ADB工具有哪些主要功能？
1. 基本结构：
   - 客户端（adb.exe）：电脑端
   - 守护进程（adbd）：移动设备端
   - 服务器（adbd）：电脑端后台进程

2. 常用命令：
   - adb devices：查看连接设备
   - adb install：安装应用
   - adb shell pm list packages：查看已安装应用
   - adb uninstall：卸载应用
   - adb push/pull：文件传输
   - adb logcat：查看日志

### APP专项测试包括哪些内容？
1. 稳定性测试：
   - 使用Monkey工具
   - 测试崩溃和无响应
   - 内存泄漏检测

2. 性能测试：
   - 启动时间测试
   - 界面响应时间
   - 内存使用率
   - CPU使用率
   - 电量消耗
   - 流量消耗
   - FPS帧率

3. 弱网测试：
   - 测试不同网络环境
   - 检查崩溃和功能可用性
   - 验证重连机制

4. 兼容性测试：
   - 不同设备测试
   - 不同分辨率测试
   - 使用正交实验法

### APP自动化测试工具有哪些？
1. UIAutomator：
   - 安卓自动化框架
   - 基于Java开发
   - 支持模拟器和真机

2. UIAutomator2：
   - UIAutomator升级版
   - Python库封装

3. Appium：
   - 支持iOS和Android
   - 使用广泛
   - 包含服务器端和Inspector工具

### Monkey测试命令有哪些参数？
1. 基本参数：
   - -p：指定测试包名
   - --pct-touch：触摸事件
   - --pct-motion：滑动事件
   - --ignore-crashes：忽略崩溃

2. 日志相关：
   - -v：日志详细程度
   - 1>：正常信息重定向
   - 2>：错误信息重定向

3. 特殊参数：
   - -s：指定种子值
   - --throttle：事件间隔
   - COUNT：事件数量 