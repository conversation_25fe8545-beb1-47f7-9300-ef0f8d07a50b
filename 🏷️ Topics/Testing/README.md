# Testing - 测试技术索引

## 测试类型

### 功能测试
- [[功能测试理论]]
- [[测试用例设计]]
- [[边界值测试]]
- [[等价类划分]]

### 自动化测试
- [[Selenium自动化测试]]
- [[API自动化测试]]
- [[移动端自动化测试]]
- [[测试框架设计]]

### 性能测试
- [[性能测试理论]]
- [[JMeter性能测试]]
- [[负载测试]]
- [[压力测试]]

### 接口测试
- [[API测试基础]]
- [[Postman接口测试]]
- [[接口自动化]]
- [[接口安全测试]]

## 测试工具

### 自动化工具
- [[Selenium]]
- [[Appium]]
- [[Cypress]]
- [[Playwright]]

### 性能工具
- [[JMeter]]
- [[LoadRunner]]
- [[Gatling]]

### 接口工具
- [[Postman]]
- [[Insomnia]]
- [[SoapUI]]

## 测试框架

### Python框架
- [[PyTest]]
- [[unittest]]
- [[Robot Framework]]

### Java框架
- [[TestNG]]
- [[JUnit]]
- [[Cucumber]]

## 测试理论

- [[软件测试基础]]
- [[测试设计方法]]
- [[缺陷管理]]
- [[测试流程]]
- [[质量保证]]

## 实践案例

- [[Web应用测试案例]]
- [[移动应用测试案例]]
- [[API测试案例]]
- [[性能测试案例]]
