# 移动端APP开发流程

## 一、前期准备

### 1. 需求分析
- 与客户沟通，明确移动端APP的核心功能需求
- 确定目标用户群体和主要使用场景
- 划分功能优先级和开发阶段
- 形成详细的需求分析文档

### 2. 技术选型
- 使用Flutter开发跨平台应用(Android、iOS、Web)
- 避免使用非官方推荐的第三方组件库
- 使用Flutter内置的Material Icons或Cupertino Icons
- 制定代码规范和命名约定

### 3. 界面设计
- 参考`移动端APP前端开发提示词.md`中的设计规范
- 单个页面尺寸为375x812px
- 设计风格遵循现代优雅的极简主义美学
- 符合Material Design或Human Interface设计规范
- 确保适配不同屏幕尺寸和刘海屏

## 二、开发准备

### 1. 开发环境搭建
- 安装Flutter SDK和Dart
- 配置Android Studio/Visual Studio Code
- 安装Android SDK和Xcode(Mac环境)
- 配置模拟器和真机调试环境
- 安装必要的Flutter插件

### 2. 项目初始化
- 创建Flutter项目
- 配置pubspec.yaml添加必要依赖
- 设置项目基本结构(MVC/MVVM)
- 配置主题和基础样式
- 配置国际化支持(如需)

### 3. 版本控制
- 创建Git仓库
- 设置.gitignore文件
- 建立开发分支策略
- 设置Cursor记忆库 (使用`cursor rules.md`中的指南)
  - 执行`init memory bank`初始化记忆库
  - 适时使用`update memory bank`更新记忆库

## 三、前端开发流程

### 1. UI原型开发
- 根据`移动端APP前端开发提示词.md`创建静态HTML原型
- 将每个界面作为独立HTML文件存放在UI文件夹中
- 使用iframe在index.html中展示所有页面
- 确保UI符合设计规范

### 2. 转换为Flutter代码
- 根据`前端转向开发移动端.md`指南进行开发
- 参考HTML原型，使用Flutter Widget重构界面
- 使用Flutter内置的Material Components或Cupertino Components
- 遵循Flutter的响应式编程模型

### 3. 功能开发
- 按模块进行开发
- 封装公共组件和工具类
- 实现路由和页面导航
- 处理状态管理(使用Provider/Bloc/Riverpod等)
- 处理页面交互和动画效果
- 适配不同设备和方向(横竖屏)

### 4. 前端数据管理
- 创建数据模型类
- 实现数据持久化(使用Hive/SQLite/SharedPreferences)
- 设计状态管理架构
- 处理离线数据缓存
- 实现数据同步机制

## 四、与后端对接

### 1. 接口定义
- 与后端开发人员共同制定API接口规范
- 定义请求和响应格式
- 明确错误码和处理机制
- 编写接口文档

### 2. 接口联调
- 实现网络请求层(使用Dio/http包)
- 配置开发环境代理
- 逐个接口进行联调
- 处理各种网络状态和异常
- 调整前端逻辑以适配实际接口

### 3. 数据模拟
- 在后端接口未完成前，创建Mock数据
- 使用本地JSON模拟后端响应
- 实现离线开发功能

## 五、测试与优化

### 1. 功能测试
- 编写单元测试
- 编写Widget测试
- 进行集成测试
- 跨平台兼容性测试(Android、iOS和Web)
- 不同设备尺寸和分辨率测试

### 2. 性能优化
- 优化应用启动时间
- 减少UI重绘和布局计算
- 优化图片和资源加载
- 使用懒加载和缓存机制
- 优化网络请求策略
- 内存使用优化

### 3. 用户体验优化
- 优化页面过渡动画
- 改善表单填写体验
- 完善错误处理和提示
- 优化加载状态显示
- 适配深色模式(如需)
- 改善无障碍访问支持

## 六、部署与上线

### 1. Android应用打包
- 配置应用签名
  - 生成签名密钥对(keytool -genkey -v -keystore my-release-key.keystore -alias key_alias -keyalg RSA -keysize 2048 -validity 10000)
  - 妥善保存密钥库文件(.keystore)和密码
  - 在Flutter项目的android/app目录下创建key.properties文件
  - 在key.properties中配置密钥路径、密码和别名
  - 修改android/app/build.gradle添加签名配置
- 设置应用图标和启动页
  - 准备不同分辨率的应用图标(mipmap-hdpi, mipmap-mdpi, mipmap-xhdpi, mipmap-xxhdpi, mipmap-xxxhdpi)
  - 使用Android Studio的Asset Studio生成自适应图标
  - 配置启动页背景颜色和图片(在android/app/src/main/res/drawable/launch_background.xml)
  - 配置启动主题样式(在android/app/src/main/res/values/styles.xml)
  - 确保在主活动中正确设置启动屏幕过渡
- 优化APK/AAB大小
  - 启用R8代码缩减(在android/gradle.properties设置android.enableR8=true)
  - 配置ProGuard规则优化代码(在android/app/proguard-rules.pro)
  - 使用Android App Bundle(AAB)格式替代APK
  - 移除未使用的资源和库
  - 压缩图片资源
  - 配置Gradle构建的splitPerAbi选项生成针对特定CPU架构的APK
- 生成发布版本
  - 设置正确的版本号和构建号(在pubspec.yaml)
  - 修改AndroidManifest.xml添加必要的权限和配置
  - 使用命令生成发布版本：flutter build appbundle (或 flutter build apk --release)
  - 测试发布版本在不同设备上的表现
  - 准备内部测试分发(如使用Firebase App Distribution)
- 准备应用描述和截图
  - 撰写清晰简洁的应用描述(短描述和详细描述)
  - 制作应用宣传图(Feature Graphic，1024x500px)
  - 准备至少3个不同设备分辨率的高质量截图
  - 制作应用演示视频(可选，但推荐)
  - 准备应用隐私政策网页链接

### 2. iOS应用打包
- 配置证书和描述文件
  - 注册Apple开发者账号(个人/企业，99美元/年)
  - 在Apple Developer Portal创建App ID
  - 生成发布证书(Distribution Certificate)
  - 创建发布描述文件(Provisioning Profile)
  - 在Xcode中配置团队和签名设置
  - 设置正确的Bundle Identifier(需与Apple Developer Portal一致)
- 设置应用图标和启动页
  - 准备iOS应用图标(各种尺寸，从20x20到1024x1024)
  - 使用Xcode的Asset Catalog管理图标
  - 配置启动屏幕(在LaunchScreen.storyboard)
  - 准备适配不同设备尺寸的启动图片
  - 确保支持深色模式(如需)
  - 测试在不同iPhone/iPad设备上的显示效果
- 生成IPA文件
  - 在Flutter项目中设置正确的版本号(CFBundleShortVersionString)和构建号(CFBundleVersion)
  - 在ios/Runner/Info.plist中配置必要的权限描述(如相机、位置等)
  - 执行构建命令：flutter build ios --release
  - 在Xcode中创建归档文件(Archive)
  - 通过归档导出IPA文件(选择App Store Connect分发方式)
- 准备AppStore所需的材料
  - 创建App Store Connect账号并登录
  - 创建新应用(填写应用名称、SKU、Bundle ID等)
  - 设置App Store信息(类别、价格、可用性)
  - 准备隐私政策URL(必需)
  - 填写联系信息和版权信息
  - 配置应用内购买项目(如有)
- 设置App Review信息
  - 准备测试账号和密码(如有登录功能)
  - 编写审核注意事项(特别功能的测试方法)
  - 解释使用任何非公开API的原因(如有)
  - 说明应用是否使用IDFA(广告标识符)
  - 列出需要特殊权限的功能用途
  - 准备演示视频(如功能复杂或不明显)

### 3. 应用商店发布
- 上传到Google Play商店
  - 创建Google Play开发者账号(一次性25美元)
  - 创建新应用并填写基本信息
  - 配置内容分级问卷
  - 设置价格和分发国家/地区
  - 上传APK或AAB文件
  - 填写应用详情页信息(描述、截图、宣传图等)
  - 设置分阶段发布策略(内部测试、封闭测试、开放测试、生产环境)
  - 提交审核(通常24小时内完成)
- 上传到Apple App Store
  - 通过Xcode或Application Loader上传IPA文件
  - 等待处理完成(可能需要30分钟到数小时)
  - 在App Store Connect填写版本信息
  - 上传应用预览和截图(必须符合Apple规范)
  - 填写关键词和促销文本
  - 提交审核(一般需要1-3天，节假日可能更长)
  - 审核通过后发布(可选择手动发布或自动发布)
- 设置应用分发区域
  - 在Google Play选择可用国家和地区
  - 在App Store选择可用区域
  - 考虑本地化需求(不同语言版本)
  - 了解不同地区的法律法规要求
  - 设置地区特定的价格(如需)
- 配置价格和付费模式(如适用)
  - 选择免费或付费模式
  - 设置基础价格和货币
  - 配置应用内购买项目
  - 设置订阅选项和试用期(如有)
  - 配置促销代码和优惠(App Store)
  - 设置税务信息和银行账户
- 设置隐私政策和服务条款
  - 撰写符合GDPR、CCPA等法规的隐私政策
  - 准备用户服务协议
  - 创建专门的网页托管这些文档
  - 在应用商店页面提供链接
  - 确保应用内也有隐私政策入口
  - 配置数据收集透明度报告(App Store)

### 4. Web版本部署(如需)
- 构建Web版本
  - 确保Flutter项目支持Web平台(flutter config --enable-web)
  - 执行构建命令：flutter build web --release
  - 优化生成的JavaScript和资源文件
  - 设置正确的base-href(如需在子目录部署)
  - 配置PWA支持(修改manifest.json和添加service-worker.js)
  - 测试在不同浏览器上的兼容性
- 配置服务器和域名
  - 选择合适的Web托管服务(如Firebase Hosting、Netlify、Vercel等)
  - 购买并设置域名
  - 配置DNS记录(A记录、CNAME等)
  - 设置子域名(如需)
  - 完成域名备案(中国大陆服务器必须)
- 设置CDN和缓存策略
  - 配置CDN服务(如Cloudflare、Akamai等)
  - 设置资源缓存策略
  - 配置边缘计算功能(如需)
  - 设置内容压缩(Gzip/Brotli)
  - 启用HTTP/2或HTTP/3以提高性能
  - 配置缓存头(Cache-Control, ETag等)
- 配置SSL证书
  - 申请SSL证书(Let's Encrypt或商业证书)
  - 安装并配置证书
  - 设置HTTPS强制跳转
  - 配置HSTS提高安全性
  - 定期更新证书(设置自动续期)
- 优化Web版本性能
  - 启用懒加载和代码分割
  - 优化首次加载时间
  - 实现预加载关键资源
  - 配置service worker实现离线功能
  - 优化字体加载
  - 实现响应式设计以适应不同设备

### 5. 应用发布后的运营策略
- 应用商店优化(ASO)
  - 优化应用标题和描述中的关键词
  - 鼓励用户评价和留言
  - 定期更新应用截图和描述
  - 监控竞品表现和关键词排名
  - 分析安装转化率和优化商店页面
- 版本迭代计划
  - 制定定期更新计划(如每2-4周)
  - 跟踪用户反馈和应用评分
  - 优先修复关键bug和崩溃问题
  - 安排新功能发布时间表
  - 准备iOS和Android的测试渠道(TestFlight和Google Play内部测试)
- 跨平台一致性维护
  - 确保Android和iOS版本功能同步
  - 维护统一的用户体验
  - 协调不同平台的版本发布
  - 处理平台特有的适配问题
  - 同步Web版本的功能更新

## 七、运营与迭代

### 1. 数据监控
- 集成应用分析工具(如Firebase Analytics)
- 监控崩溃和性能指标
- 收集用户行为数据
- 建立用户反馈渠道
- 设置关键指标监控

### 2. 版本迭代
- 规划功能迭代路线图
- 修复已知问题
- 优化现有功能
- 根据用户反馈添加新功能
- 适配新版本操作系统

### 3. 持续优化
- 定期代码重构
- 更新第三方依赖
- 优化应用体积和性能
- 减少电量消耗
- 增强安全性

## 八、开发提示与最佳实践

### 1. Cursor辅助开发
- 使用Cursor的Memory Bank功能跟踪项目进度
- 在开发过程中使用Plan Mode进行规划
- 使用Act Mode生成和修改代码
- 定期使用`update memory bank`更新记忆库

### 2. Flutter最佳实践
- 使用StatelessWidget和StatefulWidget分离UI和状态
- 遵循单一职责原则设计Widget
- 使用const构造函数优化重建性能
- 合理使用BuildContext
- 避免在build方法中执行耗时操作
- 使用异步加载处理网络请求
- 注意Flutter的特殊限制

### 3. 与客户沟通
- 定期演示开发进度
- 及时反馈技术难点
- 灵活调整开发计划
- 记录需求变更

## 九、与PC端后台管理系统的协同

### 1. 数据共享与同步
- 确保移动端与PC端共用同一套后端API
- 规划用户数据同步策略
- 协调PC端操作对移动端的影响
- 设计跨平台一致的数据结构

### 2. 功能协同
- 明确PC端和移动端各自的功能边界
- 设计PC端管理后台对移动端用户的管理功能
- 确保权限体系的一致性
- 规划数据统计和运营分析方案 