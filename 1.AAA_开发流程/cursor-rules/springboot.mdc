---
description: 
globs: 
alwaysApply: true
---
# Spring Boot开发规范

## 项目结构与组织
- 标准目录结构
  - 按功能模块划分包结构
  - 控制器、服务、仓库、实体等分层设计
  - 推荐结构：
    ```
    com.company.project
    ├── config/          # 配置类
    ├── controller/      # 控制器
    ├── dto/             # 数据传输对象
    ├── model/           # 实体模型
    ├── repository/      # 数据访问层
    ├── service/         # 业务逻辑层
    ├── util/            # 工具类
    └── Application.java # 启动类
    ```
