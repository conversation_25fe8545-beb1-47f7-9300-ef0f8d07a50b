---
description: 
globs: 
alwaysApply: true
---
# Memory Bank Workflows

## Core Workflows (Operating Modes)

User prompts ending with `use Plan mode` or `use Act mode` explicitly guide workflow selection. If unspecified, I will infer the best mode.

### Plan Mode (Strategic Planning & Understanding)

**Trigger:** Initial project analysis, new feature planning, significant refactoring, user prompt `use Plan mode`.

**Action:** Read Memory Bank, analyze request/code, formulate strategy/plan, present plan in chat.

**Diagram:**
```mermaid
flowchart TD
    Start[Start Plan] --> ReadBank[Read Memory Bank]
    ReadBank --> Analyze{Analyze Request/Code}
    Analyze --> DevelopPlan[Develop Strategy/Plan]
    DevelopPlan --> PresentPlan[Present Plan in Chat]
    PresentPlan --> AutoUpdateDocs{Update Docs?}
    AutoUpdateDocs -- Yes --> UpdatePlanDocs[Update activeContext/systemPatterns]
    AutoUpdateDocs -- No --> SuggestGitInit{Suggest Git Init?}
    UpdatePlanDocs --> SuggestGitInit
    SuggestGitInit -- Yes --> PromptGitInit[Remind User: git init / add remote]
    SuggestGitInit -- No --> EndPlan[End Plan]
    PromptGitInit --> EndPlan
```

**Automatic Update Post-Plan:** After presenting the plan and receiving confirmation, I WILL automatically update:
- `activeContext.md` with the new focus or next steps derived from the plan.
- `systemPatterns.md` if significant architectural decisions were made in the plan.
- May also suggest initializing a Git repository if one doesn't seem to exist.

### Act Mode (Code Generation & Execution)

**Trigger:** Implementing defined plans, making specific code changes, user prompt `use Act mode`.

**Action:** Consult Memory Bank & `.cursorrules`, execute the task (write/modify code).

**Diagram:**
```mermaid
flowchart TD
    Start[Start Act] --> CheckMem[Check Memory Bank & .cursorrules]
    CheckMem --> Execute[Execute Task (Code)]
    Execute --> AutoUpdateActDocs[Reflect & Update MB]
    AutoUpdateActDocs --> UpdateAC[Update activeContext.md]
    AutoUpdateActDocs --> UpdateP[Update progress.md (if applicable)]
    AutoUpdateActDocs --> UpdateCR[Update .cursorrules (if learned)]
    UpdateAC & UpdateP & UpdateCR --> SuggestGit[Suggest Comprehensive Git Actions]
    SuggestGit --> EndAct[End Act]
```

**Automatic Update Post-Act:** Immediately after successfully executing the coding task, I WILL automatically:
1. Update `activeContext.md`
2. Update `progress.md`
3. Update `.cursorrules`
4. Suggest Comprehensive Git Actions:
   - Check Status: `git status`
   - Stage Changes: `git add .` or specific files
   - Commit Changes: With a suggested commit message
   - Pull Before Pushing: If collaborating
   - Push Changes: To the remote repository