---
description: 
globs: 
alwaysApply: true
---
# Memory Bank Initialization

## Initialization Process

When the Memory Bank is initialized (via `initialize memory bank` command or when core files are missing), I will:

1. Create the directory structure for the Memory Bank
2. Create all core files with appropriate initial content
3. Suggest next steps to populate the Memory Bank with project-specific information

## Directory Structure

The standard Memory Bank structure is:

```
project_root/
├── .memorybank/           # Main directory for Memory Bank files
│   ├── projectbrief.md    # Project scope and requirements
│   ├── productContext.md  # Project goals and user experience
│   ├── systemPatterns.md  # Architecture design and patterns
│   ├── techContext.md     # Technology stack and dependencies
│   ├── activeContext.md   # Current focus and next steps
│   ├── progress.md        # Project status tracking
│   └── .cursorrules       # Project-specific patterns and decisions
```

## Initial File Templates

When creating the core files, I will use appropriate templates with sections for all necessary project information. For example:

### projectbrief.md
- Project Overview
- Goals and Objectives
- Requirements
- Scope
- Constraints

### activeContext.md
- Current Focus
- Recent Changes
- Next Steps
- Active Decisions

The other core files will follow similar comprehensive templates, appropriate to their purpose.