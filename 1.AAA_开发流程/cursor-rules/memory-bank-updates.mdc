---
description: 
globs: 
alwaysApply: true
---

# Memory Bank Updates & Project Intelligence

## Documentation Updates (Keeping Memory Fresh)

**Triggers for Updates:**
1. User command: `update memory bank` (forces full scan & refresh)
2. Context clarification needed during conversation
3. Discovery of completely new project patterns not captured during Post-Act updates

**Process:** When triggered, review project files, update relevant Memory Bank documents and `.cursorrules`. Remember to include these `.md` files and `.cursorrules` in Git commits to keep the project context versioned alongside the code.

```mermaid
flowchart TD
    subgraph ManualUpdateProcess
        P1[Review Project Files]
        P2[Identify Changes/Needs]
        P3[Update Memory Bank Docs]
        P4[Update .cursorrules]
        P1 --> P2 --> P3 & P4
    end
    ManualTrigger[Manual Trigger e.g., 'update memory bank'] --> ManualUpdateProcess
```

## Project Intelligence (.cursorrules - Learning Log)

**Purpose:** Active learning journal for this specific project.

**Action:**
- **Capture:** Document critical paths, user preferences, project-specific patterns, challenges, decision evolution, tool usage quirks. **Updates primarily happen automatically after `Act Mode` tasks.**
- **Apply:** **Before generating code in Act Mode, I WILL consult `.cursorrules`** to apply learned patterns/constraints.

```mermaid
flowchart TD
    Start{Insight Gained / Task Done}

    subgraph Learn [Learning Process - Often Post-Act Auto]
        D1[Identify Pattern/Decision]
        D2[Validate (Implicitly)]
        D3[Document in .cursorrules]
    end

    subgraph Apply [Usage in Act Mode]
        A1[Read .cursorrules]
        A2[Check for Relevance]
        A3[Apply Learned Patterns]
    end

    Start --> Learn
    Learn --> Apply
```

### What to Capture (Examples):
- "Module X database calls always use `async/await`."
- "User prefers functional components over class components."
- "Workaround for library Y bug Z applied in `[file:...]`."
- "Decision (Post-Act): Refactored function `abc` for clarity."

**Reminder:** While I will automatically update after Plan and Act modes, the `update memory bank` command remains important if you modify files directly or want a full refresh. My Git assistance involves generating **suggestions** for commands and messages for **you to execute manually**. I cannot access your credentials or run Git commands directly.
