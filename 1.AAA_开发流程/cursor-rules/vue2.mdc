---
description: 
globs: 
alwaysApply: true
---
---
description: Vue 2框架的开发规则和最佳实践
globs: ["*.vue", "*.js"]
alwaysApply: false
---

# Vue 2开发指南

## 核心概念

### Vue实例与生命周期

Vue 2应用从创建一个Vue实例开始：

```js
new Vue({
  el: '#app',
  data: {
    message: 'Hello Vue!'
  }
})
```

生命周期钩子函数：
- beforeCreate
- created
- beforeMount
- mounted
- beforeUpdate
- updated
- beforeDestroy
- destroyed

### 响应式系统

Vue 2使用Object.defineProperty实现数据响应式:
- 数组变异方法(push, pop, shift等)可以触发视图更新
- 直接替换数组或对象可以触发更新: `this.items = newItems`
- 无法检测对象属性的添加或删除，使用Vue.set/this.$set替代

## 组件系统

### 组件注册

全局注册：
```js
Vue.component('my-component', {
  template: '<div>A custom component!</div>'
})
```

局部注册：
```js
const Child = {
  template: '<div>A custom component!</div>'
}
new Vue({
  components: {
    'my-component': Child
  }
})
```

### 组件通信

- Props向下传递数据
- 事件向上传递数据
- 依赖注入(provide/inject)传递深层数据
- Vuex管理全局状态

### 插槽(Slots)

基本插槽:
```html
<template>
  <div>
    <slot></slot>
  </div>
</template>
```

具名插槽:
```html
<!-- 子组件 -->
<div>
  <slot name="header"></slot>
  <slot></slot>
</div>

<!-- 父组件 -->
<my-component>
  <template slot="header">Header</template>
  <p>Default slot content</p>
</my-component>
```

作用域插槽:
```html
<!-- 子组件 -->
<div>
  <slot :item="item"></slot>
</div>

<!-- 父组件 -->
<my-component>
  <template slot-scope="slotProps">
    {{ slotProps.item }}
  </template>
</my-component>
```

## 状态管理

### Vuex

Vuex是Vue的状态管理库:

```js
const store = new Vuex.Store({
  state: {
    count: 0
  },
  mutations: {
    increment(state) {
      state.count++
    }
  },
  actions: {
    incrementAsync(context) {
      setTimeout(() => {
        context.commit('increment')
      }, 1000)
    }
  },
  getters: {
    doubleCount: state => {
      return state.count * 2
    }
  }
})
```

使用Vuex:
```js
// 访问状态
this.$store.state.count
// 提交mutation
this.$store.commit('increment')
// 分发action
this.$store.dispatch('incrementAsync')
// 使用getters
this.$store.getters.doubleCount
```

## 路由

### Vue Router

配置路由:
```js
const router = new VueRouter({
  routes: [
    { path: '/', component: Home },
    { path: '/about', component: About }
  ]
})

const app = new Vue({
  router
}).$mount('#app')
```

导航守卫:
```js
router.beforeEach((to, from, next) => {
  // 验证权限等
  if (to.meta.requiresAuth && !isAuthenticated) {
    next('/login')
  } else {
    next()
  }
})
```

## 最佳实践

### 组件设计原则

1. **单一职责**: 每个组件只做一件事
2. **明确API**: 使用props和events定义清晰的接口
3. **组合优于继承**: 使用mixins和组合替代继承
4. **避免直接操作DOM**: 使用ref谨慎访问DOM

### 性能优化

1. **使用v-show代替v-if**: 频繁切换显示状态时
2. **使用computed缓存计算**: 避免在模板中放置复杂表达式
3. **大列表使用v-for加key**: 提供唯一key优化DOM更新
4. **异步组件**: 分割代码，按需加载组件
   ```js
   const AsyncComponent = () => ({
     component: import('./MyComponent.vue'),
     loading: LoadingComponent,
     error: ErrorComponent,
     delay: 200,
     timeout: 3000
   })
   ```

### 常见反模式

1. **避免在created/mounted中直接修改props**
2. **避免在template中使用过于复杂的表达式**
3. **避免使用$parent/$children直接访问组件实例**
4. **避免在computed属性中产生副作用**
5. **避免在watch中执行过于耗时的操作**

## 迁移与升级

### 向Vue 3迁移准备

1. 避免使用Vue.prototype扩展全局属性
2. 使用命名导出替代默认导出
3. 避免使用.sync修饰符
4. 使用新的slot语法(v2.6+支持)
5. 逐步引入组合式API(使用@vue/composition-api插件)

## 调试与测试

### 调试技巧

1. **使用Vue Devtools**: 查看组件树和Vuex状态
2. **使用this.$data查看组件数据**
3. **使用console.log(vm.$el)检查DOM**

### 单元测试

使用Vue Test Utils:
```js
import { mount } from '@vue/test-utils'
import Counter from '@/components/Counter'

describe('Counter', () => {
  test('increments count when button is clicked', async () => {
    const wrapper = mount(Counter)
    await wrapper.find('button').trigger('click')
    expect(wrapper.find('.count').text()).toBe('1')
  })
})
```

## 模板

### 基础组件模板

```vue
<template>
  <div class="component-container">
    <!-- 组件内容 -->
  </div>
</template>

<script>
export default {
  name: 'ComponentName',
  props: {
    // 组件属性
  },
  data() {
    return {
      // 组件数据
    }
  },
  computed: {
    // 计算属性
  },
  methods: {
    // 组件方法
  },
  created() {
    // 创建时执行
  },
  mounted() {
    // 挂载时执行
  }
}
</script>

<style scoped>
/* 组件样式 */
</style>
```

### Vuex模块模板
```js
// store/modules/auth.js
export default {
  namespaced: true,
  state: {
    user: null,
    token: null
  },
  mutations: {
    SET_USER(state, user) {
      state.user = user
    },
    SET_TOKEN(state, token) {
      state.token = token
    }
  },
  actions: {
    async login({ commit }, credentials) {
      // 调用API登录
      const { user, token } = await api.login(credentials)
      commit('SET_USER', user)
      commit('SET_TOKEN', token)
    },
    logout({ commit }) {
      commit('SET_USER', null)
      commit('SET_TOKEN', null)
    }
  },
  getters: {
    isAuthenticated: state => !!state.token
  }
}
```
```
