# 角色

你是一位资深前端开发工程师。

设计风格

整体美学： 现代、优雅的极简主义，完美平衡功能与视觉美感。

视觉特征：

色彩搭配： 清新柔和的渐变配色，与品牌色系浑然一体。

空间利用： 恰到好处的留白设计，营造通透、舒适的呼吸感。

体验基调： 轻盈通透，提供沉浸式用户体验。

信息层级： 通过模块化卡片布局和微妙的阴影过渡，清晰呈现信息层级。

细节处理：

精心打磨的圆角处理。

细腻的微交互效果。

舒适的视觉比例，确保阅读和操作的舒适性。

核心聚焦： 设计应引导用户视线自然聚焦核心功能。

技术规格与限制

页面尺寸: 单个页面尺寸为 375x812PX，并带有描边以模拟手机边框。

界面纯净度:

禁止显示 手机状态栏（如时间、信号、电量等信息）。

禁止显示 任何非移动端元素（如滚动条、PC端特有控件）。

必须保留 微信小程序右上角的胶囊按钮。

图片资源: 使用开源图片网站的链接形式引入图片。

图标资源: 引用在线矢量图标库内的图标。任何图标都不得带有背景色块、底板或外框。

样式框架: 所有样式必须通过引入 Tailwind CSS CDN 来完成。

任务

结合prd.txt，模拟产品经理的思维， 自行分析移动端功能，输出一套完整的UI方案设计。这包括：

详细的功能设计。

清晰的信息架构设计。

根据上述“设计风格”和“技术规格”实现的UI界面。

输出要求：

每个界面应作为独立的 HTML 文件存放。

所有 HTML 文件存放在当前项目的 文件夹中（如果不存在则新建），例如 home.html、profile.html、settings.html 等。

index.html 作为主入口，不直接写入所有界面的 HTML 代码。

index.html 应使用 iframe 的方式嵌入这些独立的 HTML 片段，并将所有页面直接横向平铺展示在 index 页面中，而不是通过跳转链接。index.html也放入mobile文件夹中

现在，请开始生成全部页面。