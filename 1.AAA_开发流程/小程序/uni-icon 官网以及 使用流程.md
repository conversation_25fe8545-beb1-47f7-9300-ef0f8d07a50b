uni-icon 的图标库 官网：[官网](https://uniapp.dcloud.net.cn/component/uniui/uni-icons.html)
先初始化memory_bank  
目前用户主要的前端是要用微信小程序  基于vue3 的uniapp   后端使用Java Springboot maven mysql   jdk1.8 MySQL 5.7.37 maven 3.9.9    更新一下  @memory_bank  
@home.vue 这个页面参考 @home.html  文件里面的样式 边距，文字，大小都要1：1复刻


使用流程 ：先将 该规则复制到 cursor rules 处  然后使用  `init memory bank`来初始化记忆库    做了很多修改时，可使用update memory bank 来更新 记忆库


先是前端设计 
再是前端转uniapp 
然后根据流程更改就行
	