  

你是一位资深的前端开发工程师，你现在正在使用uniappVue2.x语法开发一款小程序以及H5多端的语法来进行开发

请遵循开发规范全局的开发规划，样式，重复代码的封装减少余操作，需要去掉自带的tabbar

请勿引入其他站外的CDN以及CSS，请勿使用其他第三方的组件库

图标icon请使用uni-icon来进行昔换，我已导入安装了uni-icon组件

图片请使用占位文件方便我后续开发替换真实的图片

现在已有正常基本开发框架  @English_Learn_front  ，你可以先进行整体代码的阅读，请按模块进行开发，可以先放测试代码，项目运行正常了再进行代码开发

如果UI是全屏的样式页面的头部需要根据页面顶部的胶囊按钮对齐要兼容刘海屏跟普通屏，然后内容被挡住，如果可以用自带的那就用自带的

## Mock服务使用指南
在后端接口未完成前，请实现前端Mock数据：
- 使用uni.request的拦截器功能模拟接口响应
- 在utils目录创建mock.js文件存放模拟数据
- 针对不同页面准备对应的JSON数据结构
- 模拟网络延迟和各种状态码响应
- 添加开关在开发/生产环境间切换
- 确保Mock数据结构与后端API文档一致

现在我将发给你原型+ui，请按照这个UI1：1复刻，来进行开发       

客户要求先完成前端页面

现在开始开发

