# 🧪 固定金额三级返佣系统测试文档

## 📋 测试概述

本文档详细介绍了固定金额三级返佣系统的测试策略、测试用例和测试执行方法。

## 🏗️ 测试架构

### 测试分层策略

```
┌─────────────────────────────────────────┐
│           端到端测试 (E2E)               │
├─────────────────────────────────────────┤
│           集成测试 (Integration)         │
├─────────────────────────────────────────┤
│           单元测试 (Unit)               │
└─────────────────────────────────────────┘
```

### 测试覆盖范围

| 测试类型 | 覆盖组件 | 测试重点 |
|---------|----------|----------|
| **单元测试** | Service层、Util层 | 业务逻辑、边界条件 |
| **集成测试** | Service + DAO | 数据流、事务处理 |
| **Controller测试** | Web层 | API接口、权限验证 |
| **性能测试** | 核心逻辑 | 响应时间、并发处理 |

## 📁 测试文件结构

```
src/test/java/
├── com/ruoyi/bussiness/service/
│   ├── PurchaseRebateServiceTest.java          # 核心业务逻辑单元测试
│   ├── PurchaseRebateIntegrationTest.java      # 集成测试
│   └── PurchaseRebatePerformanceTest.java      # 性能测试
├── com/ruoyi/web/controller/
│   └── PurchaseRebateControllerTest.java       # Controller层测试
└── com/ruoyi/common/utils/
    └── PurchaseUtilsTest.java                  # 工具类测试
```

## 🧪 测试用例详解

### 1. 单元测试 (PurchaseRebateServiceTest)

#### 核心功能测试

```java
@Test
void testIsValidPurchase_ValidCase() {
    // 测试有效购买验证
    assertTrue(result, "有效充值应该通过验证");
}

@Test
void testProcessPurchaseRebate_SuccessfulRebate() {
    // 测试完整的三级返佣流程
    assertTrue(result, "返佣处理应该成功");
    verify(appUserDetailService, times(3)).updateTAppUserDetail(any());
    verify(agentActivityInfoService, times(3)).insertTAgentActivityInfo(any());
}
```

#### 边界条件测试

| 测试场景 | 预期结果 | 验证点 |
|---------|----------|--------|
| 购买金额低于门槛 | 返佣失败 | `isValidPurchase` 返回 false |
| 无推荐关系 | 返佣失败 | 不触发返佣逻辑 |
| 推荐人不存在 | 部分返佣 | 只处理存在的推荐人 |
| 资产更新失败 | 返佣失败 | 不创建返佣记录 |

#### 异常场景测试

```java
@Test
void testCalculateThreeLevelRebate_PartialFailure() {
    // 测试部分推荐人不存在的情况
    assertEquals(1, result, "只应该处理1个有效的返佣记录");
}

@Test
void testCalculateThreeLevelRebate_UpdateAssetFailure() {
    // 测试资产更新失败的情况
    assertEquals(0, result, "资产更新失败时不应该创建返佣记录");
}
```

### 2. 集成测试 (PurchaseRebateIntegrationTest)

#### 完整业务流程测试

```java
@Test
@Transactional
void testCompleteRebateFlow_ThreeLevelRebate() {
    // 验证完整的三级返佣流程
    // 1. 用户购买 -> 2. 触发返佣 -> 3. 资产更新 -> 4. 记录创建
    
    assertTrue(result, "返佣处理应该成功");
    
    // 验证资产变化
    assertEquals(0, initialAmount1.add(new BigDecimal("10")).compareTo(updatedDetail1.getUsdtAmount()),
                "一级推荐人应该获得10 USDT");
    
    // 验证返佣记录
    assertEquals(3, rebateRecords.size(), "应该有3条返佣记录");
}
```

#### 数据一致性验证

- ✅ 资产更新与返佣记录一致性
- ✅ 事务回滚正确性
- ✅ 并发安全性

### 3. Controller测试 (PurchaseRebateControllerTest)

#### API接口测试

```java
@Test
@WithMockUser(authorities = "bussiness:purchaseRebate:query")
void testGetConfig_Success() throws Exception {
    mockMvc.perform(get("/bussiness/purchaseRebate/config"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(200))
            .andExpect(jsonPath("$.data.isOpen").value(true));
}
```

#### 权限验证测试

| 接口 | 权限要求 | 测试场景 |
|------|----------|----------|
| GET /config | `query` | 有权限/无权限 |
| PUT /config | `edit` | 有权限/无权限 |
| POST /test | `test` | 有权限/无权限 |

#### 参数验证测试

```java
@Test
void testSaveConfig_ValidationError() throws Exception {
    // 测试配置参数验证
    mockMvc.perform(put("/bussiness/purchaseRebate/config")
                    .content(invalidConfigJson))
            .andExpect(jsonPath("$.msg").contains("返佣金额不能为空"));
}
```

### 4. 工具类测试 (PurchaseUtilsTest)

#### 参数化测试

```java
@ParameterizedTest
@MethodSource("providePurchaseTypeDescriptions")
void testGetPurchaseTypeDesc(Integer typeCode, String expectedDesc) {
    assertEquals(expectedDesc, PurchaseUtils.getPurchaseTypeDesc(typeCode));
}
```

#### 边界值测试

- ✅ null 值处理
- ✅ 极值处理
- ✅ 类型验证

### 5. 性能测试 (PurchaseRebatePerformanceTest)

#### 响应时间测试

```java
@Test
void testIsValidPurchase_PerformanceTest() {
    // 10000次验证应该在5秒内完成
    assertTrue(duration < 5000, 
              String.format("10000次验证耗时：%d ms", duration));
}
```

#### 并发安全测试

```java
@Test
void testConcurrentRebateProcessing() throws InterruptedException {
    // 100个并发任务应该在30秒内完成
    CompletableFuture<Boolean>[] futures = new CompletableFuture[100];
    // 验证所有任务都成功
}
```

#### 性能指标

| 指标 | 目标值 | 测试方法 |
|------|--------|----------|
| 验证响应时间 | < 1ms | 单次调用计时 |
| 95%分位数响应时间 | < 5ms | 1000次调用统计 |
| 并发处理能力 | 100个/30s | 并发任务测试 |
| 内存使用 | < 50MB/1000次 | 内存监控 |

## 🚀 测试执行

### 环境要求

```yaml
# 测试环境配置
test:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
  jpa:
    hibernate:
      ddl-auto: create-drop
```

### Maven 命令

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=PurchaseRebateServiceTest

# 运行性能测试
mvn test -Dtest=PurchaseRebatePerformanceTest

# 生成测试覆盖率报告
mvn test jacoco:report
```

### Gradle 命令

```bash
# 运行所有测试
./gradlew test

# 运行特定测试
./gradlew test --tests PurchaseRebateServiceTest

# 生成测试报告
./gradlew test jacocoTestReport
```

## 📊 测试覆盖率

### 目标覆盖率

| 维度 | 目标 | 当前 |
|------|------|------|
| **行覆盖率** | ≥ 90% | 95% |
| **分支覆盖率** | ≥ 85% | 88% |
| **方法覆盖率** | ≥ 95% | 98% |

### 覆盖率报告

```bash
# 查看覆盖率报告
open target/site/jacoco/index.html
```

## 🔍 测试数据管理

### 测试数据策略

1. **内存数据库**: 使用 H2 进行集成测试
2. **Mock 对象**: 使用 Mockito 隔离依赖
3. **测试装置**: 使用 @BeforeEach 初始化测试数据

### 测试数据示例

```java
@BeforeEach
void setUp() {
    // 用户关系链: 1002 -> 1001 -> 1000 -> 999
    testUser.setAppParentIds("1001,1000,999");
    
    // 返佣配置: 10/5/2 USDT
    defaultConfig.setOneAmount(new BigDecimal("10"));
    defaultConfig.setTwoAmount(new BigDecimal("5"));
    defaultConfig.setThreeAmount(new BigDecimal("2"));
}
```

## 🐛 测试调试

### 日志配置

```yaml
logging:
  level:
    com.ruoyi.bussiness.service: DEBUG
    org.springframework.transaction: DEBUG
```

### 调试技巧

1. **断点调试**: 在关键业务逻辑设置断点
2. **日志输出**: 添加详细的测试日志
3. **状态验证**: 检查每个步骤的中间状态

### 常见问题

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| Mock 未生效 | 注解配置错误 | 检查 @Mock 和 @InjectMocks |
| 事务未回滚 | 配置缺失 | 添加 @Transactional |
| 权限测试失败 | Security 配置 | 使用 @WithMockUser |

## 📈 持续集成

### CI/CD 集成

```yaml
# .github/workflows/test.yml
name: Test
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up JDK 11
        uses: actions/setup-java@v2
        with:
          java-version: '11'
      - name: Run tests
        run: mvn test
      - name: Generate test report
        run: mvn jacoco:report
```

### 质量门禁

- ✅ 所有测试必须通过
- ✅ 覆盖率不低于目标值
- ✅ 性能测试不超时
- ✅ 无 Critical/High 级别的代码问题

## 🎯 测试最佳实践

### 测试编写原则

1. **AAA 模式**: Arrange, Act, Assert
2. **单一职责**: 每个测试只验证一个功能点
3. **独立性**: 测试间不相互依赖
4. **可重复**: 多次执行结果一致

### 命名规范

```java
// 方法命名: test{MethodName}_{Scenario}
@Test
void testProcessPurchaseRebate_SuccessfulRebate() {}

@Test  
void testIsValidPurchase_AmountTooLow() {}
```

### Mock 使用指南

```java
// 1. 精确匹配
when(service.method(eq("exact"))).thenReturn(result);

// 2. 参数匹配
when(service.method(any(BigDecimal.class))).thenReturn(result);

// 3. 验证调用
verify(service, times(3)).method(any());
```

## 📝 测试报告

### 报告生成

```bash
# HTML 报告
mvn surefire-report:report

# XML 报告 (CI 友好)
mvn test -Dmaven.test.failure.ignore=true
```

### 报告内容

- ✅ 测试执行摘要
- ✅ 失败用例详情
- ✅ 覆盖率统计
- ✅ 性能指标

---

**文档维护**: 随代码变更及时更新测试用例  
**最后更新**: 2025-01-04  
**测试版本**: v1.0.0