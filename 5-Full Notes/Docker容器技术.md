---
created: 2024-11-18
aliases: []
---

状态: [[进行中]]

标签: [[Docker]] [[容器化]] [[项目部署]] [[DevOps]] [[微服务]] [[Linux]] [[MySQL]] [[Tomcat]] [[网络配置]]

# Docker

正文内容:
# 应用程序部署方式的演进
## 传统的部署方式
### 开发人员交付一个安装包（exe、msi、tar、war），测试/运维直接在物理机上部署。
### 缺点
#### 安装包对运行环境有依赖，如果测试环境、生产环境跟开发环境不一样，部署起来有各种问题
#### 计算机资源很难合理分配，程序之间会互相影响。
## 虚拟化部署方式
### 在一台物理机上运行多个虚拟机，每个应用90%行在一个虚拟机上。
### 解决了应用程序互相影响的问题，但是缺点也很明显
#### 每个虚拟机上部署了操作系统，操作系统占用资源。
#### 虚拟机比较多的情况下，操作系统的维护，也比较耗费精力。
## 容器化部署方式
### 共享了操作系统。一台虚拟机上可以运行多个容器，每个容器中运行一个应用程序。
#### 优点：
#### 应用程序之间互相隔离，互不影响。
#### 容器对系统资源的需求远远低于虚拟机。
#### 容器中运行了应用程序以及它依赖的环境。确保在任何环境下，都能正常部署起来。
### 开发人员交付：镜像。
# Docker是什么
## docker是一个工具/软件、用来创建、管理、编排容器的。docker可以运行在Linux，Windows，Mac上。
## CE 社区版
## EE企业版
# docker 安装的两种方式
## 1. 导入ova文件，选择安装位置
## 2.自行下载安装
### 安装步骤
#### 在本地D:\Environment\Linux\Docker路经下的安装docker.txt文件查看步骤
# 检查docker环境
## docker version 查看版本
## systemctl status docker 查看docker运行状态
## systemctl start/stop/restart docker 启动/停止/重启 docker
## ps -ef | grep docker 查看docker进程
# docker中的三个核心概念
## 仓库Repository
### 类似于手机的应用市场：存放镜像文件。分为公有仓库和私有仓库
## 镜像image（安装包）
### 包含了应用程序以及依赖的一个文件。通过它可以创建容器
### docker images查看安装了那些镜像
### docker pull  mysql:5.7下载特定版本的镜像文件
### docker pull mysql 不加tag默认下载的是latest，最新版本。
### docker search mysql 搜索镜像  
### docker inspect mysql:5.5 查看镜像详细信息  
### docker inspect d404d78aa797 查看镜像详细信息      docker rmi 镜像名称:tag/镜像ID

# 容器（container）
## 通过镜像创建容器  
## 容器常用操作：启动、停止、重启、删除  
## 运行了应用程序以及依赖的环境，可以理解成一台小型虚拟机，简易版Linux系统+应用程序。
# 数据挂载
## 容器删除后，容器产生的数据丢失，希望容器删除后，数据仍旧保留，可以使用数据挂载实现
## 多个容器共享数据，可以使用数据挂载实现
###   实现数据挂载 docker run -id --name abc -p 88:80 -v=nginx_abc:/usr/share/nginx/html/ nginx:1.20.2
# mysql 挂载
## docker run -id --name mabc -p 3307:3306 -e MYSQL_ROOT_PASSWORD=123456 mysql:5.7 创建了一个容器
## 容器的其他操作
### docker  logs   容器名/容器ID     查看日志
### docker  top  容器名/容器ID       查看容器中运行的进程
### docker  stats       查看容器的统计信息
### docker   kill  容器名/容器ID
# 基于Dockerfile 创建镜像
## docker build -t centos_jdk:1.0 -f Dockerfile .          创建镜像
## docker   images    检查创建好的镜像
## docker run -id --name ddd centos_jdk:1.0   使用创建好的镜像创建容器
## docker exec -it ddd /bin/bash 进入容器
## exit 退出
# 容器搭建租车系统
## 1.为了方便定位问题，先把之前创建的容器全部删除掉，在一个相对干净的环境中搭建该系统。
## 2.自定义网络
### 两个容器放到同一个网络中 docker network create carrental
## 3.创建mysql容器
### 容器名字为：car_mysql，如下的命令设置了数据库的名字为carRental，数据库的密码为123456，映射了Linux系统的4444端口，通过**************:4444可以查看该数据库
### docker run -di  --name car_mysql  --net carrental   -v=mysql_vol:/var/lib/mysql   -e MYSQL_ROOT_PASSWORD=123456 -p 4444:3306 -e MYSQL_DATABASE=carRental  mysql:5.7  --character-set-server=utf8   

## 4.创建tomat容器
### 容器名字为：car_tom，映射Linux系统的8089端口，通过**************:8089/carRental最终访问租车系统
### docker run -id --name car_tom -p  8089:8080  --net carrental  -v=mytom_vol:/usr/local/tomcat/webapps   tomcat:8.5.54 
### 创建容器时，检查是否有IPV4转发的告警，如果有如下的告警，会导致网络不通，使用浏览器访问时访问不到。  
### WARNING: IPv4 forwarding is disabled. Networking will not work.
### 解决办法：
### 编辑文件
### vi  /etc/sysctl.conf
### 配置转发
### net.ipv4.ip_forward=1
### 保存并退出
### :wq
### 重启服务，让配置生效
### systemctl restart network
### 查看是否成功，如果返回为"net.ipv4.ip_forward = 1"则表示成功
### sysctl  net.ipv4.ip_forward
## 5.部署carRental应用
### (1). 将carRental.war包上传到 /var/lib/docker/volumes/mytom_vol/_ data目录下，系统会自动解压缩，并同步到容器中，修改/var/lib/docker/volumes/mytom_vol/  _ data/carRental/WEB-INF/classes目录下的db.properties文件，修改为：
### jdbc.user=root
### jdbc.password=123456
### url=******************************************************************************************************************************
### jdbc.driverClass=com.mysql.cj.jdbc.Driver
### 修改蓝色部分的内容即可。蓝色框起来的内容是虚拟机的IP地址，以及创建mysql容器时映射的虚拟机的端口。
### (2). 使用sqlyog连接**************:4444，导入初始的数据文件：initCarRental.sql
### (3). 配置上传车辆图片的路径
### 修改/var/lib/docker/volumes/mytom_vol/_ data/carRental/WEB-INF/classes/file.properties文件中的path
### cd   /var/lib/docker/volumes/mytom_vol/_ data/carRental/WEB-INF/classes/  
### vi    file.properties
### // 按insert进入编辑模式，将path修改为如下的值g
### path=/usr/local/tomcat/webapps/upload/
### // 检查结果
### cat   file.properties
### // 创建目录
### mkdir  -p  /var/lib/docker/volumes/mytom_vol/ _ data/upload/images
### / /  将defaultcarimage.jpg 上传到/var/lib/docker/volumes/mytom_vol/_ data/upload/images路径下
### (4)重启car_tom容器
### docker  restart  car_tom  
## 6.使用系统
### 浏览器中输入
### http://***************:8089/carRental，进入登录页面，用户名：admin，密码：123456
### 登录系统后，进入基础管理->车辆管理，点击增加，能显示出来车辆图片，说明部署正确。

# 参考资料
- Docker官方文档
- 容器化部署指南
- Docker命令参考
- MySQL容器配置手册
- Tomcat部署文档

# 相关笔记
- [[Docker]]
- [[项目部署]]
- [[容器化]]
- [[租车系统]]
- [[MySQL]]
- [[Tomcat]]
- [[Linux]]
- [[网络配置]]

