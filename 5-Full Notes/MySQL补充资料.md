---
created: 2024-12-28 10:17
aliases:
---

状态:

标签:

# mysql基础-余胜军
## SQL语句的概述
1. SQL 是用于访问和处理数据库的标准的计算机语言
2. SQL指结构化查询语言，全称是Structured Query Language
3. SQL可以访问和处理数据库
4. SQL是一种ANSI（America National Standard Institute 美国国家标准化组织）标准的计算机语言
5. SQL编程
## SQL语句特点
1. 具有综合统一性，不同数据库的支持的sql稍有不同，  为许多任务提供了统一的命令，这样方便用户的学习和使用。
2. 非过程化语言
3. 语言简洁，用户较为容易接受 select delete drop 
4. 集合性
## 数据库的分类
![[Pasted image 20241228105908.png]]
数据库应用场景 以后的开发的数据基本都会放到服务器端的数据库当中
## 分类：
A: 关系型数据库(RDBMS)
1. Oracle数据库（甲骨文公司）
2. MySQL数据库 （免费）
3. SQL Server数据库（微软开发） C#
4. SQLite（嵌入型数据库）学习、安卓开发
5. db2
   磁盘数据库 I/O 效率较低
B: 非关系型数据库（NoSQL)
1. Redis（缓存数据库）
2. MongoDB (文档型数据库)
3. Elasticsearch（搜索服务）
4. Hbase (分布式、列式数据库)
## SQL与数据库的关系
1. SQL是一种用于操作数据的语言，SQL适用于所有关系型数据库
2. MySQL、Oracle、SQL Server、DB2是一个数据库软件，这些数据库软件支持标准SQL，也就是通过SQL可以使用这些软件，不过每个数据库系统会在标准SQL的基础上扩展自己的SQL语法，大部分的
3. NoSQL数据库有自己的操作语言，对SQL支持的并不好。
SQL 属于 数据库编程语言数据库 MySQL、Oracle、SQLServer、DB2 数据库

SQL
![[Pasted image 20241228111900.png]]

NoSQL
![[Pasted image 20241228112022.png]]
## MySQL简介
### 简介:
1. MySQL数据库管理系统由瑞典的DataKonsultAB公司研发，该公司被Sun公司收购，现在Sun公司又被Oracle公司收购，因此MySQL目前属于Oracle 旗下产品。
2. MySQL所使用的SQL语言是用于访问数据库的最常用标准化语言。MySQL软件采用了双授权政策分为社区版（免费版本）和商业版（收费），由于其体积小、速度快、总体拥有成本低，一般中小型站的开发都选择MySQL作为网站数据库。
### 特点：
1. MySQL数据库是用C和C++语言编写的，以保证源码的可移植性
2. 支持多个操作系统例如：Windows、Linux、Mac OS等等
3. 支持多线程，可以充分的利用CPU资源
4. 为多种编程语言提供APl，包括C语言，Java，PHP。Python语言等
5. MySQL优化了SQL算法，有效的提高了查询速度
6. MySQL开放源代码且无版权制约，自主性强、使用成本低。
7. MySQL历史悠久、社区及用户非常活跃，遇到问题，可以很快获取到帮助。
数据库的安装及出错已经跳过

## 数据库基本操作
### DDL 
数据库模式定义语言DDL(Data Definition Language)，是用于描述数据库中要存储的现实世界实体的语言
1. 对数据库创建、删除、修改等操作
2. 对表结构创建、删除、修改等操作

![[Pasted image 20241228113103.png]]

ddl对表的结构进行增加、删除、修改操作
dml对表中的数据进行增删改

数据库操作

# 参考资料
# 相关笔记