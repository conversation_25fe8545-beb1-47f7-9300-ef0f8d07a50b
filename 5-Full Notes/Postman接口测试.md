---
created: 2024-12-11
aliases: []
---

状态: [[进行中]]

标签: [[Postman]] [[接口测试]] [[API测试]] [[自动化测试]]

# Postman
- 发送请求，校验结果
-  接口之间的关联
	- 接口1:返回数据，查询用户列表，收到响应后，提取响应中的数据，放到全局变量
	- 接口2:使用接口1返回的数据，添加项目，memberid 是用户已经注册的用户id，使用查询用户列表中的数据。
- 几类变量
	- 环境变量：在测试环境部署在不同的电脑上，每次执行切换测试环境，使用环境变量，可以快速切换环境
	- 全局变量：在全局中生效
		- 注册：手机号，密码
		- 登录：手机号，密码
		- 多个接口中都使用的数据，可以放倒全局变量中
	- 本地变量：局部变量，在当前接口中使用
	- 设置方法
		- pre-req 请求前设置
		- test 请求后设置
	- 使用变量： {{变量名}}
	- 数据驱动：
		- 接口的测试数据写到文件中（csv,json)
		- 接口只用保留一个
 
# 参考资料
- Postman官方文档
- API测试指南
- 接口测试实践
- 自动化测试手册

# 相关笔记
- [[接口测试]]
- [[API测试]]
- [[自动化测试]]
- [[HTTP]]
- [[数据驱动测试]]
 