---
created: 2024-12-23 09:07
aliases:
---

状态:

标签:

# jmeter
## 配置jmeter中文界面
### bin\jmeter.properties文件最后增加一句: language = zh_CN
## 运行jmeter
### 双击jmeter.bat文件
## jmeter 介绍
### 基于Java开发的，开源，免费的工具
### 可以做接口测试
### 可以做性能测试
### 支持的协议多：http、ftp、soap
## jmeter核心组件（8大元件）
### 取样器：模拟我们的业务，比如创建一个http取样器
### 逻辑控制器：控制取样器执行次数、执行顺序。
#### 循环控制器
#### 仅一次控制器
#### if控制器
#### foreach控制器
### 前置处理器：取样器前执行，准备环境、准备数据
### 后置处理器：取样器后执行，可以提取参数、处理服务器的返回结果等
### 断言：取样器之后执行。校验取样器的结果是否正确
### 配置元件：取样器后执行，准备环境，准备参数，准备数据
#### http信息头管理器
#### cookie管理器
#### JDBC连接管理
#### csv data set config csv 文件获取
### 监听器：取样器后执行，查看结果的
#### 用表格查看结果
#### 查看结果树
#### 汇总报告
#### 聚合报告
### 定时器：取样器之前执行，sleep(n)取样器之间增加间隔时间
## 发送请求，检查结果
### get 请求
#### 拼接在url之后
![[Pasted image 20241223190628.png]]
#### 参数传参
![[Pasted image 20241223190753.png]]
### post 请求
#### application/x-www-from-urlencoded:表单 ： key=value&key=value
##### 使用参数传参
![[Pasted image 20241223191003.png]]
##### 使用消息体数据传参，默认是Connect-Type;text/plain;charset=UTF-8，需要添加信息头管理器，设置content-type
![[Pasted image 20241223191341.png]]
![[Pasted image 20241223191357.png]]
#### application/json ：json格式 {key:value, key:value}
##### 使用消息体传参
![[Pasted image 20241223192732.png]]
#### multipart/form-data：上传文件
##### 使用文件上传
![[Pasted image 20241223192811.png]]
### 断言
#### 响应断言
##### 校验状态码为200
![[Pasted image 20241223192918.png]]
##### 校验响应体包含某个字符串
#### json断言
##### 校验路径存在，且值正确
![[Pasted image 20241223193011.png]]
## jmeter 参数化的几种方式，使用变量：${变量名}
### 用户定义的变量：跟线程无关的数据，环境ip地址、端口、url的前面部分
#### 测试计划：对所有用户用户线程生效、也可对某个线程生效
#### 配置元件：-->用户定义的变量：比较灵活，可以对所有线程组生效，也可以对单个线程组生效
![[Pasted image 20241223194206.png]]
### 用户参数：跟线程有关的数据，（一个线程表示一个用户），不同用户使用不同数据进行测试
#### 前置处理器->用户参数
![[Pasted image 20241223194320.png]]
![[Pasted image 20241223194331.png]]
![[Pasted image 20241223194352.png]]
### 文件参数：测试数据写入到csv文件
#### 准备数据文件：保存到recharge-data.csv中
![[Pasted image 20241223194513.png]]
### 配置元件->CSV data set config
![[Pasted image 20241223194538.png]]
### 内置函数：随机数、随机字符串、从一组变量中随机一个‘
#### 预置2000辆车
#### 函数助手->添加函数
### 数据库：从数据库中读取数据
#### 将mysql-connector-java-5.1.42.jar 放到jemter/lib/ext 目录下，放置好后重启jemter生效
#### 使用场景：
##### 并发登录的场景，1000个用户并发登录，1000个系统已注册的账户
##### 并发充值的场景，给数据库中所有用户充值
#### 创建一个连接
![[Pasted image 20241224190729.png]]
#### 创建一个JDBC（Java Database Connection）取样器，查询数据
![[Pasted image 20241224190800.png]]
#### 查询出来的数量：$ { phone _ #}，第一个：${phone_1} 第十个：${phone_10} ${phone_100}
![[Pasted image 20241224193520.png]]
#### 查询多列数据
##### 循环控制器+计数器，遍历多个变量
##### 变量嵌套变量，要V函数处理。$ { __ V(name_ $ {i},) }
## 参数关联：
### 从接口1中的结果中提取数据，作为接口2的参数使用。postman、jmeter、接口1提取的数据放到变量中，接口2使用变量。
### 线程组内的参数关联：接口1，接口2不在同一个线程组，全局变量
#### 多线程组并发执行
![[Pasted image 20241224193923.png]]
#### 通过后置处理器提取的数据，是局部变量，只在当前线程组生效
## 定时器：取样器后执行
### 思考时间：请求与请求之间的等待时间。打开百度首页，百度搜索，不同用户有不同的等待时间。
### 固定定时器：每个用户等待时间相同
![[Pasted image 20241224194255.png]]
### 高斯随机定时器：每个用户等待时间不相同
![[Pasted image 20241224195203.png]]
## 集合点
JMeter 中的集合点（JSR223 PreProcessor, JSR223 PostProcessor, JSR223 Sampler等）是一种特殊的元件，它们允许你在测试计划中嵌入脚本代码。集合点元件可以用于多种目的，包括但不限于：

1. **数据预处理**：在请求发送之前，你可以使用集合点来修改或生成测试数据。

2. **响应后处理**：在请求发送后，你可以使用集合点来处理服务器的响应数据。

3. **条件逻辑**：你可以在集合点中编写条件逻辑，根据条件执行不同的脚本。

4. **变量设置**：在发送请求之前或之后，你可以设置或修改JMeter变量的值。

5. **自定义逻辑**：你可以编写自定义的Java代码来扩展JMeter的功能。

6. **错误处理**：你可以在集合点中添加错误处理逻辑，以应对测试过程中可能出现的异常情况。

7. **调试**：集合点可以用来输出调试信息，帮助开发者理解测试的执行流程。

JMeter支持多种脚本语言，包括Groovy, BeanShell, Jexl, JavaScript等，你可以根据需要选择合适的语言来编写脚本。集合点元件的使用可以让你的测试计划更加灵活和强大。


## 练习
- 练习1  
    - 租车添加车辆接口，使用Jmeter测试一下，校验结果  
    - 金融添加项目接口，使用Jmeter测试一下，校验结果  
    - [https://api.ahfi.cn/](https://api.ahfi.cn/)  
        - 农历信息接口 https://api.ahfi.cn/doc/lunarinfo.html  
        - 密码生成器 https://api.ahfi.cn/doc/pswgenerato.html  
        - 祖师爷经典语录 https://api.ahfi.cn/doc/zsyjdyl.html  
        - 获取系统当前时间 https://api.ahfi.cn/doc/currenttime.html  
- 练习2  
    - 租车添加客户接口练习  
        - 用户参数  
        - 文件参数  
        - 内置函数  
- 练习3  
    - 从数据库中查询已有的车，车牌号查出来。一个个将车辆删除。验证单个车辆删除接口。
# 参考资料

# 相关笔记