# Java基础

## Day01

### 1. 基础语法（Demo01）
#### 注释
- 单行注释 //
  - 用于简单说明
  - 只注释一行
- 多行注释 /* */
  - 可以跨多行
  - 不能嵌套使用
- 文档注释 /** */
  - 用于生成API文档
  - 可以添加@author等标记

#### 关键字
- public：公共的访问修饰符
- class：定义类
- static：静态修饰符
- void：空返回值
- 所有关键字都是小写
- 不能用作标识符

#### 标识符
- 命名规则
  - 由字母、数字、_、$组成
  - 不能以数字开头
  - 不能是关键字
- 命名规范
  - 类名：首字母大写，驼峰命名
  - 变量名：首字母小写，驼峰命名
  - 方法名：首字母小写，驼峰命名
  - 常量名：全部大写，下划线分隔

### 2. 变量（Demo02）
#### 变量定义
- 格式：数据类型 变量名 = 初始值;
- 先声明后使用
- 作用域在{}内

#### 基本数据类型
- 整数型
  - byte：1字节，-128~127
  - short：2字节，-32768~32767
  - int：4字节（默认），-2^31~2^31-1
  - long：8字节，-2^63~2^63-1，需要加L

- 浮点型
  - float：4字节，需要加F
  - double：8字节（默认）
  - 精度：double > float

- 字符型
  - char：2字节
  - 存储单个字符
  - 使用单引号
  - 可以存储ASCII码

- 布尔型
  - boolean：1字节
  - 只有true和false两个值

### 3. 数据类型转换（Demo03）
#### 自动类型转换
- 转换规则
  - byte → short → int → long → float → double
  - char → int
- 小范围向大范围转换
- 不会损失精度
- 系统自动完成

#### 强制类型转换
- 语法：(目标类型)被转换数据
- 大范围向小范围转换
- 可能损失精度
- 可能发生溢出
- 需要手动完成

#### 特殊情况
- byte/short/char运算时自动提升为int
- 不同类型运算，结果为最大类型
- boolean类型不能转换为其他类型

### 4. 运算符（Demo04）
#### 算术运算符
- 基本运算：+, -, *, /, %
  - +：加法，字符串连接
  - -：减法
  - *：乘法
  - /：除法（整数相除结果取整）
  - %：取余
- 自增自减：++, --
  - 前缀：++a 先自增再使用
  - 后缀：a++ 先使用再自增

#### 赋值运算符
- 基本赋值：=
- 复合赋值：+=, -=, *=, /=, %=
  - a += b 等价于 a = a + b
  - 包含了强制类型转换

#### 关系运算符
- 大小比较：>, <, >=, <=
- 相等比较：==, !=
- 结果都是boolean类型

#### 逻辑运算符
- &&：与（短路）
  - 第一个为false就不再判断第二个
- ||：或（短路）
  - 第一个为true就不再判断第二个
- !：非
- 结果都是boolean类型

#### 位运算符
- 左移运算符 <<
  - 每左移1位相当于乘以2
- 右移运算符 >>
  - 每右移1位相当于除以2

#### 三元运算符
- 格式：条件 ? 表达式1 : 表达式2
- 条件为true取表达式1
- 条件为false取表达式2

## Day02

### 1. 一维数组（Demo01）
#### 数组定义
- 声明方式
  - 数据类型[] 数组名
  - 数据类型 数组名[]
- 创建方式
  - new 数据类型[长度]
  - {元素1, 元素2, ...}
- 初始化方式
  - 动态初始化：指定长度
  - 静态初始化：指定元素

#### 数组访问
- 索引访问
  - 数组名[索引]
  - 索引从0开始
  - 范围：0 ~ length-1
- 属性访问
  - 数组名.length
  - 获取数组长度
  - 长度是固定的

#### 数组遍历
- 普通for循环
  - for(int i = 0; i < arr.length; i++)
  - 可以获取索引
  - 可以修改元素
- 增强for循环
  - for(元素类型 变量名 : 数组名)
  - 简化数组遍历
  - 只能获取元素

### 2. 二维数组（Demo02）
#### 数组定义
- 声明方式
  - 数据类型[][] 数组名
  - 数据类型 数组名[][]
- 创建方式
  - new 数据类型[行数][列数]
  - {{元素1,元素2},{元素3,元素4}}
- 初始化方式
  - 动态初始化：指定行列数
  - 静态初始化：指定元素值

#### 数组访问
- 元素访问
  - 数组名[行索引][列索引]
  - 两个索引都从0开始
- 属性访问
  - 行数：数组名.length
  - 列数：数组名[i].length

#### 数组遍历
- 嵌套for循环
  - 外循环控制行
  - 内循环控制列
- 增强for循环
  - 外层遍历行数组
  - 内层遍历列元素

### 3. 数组算法（Demo03）
#### 基础算法
- 最值查找
  - 定义变量记录最值
  - 遍历比较更新
- 数组反转
  - 首尾交换
  - 循环length/2次

#### 排序算法
- 冒泡排序
  - 相邻元素比较交换
  - 每轮将最大值冒泡到末尾
- 选择排序
  - 每轮选择最小值
  - 与首位置交换

## Day03

### 1. 类和对象（Demo01）
#### 类的定义
- 类的组成
  - 成员变量：属性
  - 成员方法：行为
  - 构造方法：初始化
- 访问修饰符
  - public：公共访问
  - private：私有访问
  - protected：保护访问
  - default：默认访问

#### 对象的创建
- new关键字
  - 分配内存空间
  - 调用构造方法
  - 返回对象引用
- 构造方法
  - 与类名相同
  - 无返回值
  - 可以重载

#### 对象的使用
- 访问成员变量
  - 对象名.变量名
  - 遵循访问权限
- 调用成员方法
  - 对象名.方法名()
  - 传递必要参数

### 2. 封装（Demo02）
#### 概念理解
- 隐藏实现细节
- 提供公共访问方式
- 提高代码安全性

#### 实现方式
- private关键字
  - 私有成员变量
  - 私有成员方法
- getter/setter方法
  - 获取私有成员变量
  - 设置私有成员变量

### 3. 构造方法（Demo03）
#### 基本特征
- 方法名与类名相同
- 无返回值声明
- 创建对象时调用

#### 注意事项
- 默认构造方法
  - 系统自动提供
  - 无参构造
- 构造方法重载
  - 参数个数不同
  - 参数类型不同

## Day04

### 1. 继承（Demo01）
#### 基本概念
- 使用extends关键字实现
  ```java
  class Animal {
      protected String name;
      protected int age;
      
      public void eat() {
          System.out.println(name + "正在吃东西");
      }
  }

  class Cat extends Animal {
      private String color;
      
      public void catchMouse() {
          System.out.println(name + "正在抓老鼠");
      }
  }
  ```
- 只支持单继承，但可以多层继承
  - Java中一个类只能有一个直接父类
  - 可以 A extends B, B extends C
  - 不可以 A extends B, C
- 子类继承父类的特点
  - 可以继承父类的非私有属性和方法
  - 不能继承父类的构造方法
  - 可以扩展自己的属性和方法

#### 继承中的构造方法
- 默认super()调用
  ```java
  class Animal {
      public Animal() {
          System.out.println("Animal构造方法");
      }
  }
  
  class Cat extends Animal {
      public Cat() {
          // 默认隐含super();
          System.out.println("Cat构造方法");
      }
  }
  ```
- 显式super调用
  ```java
  class Animal {
      protected String name;
      public Animal(String name) {
          this.name = name;
      }
  }
  
  class Cat extends Animal {
      private String color;
      public Cat(String name, String color) {
          super(name);  // 必须在第一行
          this.color = color;
      }
  }
  ```

#### 重载与重写
##### 重载（OverLoad）
- 发生在同一个类中
- 方法名相同，参数列表不同
  ```java
  class Calculator {
      public int add(int a, int b) {
          return a + b;
      }
      
      public double add(double a, double b) {
          return a + b;
      }
      
      public int add(int a, int b, int c) {
          return a + b + c;
      }
  }
  ```
- 重载的好处
  - 方法名相同，便于记忆
  - 实现功能类似，但参数不同的方法
  - 提高代码的复用性

##### 重写（Override）
- 发生在子类中
- 方法签名必须完全相同
  ```java
  class Animal {
      public void makeSound() {
          System.out.println("动物发出声音");
      }
  }
  
  class Cat extends Animal {
      @Override  // 重写注解，帮助检查是否是正确的重写
      public void makeSound() {
          System.out.println("喵喵喵");
      }
  }
  ```
- 重写注意事项
  - 访问权限不能更严格（public > protected > default > private）
  - 返回值类型可以是父类方法返回值的子类型
  - 不能抛出比父类方法更多的异常

### 2. 多态（Demo02）
#### 概念详解
- 对象的多种形态
  ```java
  Animal animal1 = new Animal(); // 父类引用指向父类对象
  Animal animal2 = new Cat();    // 父类引用指向子类对象
  Cat cat = new Cat();           // 子类引用指向子类对象
  ```
- 多态的前提条件
  1. 必须有继承关系
  2. 必须有方法重写
  3. 必须有父类引用指向子类对象

#### 多态的实际应用
```java
class Pet {
    private String name;
    private int health;
    
    public void treatment() {
        System.out.println("宠物看病");
    }
}

class Dog extends Pet {
    @Override
    public void treatment() {
        System.out.println("狗狗打针、吃药");
    }
}

class Cat extends Pet {
    @Override
    public void treatment() {
        System.out.println("猫咪打针、吃药、吸氧");
    }
}

// 宠物医院类
class Hospital {
    // 使用多态简化代码，统一处理不同宠物
    public void treat(Pet pet) {
        pet.treatment();
    }
}
```

#### 向上转型与向下转型
```java
// 向上转型（自动转型）
Animal animal = new Cat();

// 向下转型（强制转型）
if (animal instanceof Cat) {
    Cat cat = (Cat) animal;
    cat.catchMouse();  // 调用子类特有方法
}
```

### 3. 抽象类（Demo03）
#### 详细特点
- 使用abstract关键字修饰
  ```java
  public abstract class Shape {
      protected String color;
      
      // 抽象方法
      public abstract double getArea();
      
      // 具体方法
      public void setColor(String color) {
          this.color = color;
      }
  }
  ```
- 抽象类的实现
  ```java
  class Circle extends Shape {
      private double radius;
      
      @Override
      public double getArea() {
          return Math.PI * radius * radius;
      }
  }
  
  class Rectangle extends Shape {
      private double width;
      private double height;
      
      @Override
      public double getArea() {
          return width * height;
      }
  }
  ```

#### 抽象类的应用场景
1. 模板方法模式
```java
public abstract class DataMiner {
    // 模板方法
    public final void mine() {
        openFile();
        extractData();
        parseData();
        analyzeData();
        sendReport();
        closeFile();
    }
    
    abstract void openFile();
    abstract void extractData();
    
    // 具体方法
    void parseData() {
        System.out.println("解析数据");
    }
    
    void analyzeData() {
        System.out.println("分析数据");
    }
    
    void sendReport() {
        System.out.println("发送报告");
    }
    
    void closeFile() {
        System.out.println("关闭文件");
    }
}
```

2. 通用基类
```java
public abstract class BaseDao {
    // 通用数据库操作方法
    public abstract void insert(Object obj);
    public abstract void update(Object obj);
    public abstract void delete(Object obj);
    public abstract Object select(int id);
    
    // 共用的工具方法
    protected void printLog(String message) {
        System.out.println("日志：" + message);
    }
}
```

### 4. 接口（Demo04）
#### 接口的详细说明
- 接口定义
  ```java
  public interface Flyable {
      // 常量
      int MAX_SPEED = 100;  // 默认public static final
      
      // 抽象方法
      void fly();          // 默认public abstract
      void land();
      
      // 默认方法（Java 8新特性）
      default void hover() {
          System.out.println("悬停在空中");
      }
      
      // 静态方法（Java 8新特性）
      static int getMaxSpeed() {
          return MAX_SPEED;
      }
  }
  ```

#### 接口的实现
```java
// 实现单个接口
class Bird implements Flyable {
    @Override
    public void fly() {
        System.out.println("鸟儿展翅高飞");
    }
    
    @Override
    public void land() {
        System.out.println("鸟儿平稳降落");
    }
}

// 实现多个接口
interface Swimmable {
    void swim();
}

class Duck implements Flyable, Swimmable {
    @Override
    public void fly() {
        System.out.println("鸭子起飞");
    }
    
    @Override
    public void land() {
        System.out.println("鸭子降落");
    }
    
    @Override
    public void swim() {
        System.out.println("鸭子游泳");
    }
}
```

#### 接口的应用场景
1. 策略模式
```java
// 支付接口
interface PaymentStrategy {
    void pay(double amount);
}

// 具体实现
class AliPay implements PaymentStrategy {
    @Override
    public void pay(double amount) {
        System.out.println("使用支付宝支付：" + amount);
    }
}

class WeChatPay implements PaymentStrategy {
    @Override
    public void pay(double amount) {
        System.out.println("使用微信支付：" + amount);
    }
}

// 使用支付策略
class ShoppingCart {
    private PaymentStrategy paymentStrategy;
    
    public void setPaymentStrategy(PaymentStrategy strategy) {
        this.paymentStrategy = strategy;
    }
    
    public void checkout(double amount) {
        paymentStrategy.pay(amount);
    }
}
```

2. 回调接口
```java
// 回调接口
interface CallBack {
    void onSuccess(String result);
    void onFailure(String error);
}

// 异步任务类
class AsyncTask {
    public void execute(CallBack callback) {
        try {
            // 模拟耗时操作
            Thread.sleep(1000);
            callback.onSuccess("操作成功");
        } catch (Exception e) {
            callback.onFailure("操作失败：" + e.getMessage());
        }
    }
}
```

### 5. 修饰符（Demo05）
#### static的详细用法
1. 静态变量
```java
public class Counter {
    private static int count = 0;  // 所有实例共享
    private int instanceCount = 0;  // 实例独有
    
    public Counter() {
        count++;
        instanceCount++;
    }
    
    public static int getCount() {
        return count;
    }
    
    public int getInstanceCount() {
        return instanceCount;
    }
}
```

2. 静态方法
```java
public class MathUtils {
    public static int add(int a, int b) {
        return a + b;
    }
    
    public static double calculateCircleArea(double radius) {
        return Math.PI * radius * radius;
    }
}
```

3. 静态代码块
```java
public class DatabaseConnection {
    private static Connection connection;
    
    static {
        try {
            // 加载数据库驱动
            Class.forName("com.mysql.jdbc.Driver");
            // 建立连接
            connection = DriverManager.getConnection(
                "********************************",
                "username",
                "password"
            );
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
```

#### final的使用场景
1. final类
```java
// 不能被继承的类
public final class String {
    // String类的实现
}
```

2. final方法
```java
public class Parent {
    // 不能被子类重写的方法
    public final void showInfo() {
        System.out.println("这是父类的最终方法");
    }
}
```

3. final变量
```java
public class Constants {
    // 常量定义
    public static final double PI = 3.14159;
    public static final String DATABASE_URL = "********************************";
    
    // final参数
    public void process(final int value) {
        // value = 100; // 编译错误，不能修改final参数
        System.out.println("处理值：" + value);
    }
}
```

### 6. 异常处理（Demo06）
#### 异常的完整层次结构
```
Throwable
├── Error
│   ├── OutOfMemoryError
│   ├── StackOverflowError
│   └── ...
└── Exception
    ├── RuntimeException
    │   ├── NullPointerException
    │   ├── ArrayIndexOutOfBoundsException
    │   ├── ClassCastException
    │   └── ...
    └── 其他Exception
        ├── IOException
        ├── SQLException
        └── ...
```

#### 异常处理的最佳实践
1. 基本异常处理
```java
public class FileProcessor {
    public void readFile(String filename) {
        FileReader reader = null;
        try {
            reader = new FileReader(filename);
            // 处理文件
        } catch (FileNotFoundException e) {
            System.out.println("文件不存在：" + e.getMessage());
        } catch (IOException e) {
            System.out.println("读取文件错误：" + e.getMessage());
        } finally {
            try {
                if (reader != null) {
                    reader.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
```

2. try-with-resources（Java 7特性）
```java
public class DatabaseOperations {
    public void processData() {
        try (Connection conn = DriverManager.getConnection(DB_URL);
             PreparedStatement stmt = conn.prepareStatement("SELECT * FROM users")) {
            
            ResultSet rs = stmt.executeQuery();
            while (rs.next()) {
                // 处理结果集
            }
        } catch (SQLException e) {
            System.out.println("数据库操作错误：" + e.getMessage());
        }
    }
}
```

3. 自定义异常
```java
// 自定义异常类
public class BusinessException extends Exception {
    private int errorCode;
    
    public BusinessException(String message, int errorCode) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public int getErrorCode() {
        return errorCode;
    }
}

// 使用自定义异常
public class UserService {
    public void register(String username, String password) throws BusinessException {
        if (username == null || username.isEmpty()) {
            throw new BusinessException("用户名不能为空", 1001);
        }
        if (password == null || password.length() < 6) {
            throw new BusinessException("密码长度不能小于6位", 1002);
        }
        // 注册逻辑
    }
}
```

4. 异常链
```java
public class ServiceLayer {
    public void processBusinessLogic() throws BusinessException {
        try {
            // 数据库操作
            databaseOperation();
        } catch (SQLException e) {
            // 保留原始异常信息
            throw new BusinessException("业务处理失败", 2001, e);
        }
    }
    
    private void databaseOperation() throws SQLException {
        // 数据库操作实现
    }
}
```

## Day05

### 1. 字符串操作（Demo01）
#### String类
- 不可变类型
- 字符串常量池
- 常用方法
  - length()：长度
  - charAt()：获取字符
  - substring()：截取
  - indexOf()：查找
  - lastIndexOf()：最后出现位置
  - split()：分割
  - replace()：替换
  - trim()：去除空格

#### 格式化
- format方法
- 占位符
  - %s：字符串
  - %d：整数
  - %f：浮点数
  - %.2f：保留小数点
  - %%：百分号

### 2. 包装类（Demo02）
#### 基本类型对应包装类
- byte -> Byte
- short -> Short
- int -> Integer
- long -> Long
- float -> Float
- double -> Double
- char -> Character
- boolean -> Boolean

#### 特性
- 自动装箱和拆箱
- 提供类型转换方法
- 提供常用工具方法
- 可用于泛型

### 3. 集合（Demo03）
#### List接口
- 有序集合
- 允许重复元素
- 主要实现类
  - ArrayList：数组实现
  - LinkedList：链表实现
  
#### 常用操作
- 添加：add()
- 删除：remove()
- 查找：get()
- 修改：set()
- 遍历方式
  - for循环
  - foreach
  - Iterator

## Day06

### 1. Set集合（Demo02）
#### 特点
- 不允许重复元素
- 无序集合
- 实现类
  - HashSet：哈希表实现
  - TreeSet：红黑树实现
  - LinkedHashSet：链表实现

#### 常用操作
- 添加：add()
- 删除：remove()
- 包含：contains()
- 遍历：foreach/Iterator

### 2. Map集合（Demo03）
#### 特点
- 键值对存储
- 键不能重复
- 实现类
  - HashMap：哈希表实现
  - TreeMap：红黑树实现
  - LinkedHashMap：链表实现

#### 常用操作
- 添加：put()
- 获取：get()
- 删除：remove()
- 遍历方式
  - keySet()
  - values()
  - entrySet()

### 3. 文件操作（Demo04）
#### File类
- 文件和目录操作
- 常用方法
  - createNewFile()
  - delete()
  - exists()
  - isFile()
  - isDirectory()

#### 文件流
- 字节流
  - FileInputStream
  - FileOutputStream
- 字符流
  - FileReader
  - FileWriter

## Day07 Maven项目管理

### 1. Maven基础
#### Maven简介
- 项目管理和构建工具
- 基于项目对象模型（POM）
- 标准化项目构建流程
- 依赖管理机制
- 规范项目结构

#### 项目结构
```
my-project/
├── src/
│   ├── main/
│   │   ├── java/        # 源代码
│   │   └── resources/   # 资源文件
│   └── test/
│       ├── java/        # 测试代码
│       └── resources/   # 测试资源
├── target/              # 编译输出目录
└── pom.xml             # Maven配置文件
```

#### POM文件详解
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>

    <!-- 项目坐标 -->
    <groupId>com.example</groupId>
    <artifactId>my-project</artifactId>
    <version>1.0-SNAPSHOT</version>
    
    <!-- 项目属性 -->
    <properties>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <testng.version>7.4.0</testng.version>
    </properties>

    <!-- 依赖管理 -->
    <dependencies>
        <!-- TestNG依赖 -->
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <version>${testng.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <!-- 构建配置 -->
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
            </plugin>
        </plugins>
    </build>
</project>
```

### 2. TestNG测试框架
#### TestNG配置文件
```xml
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite name="测试套件" parallel="methods" thread-count="5">
    <!-- parallel: 并行执行方式（methods/tests/classes） -->
    <!-- thread-count: 并行线程数 -->
    
    <test name="功能测试">
        <classes>
            <class name="com.example.LoginTest"/>
            <class name="com.example.RegisterTest"/>
        </classes>
        <groups>
            <run>
                <include name="functional"/>
                <exclude name="broken"/>
            </run>
        </groups>
    </test>
</suite>
```

#### TestNG注解使用
```java
import org.testng.annotations.*;

public class TestDemo {
    @BeforeSuite
    public void beforeSuite() {
        System.out.println("在整个测试套件开始前执行");
        // 例如：初始化测试环境、连接数据库等
    }

    @BeforeClass
    public void beforeClass() {
        System.out.println("在当前测试类开始前执行");
        // 例如：初始化测试数据
    }

    @BeforeMethod
    public void beforeMethod() {
        System.out.println("在每个测试方法前执行");
        // 例如：重置测试状态
    }

    @Test(groups = "functional")
    public void testLogin() {
        System.out.println("测试登录功能");
        // 测试代码
    }

    @Test(groups = "functional", dependsOnMethods = "testLogin")
    public void testUserProfile() {
        System.out.println("测试用户资料");
        // 测试代码
    }

    @Test(groups = "broken", enabled = false)
    public void testBrokenFeature() {
        System.out.println("已禁用的测试");
    }

    @AfterMethod
    public void afterMethod() {
        System.out.println("在每个测试方法后执行");
        // 例如：清理测试数据
    }

    @AfterClass
    public void afterClass() {
        System.out.println("在当前测试类结束后执行");
    }

    @AfterSuite
    public void afterSuite() {
        System.out.println("在整个测试套件结束后执行");
        // 例如：关闭数据库连接
    }
}
```

#### 数据驱动测试
```java
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

public class DataDrivenTest {
    // 数据提供者
    @DataProvider(name = "loginData")
    public Object[][] provideData() {
        return new Object[][] {
            {"admin", "123456", true},    // 有效账号
            {"user1", "wrong", false},     // 错误密码
            {"", "123456", false},         // 空用户名
            {"admin", "", false}           // 空密码
        };
    }

    // 使用数据提供者的测试方法
    @Test(dataProvider = "loginData")
    public void testLogin(String username, String password, boolean expectedResult) {
        boolean actualResult = login(username, password);
        Assert.assertEquals(actualResult, expectedResult);
    }

    // 从CSV文件读取测试数据
    @DataProvider(name = "userDataFromCsv")
    public Object[][] readCsvData() throws IOException {
        List<Object[]> data = new ArrayList<>();
        try (BufferedReader br = new BufferedReader(new FileReader("test-data.csv"))) {
            String line;
            while ((line = br.readLine()) != null) {
                String[] values = line.split(",");
                data.add(values);
            }
        }
        return data.toArray(new Object[0][]);
    }
}
```

#### 测试报告配置
```java
import org.testng.Reporter;
import org.testng.annotations.Test;

public class ReportTest {
    @Test
    public void testWithLogs() {
        // 添加日志到测试报告
        Reporter.log("开始测试", true);
        
        // 测试步骤
        Reporter.log("执行步骤1", true);
        // 测试代码
        
        Reporter.log("执行步骤2", true);
        // 测试代码
        
        Reporter.log("测试完成", true);
    }
}
```

## Day08 自动化测试

### 1. Selenium WebDriver
#### 基本配置
```java
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.support.ui.WebDriverWait;
import java.time.Duration;

public class SeleniumConfig {
    private WebDriver driver;
    private WebDriverWait wait;

    @BeforeClass
    public void setUp() {
        // 初始化浏览器驱动
        driver = new ChromeDriver();
        
        // 配置等待时间
        driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(10));
        wait = new WebDriverWait(driver, Duration.ofSeconds(10));
        
        // 最大化浏览器窗口
        driver.manage().window().maximize();
    }

    @AfterClass
    public void tearDown() {
        if (driver != null) {
            driver.quit();
        }
    }
}
```

#### 元素定位方法
```java
// 八种定位方式
WebElement element1 = driver.findElement(By.id("username"));
WebElement element2 = driver.findElement(By.name("password"));
WebElement element3 = driver.findElement(By.className("login-btn"));
WebElement element4 = driver.findElement(By.tagName("button"));
WebElement element5 = driver.findElement(By.linkText("注册账号"));
WebElement element6 = driver.findElement(By.partialLinkText("注册"));
WebElement element7 = driver.findElement(By.xpath("//input[@id='username']"));
WebElement element8 = driver.findElement(By.cssSelector("#username"));
```

#### 页面操作示例
```java
import org.openqa.selenium.*;
import org.openqa.selenium.support.ui.*;
import org.testng.annotations.*;

public class LoginTest {
    private WebDriver driver;
    private WebDriverWait wait;

    @Test
    public void testLogin() {
        // 打开登录页面
        driver.get("http://example.com/login");

        // 等待页面加载完成
        wait.until(ExpectedConditions.visibilityOfElementLocated(By.id("loginForm")));

        // 输入用户名和密码
        WebElement username = driver.findElement(By.name("username"));
        WebElement password = driver.findElement(By.name("password"));
        username.clear();
        username.sendKeys("admin");
        password.clear();
        password.sendKeys("123456");

        // 点击登录按钮
        driver.findElement(By.id("loginBtn")).click();

        // 等待登录成功
        wait.until(ExpectedConditions.urlContains("/dashboard"));
        
        // 验证登录成功
        WebElement welcomeMsg = wait.until(
            ExpectedConditions.presenceOfElementLocated(By.className("welcome"))
        );
        Assert.assertTrue(welcomeMsg.getText().contains("Welcome"));
    }
}
```

#### 常见操作示例
```java
public class SeleniumOperations {
    private WebDriver driver;
    private WebDriverWait wait;
    private JavascriptExecutor js;

    // 处理弹窗
    public void handleAlert() {
        Alert alert = wait.until(ExpectedConditions.alertIsPresent());
        alert.accept();  // 确认
        // 或者 alert.dismiss(); // 取消
    }

    // 切换框架
    public void switchFrame() {
        // 切换到iframe
        WebElement frame = driver.findElement(By.id("myFrame"));
        driver.switchTo().frame(frame);
        
        // 操作iframe中的元素
        driver.findElement(By.id("element")).click();
        
        // 切回主文档
        driver.switchTo().defaultContent();
    }

    // 处理下拉框
    public void handleDropdown() {
        Select dropdown = new Select(driver.findElement(By.id("dropdown")));
        dropdown.selectByVisibleText("选项1");
        // 或者
        dropdown.selectByValue("1");
        // 或者
        dropdown.selectByIndex(0);
    }

    // 文件上传
    public void uploadFile() {
        WebElement upload = driver.findElement(By.id("fileUpload"));
        upload.sendKeys("C:\\测试文件.txt");
    }

    // 执行JavaScript
    public void executeJS() {
        js = (JavascriptExecutor) driver;
        // 滚动到元素可见
        WebElement element = driver.findElement(By.id("myElement"));
        js.executeScript("arguments[0].scrollIntoView(true);", element);
        
        // 点击元素
        js.executeScript("arguments[0].click();", element);
    }
}
```

### 2. JSON处理
#### FastJSON基础用法
```java
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONArray;

public class JsonDemo {
    // 实体类
    public static class User {
        private String name;
        private int age;
        private List<String> roles;
        
        // getter和setter
    }

    @Test
    public void testJsonProcessing() {
        // 对象转JSON字符串
        User user = new User();
        user.setName("张三");
        user.setAge(25);
        user.setRoles(Arrays.asList("admin", "user"));
        
        String jsonString = JSON.toJSONString(user);
        System.out.println(jsonString);

        // JSON字符串转对象
        String json = "{\"name\":\"张三\",\"age\":25,\"roles\":[\"admin\",\"user\"]}";
        User parsedUser = JSON.parseObject(json, User.class);
        
        // 操作JSONObject
        JSONObject jsonObject = JSON.parseObject(json);
        String name = jsonObject.getString("name");
        int age = jsonObject.getIntValue("age");
        
        // 处理JSON数组
        String jsonArrayStr = "[{\"name\":\"张三\"},{\"name\":\"李四\"}]";
        List<User> users = JSON.parseArray(jsonArrayStr, User.class);
    }
}
```

#### 复杂JSON处理
```java
public class ComplexJsonDemo {
    // 嵌套对象
    public static class Department {
        private String name;
        private List<Employee> employees;
    }
    
    public static class Employee {
        private String name;
        private int age;
        private Map<String, Object> attributes;
    }

    @Test
    public void testComplexJson() {
        // 构建复杂JSON
        Department dept = new Department();
        dept.setName("技术部");
        
        Employee emp1 = new Employee();
        emp1.setName("张三");
        emp1.setAge(30);
        emp1.setAttributes(new HashMap<>() {{
            put("position", "开发工程师");
            put("skills", Arrays.asList("Java", "Python"));
        }});
        
        dept.setEmployees(Arrays.asList(emp1));
        
        // 转换为JSON
        String jsonString = JSON.toJSONString(dept, true); // 格式化输出
        System.out.println(jsonString);
        
        // 解析复杂JSON
        Department parsed = JSON.parseObject(jsonString, Department.class);
        
        // 使用JSONPath查询
        String json = JSON.toJSONString(dept);
        String name = JSONPath.eval(json, "$.employees[0].name").toString();
        List<String> skills = JSONPath.eval(json, "$.employees[0].attributes.skills");
    }
}
```

### 3. 综合实例
```java
import org.openqa.selenium.*;
import org.openqa.selenium.support.ui.*;
import org.testng.annotations.*;
import com.alibaba.fastjson2.JSON;

public class IntegrationTest {
    private WebDriver driver;
    private WebDriverWait wait;
    
    @Test
    public void testApiAndUi() {
        // 1. 通过API创建用户
        String userJson = "{\"username\":\"testuser\",\"password\":\"123456\"}";
        User user = JSON.parseObject(userJson, User.class);
        
        // 2. 使用Selenium验证UI
        driver.get("http://example.com/login");
        
        // 登录
        WebElement username = wait.until(
            ExpectedConditions.presenceOfElementLocated(By.id("username"))
        );
        username.sendKeys(user.getUsername());
        
        WebElement password = driver.findElement(By.id("password"));
        password.sendKeys(user.getPassword());
        
        driver.findElement(By.id("loginBtn")).click();
        
        // 验证登录成功
        WebElement welcome = wait.until(
            ExpectedConditions.presenceOfElementLocated(By.className("welcome"))
        );
        Assert.assertTrue(welcome.getText().contains(user.getUsername()));
        
        // 3. 获取用户数据并验证
        String responseJson = // 获取API响应
        User responseUser = JSON.parseObject(responseJson, User.class);
        Assert.assertEquals(responseUser.getUsername(), user.getUsername());
    }
}
```