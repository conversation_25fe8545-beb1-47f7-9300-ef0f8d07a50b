---
created: 2024-10-28
aliases: []
---

状态: [[进行中]]

标签: [[Linux]] [[服务配置]] [[RabbitMQ]] [[Redis]] [[MinIO]] [[项目部署]] [[命令行]]

# Linux中间件服务安装与配置

## rabbitmq安装
###  启动rabbitmq要注意主机名不能含有数字，修改主机不要设置为localhost
### 安装rabbitmq
#### yum install erlang
#### yum install rabbitmq-server
### 启动服务 有两种，一种是systemctl另一种是rabbitmq自带的  systemctl start rabbitmq-server 
### 启动管理端（web)  rabbitmq-plugins enable rabbitmq_management
### 重启服务
### 配置访问账号和权限
#### rabbitmgctl add user admin 123456
#### rabbitmgctl set_user_tags admin administrator
#### rabbitmqctl set_permissions p/ admin ",*" ".*” ".*"
## 安装Redis 
### 安装源
### 安装redis 
#### yum install redis
#### systemctl  start redis 
#### systemctl status redis 
#### 配置密码  vi /etc/redis.conf
##### 修改如下内容，先找到requirepass
##### vi编辑rredis.cof文件 #1.vi /etc/redis.conf
bind 127.0.0.1 添加井号，注释掉这部分，这是限制redis只能本地访问，而现在需要远程访问
##### requirepass 123456  设置密码
##### daemonize yes  让在后台运行
## 安装 minIO
 ### 1.在浏览器下载minIO。然后通过mobaXtrem上传到 /opt/minio目录中，使用mkdir创建
 ### 2.在minion目录下，使用chmod +x minio 给其运行权限
 ### 3.再/var/ib中创建一个目录minio,用来放上传的数据指定一个存放数据的路径
### var/1ib/minio#自己创建一个minio文件夹
### 启动 -1（第一次启动，这种方式启动） /opt/minio/minio server /var /lib/minio
### 将其运行到后台  nohup /opt/minio/minio server /var/lib/minio > /var/log/minio.1og &
## 上传项目前端文件dist.zip到 /var/www 目录中
### 然后解压dist.zip文件 unzip dist.zip
### 检查文件
#### root@wxf www]# cd dist! 
#### [root@wxf dist]# ls
#### favicon.ico   getAuthorization.js   index.htm]  index.html.bakl static
#### [root@wxf dist]# pwd
#### /war /www/dist
#### 注意确认路径
#### [root@wxf dist]#
### 添加配置文件

# 参考资料
- Linux系统管理手册
- RabbitMQ配置指南
- Redis部署文档
- MinIO安装手册
- 项目部署文档

# 相关笔记
- [[linux全部笔记]]
- [[Linux环境下的Java项目部署]]
- [[Linux项目部署流程与服务检查]]
