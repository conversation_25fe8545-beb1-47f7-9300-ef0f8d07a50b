---
created: 2024-12-19
aliases: []
---

状态: [[进行中]]

标签: [[Git]] [[持续集成]] [[版本控制]] [[DevOps]] [[自动化部署]]

# git和持续集成
## git安装
TortoiseGit-2.13.0.1-64bit.msi    图形化界面工具
## git
### 版本控制工具，对开发过程中的代码文档进行管理
#### 记录代码、文档的修改记录，可以方便回退到任一版本
#### 实现多人协同开发
#### 统计工作量，增删改的代码行数
### 版本控制工具
#### SVN ，一些老的项目，还在使用SVN
#### Git,目前最先进的版本控制工具
### SVN和Git的差别
#### SVN集中式版本控制
#### Git分布式版本控制
### Git服务器
#### 大公司：内部搭建自己的git服务器。所有产品的代码，放到这个git上进行管理
#### 小公司：使用第三方的的代码托管平台。GitHub：国外，很多开源项目将代码发布在这里，Gitee：国内：码云
#### 个人开发者：使用第三方代码托管平台。
#### Gitee，注册一个账号
## Git常用命令：
### 基础配置：
#### 配置用户名 ：git config --global user.name  "你的用户名"
#### 配置邮箱：git config --global user.email "你的邮箱"
#### 存储凭证：git config --global credential.helper store
#### 检查配置结果：git config -l
![[Pasted image 20241219185535.png]]
### 将远端代码克隆带本地
#### git clone
### 将本地工作区新增/修改/删除的代码上传到服务器上
git add 
git commit 
git push
### 一些文件，不想纳入版本控制，主目录下新建.gitignore文件
#### pytest_cache 临时文件夹
#### .idea 工程
#### areport 测试报告
#### report 测试报告
#### log 日志
### 查看历史记录：show log
### 回退版本：
#### 当前版本有问题，需要将文本回退到某个历史记录
![[Pasted image 20241219190505.png]]
#### 修改了工作区的文件
#### commit + push 推送到远端
### 冲突文件处理
#### 多个人同时维护一个代码仓库
#### 测试人员A拉取了最新的代码
#### 测试人员B拉取了最新的代码
#### 测试人员A修改了f1、f2、f3文件，推送到远端
#### 测试人员B修改了f1、f4、f5文件，推送到远端。如果AB修改f1的时候，改得不是同一段代码，系统会自动归并。修改的是同一段代码，B提交时会初出现冲突
##### git pull 拉取最新的代码
##### 人工解决冲突
##### commit +push 推送到远端
### 分支管理
#### 产品开发的过程中，会创建多个分支。
#### 某个手机app ，有国内版和国外版，分别创建一个分支
#### 产品有多个版本并行发布，V1R1分支（维护版本），V1R2（在研版本）
#### master 主分支。开发人员要切换不同的分支，修改代码。比如切换V1R1版本修改线上的bug，切换到V1R2新增某个功能
#### 切换分支：Switch/checkout
#### 分支归并
##### V1R1修复了一个缺陷，V1R2也有这个缺陷，需要将V1R1修改的代码归并到V1R2上。
##### 归并分支也有可能出现冲突，V1R1修复。

α测试:
- Alpha测试（α测试）：这是软件测试的早期阶段，通常由开发团队内部或公司内部的选定群体进行测试。在一个受控的环境中进行，目的是在软件发布到外部世界之前，找出主要的错误。Alpha测试的目标是验证软件的功能是否符合需求，并且找出需要在进入更广泛测试阶段之前解决的主要问题。
β测试 
- Beta测试（β测试）：在Alpha测试之后，Beta测试涉及将软件发布给一个更大的外部用户群体。这阶段的测试是在真实世界条件下进行的，目的是从更多样的用户那里获得关于可用性、功能性以及之前未发现的错误或缺陷的反馈。Beta测试有助于在正式发布前进一步完善产品，通常会发布一个“测试版”，用户可以选择参与。
γ测试
- Gamma测试（γ测试）：虽然不像其他测试那样常见，并且不是每个测试周期都包括这一阶段，但Gamma测试指的是在软件已经发布给公众后进行的测试。在正式发布或重大更新之前进行。Gamma测试可能包括监控用户反馈和使用数据，以便进行最后的调整或者为未来的更新做计划。一些人将其解释为发布后持续进行的过程，开发者基于用户的互动和反馈不断改进软件。
# 参考资料
[git官网](https://git-scm.com/download/win)
[官网：](https://git-scm.com/download/win)
[镜像：](http://npm.taobao.org/mirrors/git-for-windows/)
[乌龟可视化git安装](https://gitcode.csdn.net/65ec4dd11a836825ed7977df.html)
# 相关笔记