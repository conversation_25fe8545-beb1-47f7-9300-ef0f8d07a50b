---
created: 2024-12-18
aliases: []
---

状态: [[进行中]]

标签: [[测试框架]] [[自动化测试]] [[Python]] [[单元测试]] [[测试工具]]

# Pytest 测试框架总结


## 1. 基本特点
* **流行**: Pytest 是 Python 中最流行的测试框架之一
* **简单**: 使用简单，无需复杂的类继承
* **命名规则**: 测试用例的命名必须以 `test_` 开头
* **自动发现**: 能够自动发现测试用例

## 2. 基本用法
```python
def test_function():
    assert expression  # 断言测试结果
```
- 使用 assert 语句进行断言，判断测试结果是否符合预期

## 3. Fixture 装饰器
- **用途**: 用于准备测试数据和环境
- **实现**: 通过 @pytest.fixture 装饰器定义 fixture 函数
- **示例**:
```python
@pytest.fixture
def prepare_data():
    # 测试前准备
    data = {"key": "value"}
    yield data
    # 测试后清理
```
- yield 语句分隔测试前的准备和测试后的清理操作

## 4. 参数化测试
- **用途**: 使用不同的参数值运行相同的测试逻辑
- **实现**: 通过 @pytest.mark.parametrize 装饰器定义参数列表
- **示例**:
```python
@pytest.mark.parametrize("param_name", [value1, value2])
def test_with_params(param_name):
    # 测试代码
```

## 5. 命令行参数
- pytest test_file.py: 运行指定测试文件
- -v: 显示详细信息
- -s: 显示 print 输出
- -k "expression": 按关键字运行测试
- -m "mark": 运行带特定标记的测试

## 6. 断言方式
- **基本断言**:
```python
assert expression
assert a == b
assert a in b
```
- **异常断言**:
```python
with pytest.raises(ExceptionType):
    # 可能抛出异常的代码
```

## 7. 测试报告
- 生成 HTML 报告
- 统计测试结果
- 显示通过数量、失败数量和测试覆盖率分析

## 8. 最佳实践
- **单一职责**: 每个测试函数只测试一个功能点
- **数据管理**: 使用 fixture 管理测试数据
- **目录结构**:
```
tests/
├── conftest.py  # 共享 fixture
├── test_module1.py
└── test_module2.py
```
- conftest.py 文件用于存放共享的 fixture

## 9. 常用标记
- @pytest.mark.skip(reason="..."): 跳过测试用例
- @pytest.mark.skipif(condition, reason="..."): 在满足条件时跳过测试用例
- @pytest.mark.xfail: 标记测试用例预期会失败

## 10. 配置文件
- pytest.ini: 配置文件示例
```ini
[pytest]
markers =
    slow: 运行缓慢的测试
    integration: 集成测试
testpaths = tests
python_files = test_*.py
```
- markers: 定义自定义标记
- testpaths: 指定测试用例的搜索路径
- python_files: 指定测试文件的匹配模式

# 参考资料
- Pytest官方文档
- Python测试实践指南
- 自动化测试框架文档
- Python编程手册

# 相关笔记
- [[learnpython]]
- [[测试框架]]
- [[自动化测试]]
- [[单元测试]]
- [[测试用例设计]]