---
created: 2024-11-04
aliases: []
---

状态: [[进行中]]

标签: [[python]] [[编程语言]] [[后端开发]] [[数据处理]]

# Python基础

## 基本概念

### 注释 使用#进行单行注释，使用''' '''或""" """进行多行注释。

### print() 用于输出括号内的内容到屏幕，支持多个参数，参数间默认以空格分隔。

### 输入 input( )用于输出括号内的内容到屏幕，支持多个参数，参数间默认以空格分隔。

## 数据类型

### 数字类型

#### int 整型

#### 多种进制表示（二进制，八进制，十六进制）

#### float 浮点型

#### 浮点数运算可能存在精度损失。

### 字符类型

#### str 字符串 使用单引号' '或双引号" "定义，支持拼接(+)和重复(* ) 操作。

#### 布尔类型 True和False，用于逻辑判断。

#### 特殊值 None 表示空

## 变量与类型转换

### 变量名区分大小写，可以包含字母、数字和下划线，但不能以数字开头

### 类型转换：int(), float(), str(), bool()等函数可用于不同类型间的转换。

## 运算符

### 算数运算符

#### +, -, , / , (整除), %(取余), " (幂)"。

### 比较运算符

#### >, ​<, ​>=, <=, == , !=

### 赋值运算符：

#### =​, +=​, -=​, *=​, /=​, //=​, %=​, **=​

### 控制结构

- 条件判断：if​...elif​...else​结构，用于根据不同条件执行不同的代码块。

## 序列类型

- 列表：有序的、可变的集合，允许重复的元素，用方括号[]​表示。
- 元组：有序的、不可变的集合，允许重复的元素，用圆括号()​表示。
- 字符串：有序的、不可变的字符序列，用单引号或双引号表示。

#### 映射与集合类型

- 字典：无序的键值对集合，键必须是不可变类型，值可以是任意类型，用花括号{}​表示。
- 集合：无序的、不重复的元素集合，用花括号{}​或set()​函数创建。

#### 可变与不可变类型

- 不可变类型：int​, float​, str​, bool​, tuple​。
- 可变类型：list​, dict​, set​。

#### 特殊说明

- 字符串一旦创建就不能修改其内容，若需修改，则需要创建新的字符串。
- 列表可以通过索引或切片的方式直接修改其中的元素。
- 浮点数由于存储方式的原因，在进行计算时可能会产生微小的误差。

## python结构

### 分支结构

#### if elif elif else

### 循环结构

#### for ，while

#### range函数 ：用来生成整数序列

#### range(start, stop, step) 生成从start(包含)开始到end(不包含) 结束的整数序列

#### for i in range(10) i的范围为0-9

#### while i <= 100: 当i小于等于100时，执行

#### break 跳出循环

#### continue 结束本次循环

### 顺序结构

#### 程序按照代码出现的先后顺序依次执行

## 列表基础知识

### 创建列表

- 使用方括号[]​创建列表。
- 使用list()​函数将其他可迭代对象转换为列表。

### 访问列表元素

- 通过索引访问，索引从0开始。
- 支持负索引，-1​表示最后一个元素。
- 使用切片获取子列表。

### 列表的长度

- 使用len()​函数获取列表的长度。

### 修改列表元素

- 直接通过索引修改列表中的元素。
- 支持嵌套列表的修改。

### 添加元素

- ​append()​：在列表末尾添加一个元素。
- ​insert(index, value)​：在指定位置插入一个元素。
- ​extend(iterable)​：将一个可迭代对象的所有元素添加到列表末尾。

### 删除元素

- ​pop([index])​：移除并返回指定位置的元素，默认移除最后一个元素。
- ​remove(value)​：移除列表中第一个匹配的元素。
- ​del​关键字：根据索引删除元素或切片。
- ​clear()​：清空整个列表。

### 其他操作

- ​index(value[, start[, end]])​：查找元素的索引，可指定搜索范围。
- ​count(value)​：统计元素出现的次数。
- ​reverse()​：反转列表。
- ​sort([key=func[, reverse=flag]])​：对列表进行排序，支持自定义排序规则和降序排序。
- ​copy()​：拷贝列表。

### 遍历列表

- 使用for​循环遍历列表元素。
- 使用range(len(list))​结合索引遍历。

### 列表操作

- 添加元素
    
    - ​append(x)​: 在列表末尾添加元素x。
    - ​insert(i, x)​: 在索引i的位置插入元素x。
    - ​extend( iterable )​ 或 +=​: 将一个可迭代对象的所有元素添加到列表末尾。
- 删除元素
    
    - ​del x​: 删除指定索引的元素。
    - ​pop([i])​: 删除索引i的元素，并返回被删除的元素。如果不指定索引，则默认删除并返回列表的最后一个元素。
- 访问元素
    
    - ​a[i]​: 访问索引为i的元素。
    - 切片操作：a[start:stop:step]​。
- 修改元素
    
    - ​a[i] = x​: 将索引为i的元素修改为x。
- 列表排序
    
    - ​sort(key=None, reverse=False)​: 对列表进行排序。
    - ​sorted(iterable, key=None, reverse=False)​: 返回一个新的列表，原列表不变。
- 列表反转
    
    - ​reverse()​: 原地反转列表。
- 查找元素
    
    - ​min(a)​: 返回列表中的最小值。
    - ​max(a)​: 返回列表中的最大值。
    - ​sum(a)​: 返回列表中所有元素的和。
    - ​count(x)​: 返回元素x在列表中出现的次数。
    - ​index(x)​: 返回元素x在列表中第一次出现的位置。

### 元组操作

- 不可变性
    
    - 元组是不可变的，不能修改元素或增加/删除元素。
- 访问元素
    
    - ​t[i]​: 访问索引为i的元素。

## 字典操作

- 创建字典
    
    - ​{key1: value1, key2: value2}​: 创建一个字典。
- 访问元素
    
    - ​d[key]​: 访问键为key的值。
- 修改元素
    
    - ​d[key] = value​: 修改键为key的值为value。
- 添加/删除元素
    
    - ​update(E)​: 添加或更新字典中的元素。
    - ​pop(key)​: 删除并返回键为key的元素。
    - ​del d[key]​: 删除键为key的元素。
- 字典长度
    
    - ​len(d)​: 返回字典中元素的数量。
- 遍历字典
    
    - ​for key in d.keys()​: 遍历字典的键。
    - ​for k, v in d.items()​: 遍历字典的键值对。
- 字典合并
    
    - 通过循环和条件判断来合并字典。

## 复制操作

- 浅拷贝
    
    - ​copy()​: 创建一个新对象，但是元素仍然是对原始对象中元素的引用。
- 深拷贝
    
    - ​copy.deepcopy()​: 创建一个新对象，并且递归复制所有元素。

## 集合

### 定义集合：集合是一种无序且不允许重复的可变数据类型。

#### colors = {"red", "yellow", "green", "blue"}

#### colors.add("block") ## 添加元素

#### print(colors)

### 删除元素

#### colors.remove("red") ## 删除指定元素

### 去重：

#### a = [1, 2, 3, 4, 5, 5, 4, 3]

#### b = list(set(a)) ## 转换为集合再转回列表去重

#### print(b)

## 字符串的表示方式

普通字符串：可以使用单引号或双引号。

```plaintext
string1 = "Hello, world!"
string2 = '你好'
```

原始字符串：使用 r 前缀，使反斜杠不被转义。

```plaintext
raw_string = r"D:\path\to\file"
```

长字符串：三引号（''' 或 """）可以表示多行字符串。

```plaintext
long_string = """
Hello,
World!
"""
```

字符串的方法  
大小写转换：

```plaintext
string = "Hello"
print(string.upper())    # 转为大写
print(string.lower())    # 转为小写
print(string.title())     # 首字母大写
```

字符统计：

```plaintext
r = {}
for c in string:
    if c.isupper():
        r['大写字母'] = r.get('大写字母', 0) + 1
print(r)
```

替换字符：

```plaintext
new_string = string.replace("l", "L")
print(new_string)
```

去除空白字符：

```plaintext
string_with_spaces = "   Hello   "
print(string_with_spaces.strip())  # 去掉两边的空白
```

字符串分割和连接：

```plaintext
my_string = "Hello world"
words = my_string.split()  # 按空格分割
joined_string = " ".join(words)  # 用空格连接
```

字符串格式化  
f-string 格式化：

```plaintext
name = "小明"
print(f"我的名字是{name}")
```

format() 方法格式化：

```plaintext
print("姓名：{}, 年龄：{}".format("小明", 20))
```

### 编码与解码

- 获取字符的整数表示：ord('A')​
- 获取整数表示的字符：chr(65)​
- 判断字符类型：isalpha()​, isdigit()​

### 获取字符的整数表示：

```plaintext
print(ord('A'))  # 输出65
```

### 获取整数表示的字符：

```plaintext
print(chr(65))  # 输出 'A'
```

### 判断字符类型：

```plaintext
char = input("请输入一个字符：")
if char.isalpha():
    print("是字母")
elif char.isdigit():
    print("是数字")
```

## 函数

### 1. 函数定义和调用：

- 使用def​关键字定义函数，可以提高代码复用率和可读性。
- 函数可以有参数，也可以没有参数，可以有多个参数。
- 函数调用时，可以按照位置传参，也可以通过参数名传参。

### 2. 参数类型：

- 位置参数：必须按照定义的顺序传入参数。
- 可变参数：使用*args​定义，可以接收任意数量的位置参数，被打包成一个元组。
- 关键字参数：使用**kwargs​定义，可以接收任意数量的关键字参数，被打包成一个字典。
- 命名关键字参数：在函数定义中，使用*​分隔位置参数和命名关键字参数，之后定义的参数必须通过关键字传入。

### 3. 高阶函数：

- 函数可以作为参数传递给另一个函数，这种函数称为高阶函数。
- 常见的高阶函数包括map()​、reduce()​、filter()​和sorted()​。

### 4. 变量作用域：

- 局部变量：在函数内部定义，只能在函数内部使用。
- 全局变量：在函数外部定义，可以在整个程序范围内使用。
- 使用global​关键字可以在函数内部修改全局变量的值。

### 5. 匿名函数（lambda）：

- 使用lambda​定义没有名字的函数，常用于只需要使用一次的函数。
- 匿名函数常用于map()​和filter()​等函数中，以简化代码。

### 6. 函数作为参数：

- 可以将函数作为参数传递给另一个函数，例如在add​函数中传递不同的函数来计算平方和或立方和。

### 7.函数返回值：

- 函数可以有返回值，使用return​关键字返回。

### 8. 函数嵌套定义：

- 可以在一个函数内部定义另一个函数，内部函数可以访问外部函数的变量。  
    以下是根据文本2内容，按照文本1的格式整理的Python知识点总结：

### 9. 函数参数

- 函数参数可以是任意类型的：数字、字符串、列表、元组、字典等。

### 10.函数的动态增强

- 通过装饰器，可以在不修改原有函数代码的情况下，为函数添加新的功能，如日志记录、性能测试、事务处理等。

### 11.函数参数类型

- 位置参数：必须按照定义的顺序传入参数。
- 可变参数：使用*args​定义，可以接收任意数量的位置参数，被打包成一个元组。
- 关键字参数：使用**kwargs​定义，可以接收任意数量的关键字参数，被打包成一个字典。
- 命名关键字参数：在函数定义中，使用*​分隔位置参数和命名关键字参数，之后定义的参数必须通过关键字传入。

### 高阶函数

- 函数可以作为参数传递给另一个函数，这种函数称为高阶函数。
- 常见的高阶函数包括map()​、reduce()​、filter()​和sorted()​。

## 闭包与装饰器

#### 闭包

- lazy_calc函数是一个闭包的例子，它返回了一个内部函数calc，这个内部函数可以访问外部函数的变量args。
- outer函数也是一个闭包的例子，它返回了一个内部函数inner，这个内部函数可以访问外部函数的变量x。

#### 装饰器

- 装饰器是一种设计模式，用于在不修改原有函数代码的情况下，给函数动态地添加额外的功能。
- log函数是一个装饰器，它接受一个函数func作为参数，并返回一个新的函数inner，这个新函数在调用原函数前后打印日志信息。
- 使用@log 语法可以简化装饰器的应用，自动将函数作为参数传递给装饰器。

#### eval()​

- eval数用于计算表达式的值，可以将字符串形式的表达式转换为实际的值。

#### zip()​

- zip 函数可以把多个可迭代的对象，组成一个新的迭代器，将对象中对应的元素打包成一个个元组。

#### enumerate()​

- enumerate 函数用于遍历列表、字符串或元组，返回索引和元素，同时遍历数据和序号。

### 文件读写

- 读文件：磁盘上的文件读到内存中。
    
- 写文件：把内存中的内容写到文件中。
    
- 文本文件：文件内容是字符串，要注意文件的编码方式。
    
- 二进制文件：图片、视频、音频等文件，本质上文本文件也属于二进制文件。
    
- 三步操作：
    
    - 打开文件 open
        
    - 读/写文件 read/readlines/readline/write
        
    - 关闭文件 close
        
## 算法

### .递归

#### 概念：函数自己调用自己 ，把一个大问题分解成与自己相似的小问题

#### 例子 ：阶乘：循环,5! = 5 * 4* 3* 2 *1 递归 5! = 4! * 5 1！= 1 递归：必须有结束条件，终止条件、出口条件

#### for i in range(1, n + 1): r = r * i return r

### 顺序查找：

#### 概念：无序的列表中，查找某个元素是否存在。查找元素在那些位置存在 遍历序列，检查每个元素是否与目标一致

### 二分查找：

#### 概念： 有序的序列中，查找某个元素是否存在。查找元素在序列中的位置有哪些。 有序查找。 从小到大序列，key是不是存在 先用key 与中间值进行比较，如果比中间值小

### 排序

#### 排序分类

##### 冒泡排序，选择排序，插入排序，快速排序，归并排序

#### 冒泡排序：最简单，必须知道的，比较相邻元素，如果顺序不对，就将他们交换过来，直到没有可以交换的元素为止．数组中的元素排序-冒泡排序，比较相邻元素，顺序与要求的不一样，交换位置，直到没有可交换的为止。

##### 例子 ：4 3 6 8 1 0 9 10 进行冒泡排序 第一次：4 3 比较 3 4 6 8 1 0 9 10 第二次;4 6 比较，顺序不变 第三次 6 8比较顺序不变 ，第四次8 1 比较，交换位置3 4 6 1 8 0 9 10 第五次比较 8 0 ，多次重复上述操作，最大的元素到了最后，最小的到了最前方
##### 从小到大排序。{90，100，99，80，10}
##### 第一趟比较：
##### 90 100比较：90 100 99 80，10
##### 100 99比较：90 99 100 80，10
##### 100 80比较：90 99 80 100 10
##### 100 10比较：90 99 80 10 100最大的元素放到了最后
##### 外层循环，控制趟数
#### 快速排序：效率最高的

##### 例子：4 3 6 8 1 0 9 10 基准值 中间、最开始的元素作为基准 比基准值小的放前面，比基准值大的放后面，与基准值一样的放中间 4作为基准 3 1 0 4 6 8 9 10 ， 3 1 0 3作为基准 1 0 3 1 作为基准 0 1 3 出口条件，序列中只有一个元素，就是排序好的序列。

## 推导式

### 推导式
####  列表推导式、集合推导式、字典推导式  快速创建列表、集合、字典的一种方法。
##### a = []  for i in range(1, 11):  a.append(i * i)  print(a)   和  a = [ i *  i for i in range(1, 11)]   print(a)一样的效果
### 数据类型回顾
#### 可变：字典（dict）列表（list）集合（set)

#### 不可变：字符串（str) 、整数（int）、浮点数（float）,��尔（boolean）

## 生成式

### 概念：数据较大的列表，创建出来后，比较占用存储空间。如果只用列表前面的元素，后面的空间会被浪费掉。 生成式可以解决这种问题。按照需求生成一个序列，不是一次性把所有元素生成出来。

### 生成器、生成式有两种方式实现
#### 1 函数，yield
#### 2.生成式表达式，列表推导式的[ ] 换成 （）
##### a = [i*i for i in range(1,10001)]    print(a) 
##### print(a.__next__())  # 9  
##### print(next(a))

## 包和模块
### 一个模块也就是一个py文件。  包也就是一个文件夹/目录 里面有__init__.py文件。__init__.py文件可以是空的，通过它表示python中的package。  包可以被别的目录/文件使用。
#### 导包的三种方法
##### 1. import 模块名  
##### 2. from 模块名 import 函数名  
##### 3. from 模块名 import *
##### as 给包取别名    from MyPackage.myMath import add as a, PI as p  # 从 myMath 中导入 add 函数和常量 PI 别名取为a 和 p
## 内置模块
### Python自带的模块，不需要安装，直接使用
### 常用的内置模块有：
#### os 处理文件和目录
##### os.path.join() 连接路径  
##### os.path.split() 分割路径        os.path.splitext() 分割文件名和扩展名  
##### os.path.getatime() 获取文件最后访问时间
#### sys 控制python运行环境
##### print(os.path.exists(path)) # 判断文件是否存在  
##### print(os.path.isfile(path)) # 判断是否为文件  
##### print(os.path.isdir(path)) # 判断是否为目录  
##### print(os.path.getsize(path)) # 获取文件大小，单位字节
#### random 随机数生成
#### datetime 日期和时间
##### print(time.time()) # time.sleep(1) # 休眠1秒  
##### print(time.ctime())  
##### print(time.gmtime())
##### print(datatime.now) # 获取当前时间
#### math 数学函数，模块
## 第三方库
### 概念：除了Python自带的库，还有很多第三方库可以帮助我们完成编程任务。
### 常见库
#### pip 工具。包管理工具  
#### selenium 库。用于自动化测试。  
#### requests 库。用于发送HTTP请求,接口，爬虫 
#### locust 库。性能测试  
#### pymysql 库。连接MySQL数据库,操作数据库
### 命令行工具
#### pip list 列出已安装的包  
#### pip install 包名 安装包  
#### pip uninstall 包名 卸载包  
#### pip install mysql  安装mysql包  
#### pip unstall mysql 卸载mysql包  
#### pip install wordcloud  安装wordcloud包  
#### pip list | findstr "Faker"  查找faker包  
#### faker 库。生成随机数据  
#### wordcloud 库。生成词云图,根据文本生成词云
## 异常处理
### 概念：- **异常**是程序执行过程中出现的错误。常见的异常包括 `ZeroDivisionError`、`TypeError`、`ValueError`、`IndexError` 等。
### 使用try 和except块
#### try：将可能引发异常的代码放在 try 块中。
#### except：用来捕获和处理异常的代码块。可以处理特定的异常类型，或者处理所有异常。
#### 抛出异常 raise Exception("异常信息")
## 面向对象
### 面向过程：POP Procedure Oriented Programming   分析问题的解决步骤，用语句/函数实现每个步骤
### 面向对象：OOP Object Oriented Programming 分析问题中有哪些角色，每类角色有哪些行为和属性把角色抽象成类型   再创建对象，调用相应的方法来解决问题
### 一个实例对应的有相应的属性和行为，例如一个长方形，长和宽是他的属性，周长，面积是他的行为
#### 属性是固定的，行为：方法是函数实现的
## 属性
### 分类
#### 实例属性：每个实例都有自己的属性
#### 类属性：所有实例共享的属性
### 具体例子
#### self.name = name  # 实例属性
#### Student.count += 1  # 类属性的操作，类属性的操作必须通过类名来进行  每创建一个学生，count属性自增1
## 方法
### 实例方法/成员方法，第一个参数是self，表示实例本身，可以调用实例变量和实例方法。
### 类方法，第一个参数是cls，表示类本身，可以调用类变量和类方法。
### 静态方法，不用实例化也可以调用，不需要self和cls参数。
### 基本使用
#### class Hero:  
#### def __init__(self, name,power=100):          self.name = name  
#### self.power = power  
#### def eat(self,n):  
#### if __name__ == '__main__':  
#### hero1 =Hero('吕布')  
## 封装
### 概念：把属性以及对属性的一些操作绑定在一起，对外隐藏内部的实现细节，只暴露必要的接口。
#### 私有属性：在NewDog类中，属性__前带了下滑线的是被定义为私有属性，外部无法直接访问这些属性
#### 公开方法：Demo01中提供了setAge和getAge方法设置和获取年龄


## 继承
### 概念：子类可以继承父类的属性和方法，并可以对父类进行扩展，增加新的功能。
### 详细解释：继承：创建类的时候，可以从一个已有的类派生出一个新类。
#### 已有的类：父类、基类
#### 新创建的类：子类、派生类
#### 子类可以继承父类的属性和方法，还可以添加新的属性和方法。提高了代码的复用率。
#### 支持单继承，多继承（有多个父类）。
#### 构造方法不会被继承
#### 所有的类都是object继承的
## 多继承
### 一个类可以从多个父类继承，这就是多继承。
### 语法：
### class 子类(父类1, 父类2, 父类3...):
### 子类代码
#### 动物 鸟 鹦鹉
## 多态
### 概念：父类定义的接口，子类可以实现自己的功能，也可以调用父类的方法，实现代码的复用。 多态，多种形态。对子类来说，既是子类类型的，又是父类类型的不同类型的对象对同一个函数做出多种不同的响应，同一个操作可用于不同的对象
## 数据库
### 使用Python来操作数据库，pymysql,专门用来操作数据库的
### 数据库使用步骤
#### 1 连接数据库
##### db_info: {"user":"root", "pwd": "123456", "host":"localhost", "database": "", "port": 3306} 使用相关语句进行连接
#### 2.执行sql   使用sql语句操作数据的增删改查
#### 3。断开连接 使用close方法
## 多线程
### 可以提高应用程序的处理能力。  同时读取多个文件；同时处理多个任务；同时爬取网页的信息。
### 1.threading模块实现  
### 2.concurrent.futures模块实现 线程池的方法实现

# 参考资料
- Python官方文档
- Python编程指南
- Python标准库文档
- Python最佳实践指南

# 相关笔记
- [[learnpython]]
- [[编程语言]]
- [[数据结构]]
- [[算法]]
- [[后端开发]]
- [[数据处理]]