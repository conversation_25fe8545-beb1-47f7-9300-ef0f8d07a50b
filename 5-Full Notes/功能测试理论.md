---
created: 2024-11-21
aliases: []
---

状态: [[进行中]]

标签: [[测试理论]] [[测试流程]] [[测试计划]] [[测试用例]] [[缺陷管理]] [[测试报告]] [[质量保证]]

# 软件测试基础理论与实践

## 软件研发流程
## 收集用户需求：SE （system engineer）收集用户需求（Requirement）：收集用户的使用场景、用户工作中痛点的问题，整理归纳后，输出需求文档
### 输出文档
#### 有哪些功能：支持聊3天，支持群聊，聊天支持发送语音、文字、图片、视频……，
#### 功能具体的规格：发送文字时最多1000个字，最多99张图片，语音最长60s。
性能：发送一条消息，到对方收到，2s以内能收到。
兼容性：支持在安卓（11、12、9、10），iOS，鸿蒙、windows、mac、Linux等系统上使用。
## 迭代计划/测试计划：版本的交付计划，测试经理输出测试计划
## 需求澄清会议/需求评审会议：学习需求的一个过程。SE召集开发、测试、运维、资料开发人员，组织一个会议，SE讲解需求，其他人提出需求的疑问或
### 作为测试，在参加会议时，要关注哪些点？你提出过哪些疑问或建议?
### 输出文档：会议纪要、优化后需求文档。
## 需求变更（CR Change Requirement)：在软件开发的任意环节，可能发生需求变更
## 开发设计（Design）：概要设计和详细设计，梳理业务流程，划分功能模块。模块与模块之间通过接口交互，接口是怎么设计的，后台的数据库使用哪种，表结构怎么设计，具体的sql语句是什么。
### 输出文档：概要实际和详细设计的文档。
## 测试分析：分析需求怎么测试，梳理测试类，使用思维导图进行展示。
### 输出文档：xxx需求分析.xmind、xxx模块测试分析.xmind
## 串讲和反串讲：确保SE、开发、测试对需求的理解是一样的。确保开发、测试的全面性
### 串讲：开发召集SE、开发、测试人员，讲解设计文档。
### 反串讲：测试召集SE、开发人员、讲解测试分析文档。


## 编码：开发写代码实现相应的需求。
### 自验证
### 交叉代码检视
### 代码扫描工具，检查是否有问题
## 测试设计/用例设计/用例编写：把测试分析进一步细化成测试用例。
### 测试内部进行评审。会议评审
### 导入禅道等管理工具中进行管理。
## 搭建环境
### 开发环境：开发人员使用的环境
### 测试环境：测试人员使用的环境
### 生产环境/线上环境/真实环境：用户使用的环境
## 冒烟测试：选择最基本的功能对软件进行测试
## 测试执行：按照测试用例对产品进行系统测试
### 发布版本1---冒烟---测试执行，开发人员修改缺陷
### 发布版本2---冒烟---测试执行，开发人员修改缺陷
### 测试版本3---冒烟---测试执行
## 测试报告：测试结论
## 发布
# 软件测试的流程
## 参加需求评审会议--测试计划---测试分析--测试设计---脚本开发--搭建环境--冒烟测试---测试执行--测试报告
# 测试人员的工作价值是什么？你是怎么理解软件测试的？
## 软件测试
### 质量检测：为了发现软件中的错误，依据产品需求文档对软件进行测试的一个过程。
### 质量改进：分析缺陷产生的原因和趋势，提出研发过程的改进措施。
### 质量鉴定：产品能不能发布。
# 测试类型
## 是否关心软件内部结构和具体实现
### 白盒测试
### 黑盒测试
### 灰盒测试
## 是否运行程序的角度
### 静态测试：代码检视、文档测试、代码扫描工具进行扫描
### 动态测试：需要运行产品后，进行测试
## 按照自动化程度
### 手工测试
### 自动化测试：借助工具、代码开展的测试。
## 从软件开发过程，按照阶段分
### 单元测试Unit test：开发人员开展，针对代码中的函数/方法进行测试
### 集成测试integration Test：将最小可测的单元集成到一起进行测试
### 冒烟测试Smoking Test ：最基本功能的测试，冒烟测试是开发转测试的一个入口标准，冒烟通过继续测试，冒烟不通过，开发人员继续进行开发，修复缺陷
### 系统测试System Test，功能、性能、兼容性、安全、稳定性、国际化（中文、日文、韩文、英文版等）、界面UI、用户体验、安装卸载对产品进行一个全面的测试
### 回归测试Regress Test，缺陷回归，开发修复缺陷后，对缺陷进行回归；版本回归，对开发修改的内容进行测试，同时进行基本功能验证。
### 阿尔法测试Alpha Test，找一些最终的用户在研发的环境（开发、测试）上进行测试。游戏内测
### 贝塔测试Beta Test，找一些最终的用户在生产环境上进行测试（灰度发布）游戏
### 用户验收测试User Appectptance Test
# 软件研发流程
# 软件测试的过程
## 测试人员的工作价值是什么？你是怎么理解软件测试的？
# 测试类型
# 测试计划：由测试经理/测试组长编写的，给管理者（项目经理、项目干系人、项目利益相关人）看的，偏管理的文档
- 测试目的：为什么要做这次的测试  
- 测试范围：对哪些需求、功能、缺陷进行测试  
- 测试策略：开展哪些测试，比如产品初期，以功能测试为主；比较成熟的产品，以性能、稳定性、安全为主。  
- 测试环境：硬件设备、软件、测试工具  
- 测试人员：分工、职责  
- 测试准入机制/开发转测试的标准/测试进入条件：达到什么样的程度，可以从开发阶段进入测试阶段  
- 测试退出机制/测试结束的标准：达到什么样的标准，结束测试  
- 测试风险  
    - 人力、进度、技能、环境的风险等。  
#  测试用例  
## 指导测试人员进行测试的文档，属于测试的核心资产。测试资产：测试用例、测试分析文档、测试计划、测试方案、缺陷、脚本。  
## 编写测试用例、设计测试用例，是测试人员基本功。
## 测试点：输入已注册的用户名和正确的密码登录成功。
## 用例设计方法
## 将这个测试点进一步细化为测试用例
## 编写用例的规范（不同的项目有不同的规范，看之前人写的用例的风格，跟之前的保持一致）
## 用例评审 
## 作业：输出 测试分析-思维导图、测试用例-导入禅道，按照测试用例规范进行内部评审。
### 车辆管理
### 客户管理
# 缺陷bug、defect
## 什么样的问题是缺陷、
### 功能有缺失、功能和需求文档不一致、不符合用户使用习惯、异常场景未处理等，都属于缺陷。
## 只要是缺陷都需要放到缺陷管理系统中，跟踪，提交缺陷时，包含哪些内容？
## 测试人员发现一个缺陷，开发认为不是缺陷，怎么处理？

### 需要放到缺陷管理系统（禅道、Excel）中跟踪

### 可以找一些依据（需求文档、同行产品的方式、通常的处理方法）先跟开发沟通，看能不能说服对方

### 向上沟通，找测试经理、开发经理一起讨论这个问题

### 找一些关系比较好的客户，收集客户的意见。

### 沟通记录、时间、结论写到问题单中。方便回溯。

## 测试人员提交了一个非必现（偶现）的问题，开发以复现不了为理由，一直不处理，你怎么办？

### 提交问题单：系统执行的日志、数据库表中的数据、CPU、内存、操作录屏附到缺陷单中。辅助开发人员定位问题的。

### 每隔一段时间，按照之前的步骤复现下，如果复现后， 保留错误的现场，让开发在我们的环境上定位问题。

### 每隔一段时间，进行复现，2个月后仍未复现。缺陷单经过会议评审，可以先降级。致命-->严重-->一般-->提示。

### 降到提示后，如果1~2月仍不复现，可以评审先关闭。

# 测试报告

## 版本、产品测试结束后，输出的测试报告，给管理者看的。测试经理/测试组长写的。

### 用例执行情况

#### 按模块、执行人、结果来统计用例执行情况
### 缺陷解决情况

#### 按模块、提交人、解决状态、严重程度统计

### 测试结论

#### 能不能发布

## 测试人员每天写的报告，测试日报，今天的工作内容、执行了多少用例、发现了多少缺陷、写了多少代码，有没有疑难问题、风险等。

# 参考资料
- 软件测试指南
- 测试流程规范
- 测试用例设计指南
- 缺陷管理手册
- 测试报告模板

# 相关笔记
- [[软件测试]]
- [[测试理论]]
- [[测试流程]]
- [[测试计划]]
- [[测试用例]]
- [[缺陷管理]]
- [[测试报告]]
- [[白盒测试]]
- [[黑盒测试]]
- [[自动化测试]]



















