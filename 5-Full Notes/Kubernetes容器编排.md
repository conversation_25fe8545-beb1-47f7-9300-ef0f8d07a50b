---
created: 2024-12-18
aliases: []
---

状态: [[进行中]]

标签: [[Kubernetes]] [[容器编排]] [[Docker]] [[云原生]] [[微服务]] [[DevOps]] [[Pod]] [[Deployment]] [[Service]] [[集群管理]]

# Kubernetes学习笔记
## 为什么会有K8S
### 容器化部署越来越多，容器故障后，怎么快速新建议一个容器替补故障的容器。
### 应用程序的用户规模较大，后台有若干容器支撑用户使用。用户规模变大后，怎么快速扩容，用户规模减少后，怎么缩容？
### 上述问题都属于容器编排的问题

K8S：谷歌开源出来的一款软件。谷歌内部使用Borg系统管理内部的服务器，K8S
Mesos：Apache组织提供的管理工具
Swarm：Docker官方提供的管理工具。
## K8S，Kubernetes是什么
### K8S是一组服务器的集群，有两类角色：master（领导）、node（工人)
### 一对多：一个master领导，多个node工人（测试环境）
### 多对多：适合生产环境（用户使用的环境）多个master、多个node
### 一对一：适合学习。
## K8S功能：
### 容器自动部署，自动扩容，自动缩容，跨主机管理
### 故障自动恢复的能力，容器故障后，快速创建一个修新的。
### 监控：监控系统、日志收集平台，系统出现问题后能够快速定位。
### 负载均衡：一个服务（tomcat）后台启动了多个容器，能够自动实现请求的负载均衡。
### 版本发布时支持灰度发布、金丝雀发布、蓝绿发布等（自行了解）
### 不论是Java、Go、C++、Python开发的软件，都可以在k8s上运行。
## 好处
### 降低了运维的成本，不用考虑负载均衡器的选型，不用考虑服务器的监控和故障处理的开发等，可以节约成本。
### 降低了底层细节，只用关注业务本身。
搭建集群
Kubeadm官方提供的部署集群的工具。
Kubeadm init 把一台服务器创建成master节点。
Kubeadm 
Kubeadm reser
二进制包
## kubectl工具 控制K8S集群的命令行工具
### kubectl 【command子命令】 【type】 【name】 【可选参数】
#### command子命令
create创建
delete删除
describe查看详细信息
get 查询
apply应用
#### 操作的资源
node节点
namespace、ns命令空间
pod豆荚
delopyment、delopy发布
service、svc 服务
log  日志
## 查询K8S的运行状态
### kubectl get pod -n kube-system  # -n表示 namespace，各节点状态为running即可
### kubectl get pod -n kube-system -o wide  # 可以查看组件运行在那个节点

## pod
### k8s 中管理的最小单位。是豆荚的意思吗，其中运行多个容器
### docker ps | grep etcd 可以查看到pod中运行着多个容器
### kubectl get pod -n # 命名空间
### kubectl delete pod pod名称 -n 删除空间
deployment发布控制器
    K8S集群中有很多的控制器，通过不同的控制器来控制集群的功能，比如命名空间控制器，发布控制器，副本（pod的数量）控制器

## 部署应用
### create deployment的方式
#### kubectl create deployment nginxabc --image=nginx:1.20.2 --replicas 3    通过deployment控制器创建了3个pod，每个pod中运行了一个nginx容器
#### kubectl get pod   查询创建好的pod
#### kubectl expose deployment nginxabc --port=80 --type=NodeProt    把上面的3个pod打包成一个服务，供用户访问
#### kubectl get deployment 查询发布 可简写deploy
#### kubectl get service查询服务 可简写为svc
#### kubectl get pod,svc,deploy  可以一起查询
#### http://*************:30562/   可以访问这个服务
#### kubectl scale deployment nginxabc --replicas=10  扩容
#### kubectl scale deployment nginxabc --replicas=2   缩容
#### kubectl get pod -o wide  可以查看到pod分配给了哪个节点，查看pod的ip地址
	#### curl http://pod ip地址  K8S 内部访问具体的pod

#### kubectl exec -it pod名称 -- /bin/bash  进入pod
#### cd /usr/share/nginx/
#### ls -l
#### exit退出
#### 通过删除一个pod模拟故障：kubectl delete pod nginxabc-5cf.....，系统会自动重建，每次重建，pod的IP地址就变化了，一般不会把pod直接提供给用户去使用。把逻辑相同的pod组成一个整体让用户使用，pod故障，不影响虚拟机IP地址：服务端口，用户的访问方式不变
#### 删除deploy：kubectl delete nginxabc
#### 删除服务：kubectl delete service nginxabc

### 使用create deployment 的方式，部署在命名空间中
#### 命名空间用来隔离环境，搭建了一套K8S系统，开发、测试、运维都在使用，开发、测试、运维都部署了一套租车系统，那怎么区分是谁的环境？
#### 创建命名空间：kubectl create ns kube-test   测试使用   kubectl create ns kube-dev   开发使用
#### 删除命名空间：kubectl delete ns kube-test
##### 将tomcat部署在kube-test中
##### kubectl create ns kube-test
##### kubectl create deployment tom --image=tomcat:8.5.54 --replicas=2 -n kube-test   创建发布
##### kubectl expose deployment tom --port=8080 --type=NodePort -n kube-test   创建服务
##### 进入pod
##### 扩容
##### 缩容
##### kubectl get pod,svc,deploy -n kube-test   查询
##### kubectl delete kube-test 删除命名空间，里面所有内容就一起删除了
## 部署应用
暴露服务的几种方式
kubectl create deploy  xyz --image=nginx:1.20.2
kubectl create depoy xyz -- port=80 --type=xxxx 执行时会报错，xxxx是一个非法的值
clisterIP ：默认方式，不设置--type 时，默认使用这种。这种方式只能在k8s集群内部访问，外部无法访问
ExternalName:需要依赖外部的服务，临时使用。
LoadBalancer: 需要使用云服务商提供的负载服务器，这种方式暴露的服务需要付费
NodePort：K8s集群内部，外部都能访问，最简单的一种方式。一个端口值能提供一个服务，如果端口变化了，虚拟机IP变化，用户的访问方式会有变化。
k8s架构
master领导：管理node、监控、编排、调度集群中各个工作节点。
kubectl get pod -n kube-system -o wide 可以查看运行中的组件
kube-apiserver：操作k8s集群的唯一入口，通过命令，对k8s 中各类资源进行增删改查的等。
kube-controller-manager:控制器管理器，监控k8s集群中各类控制器的健康状况、有故障时及时处理和恢复
deployment controller 发布控制器
namespace controller 命名空间控制器，控制命名窗口的创建

# 参考资料
- Kubernetes官方文档
- 容器编排实践指南
- Docker与Kubernetes权威指南
- 云原生应用架构指南

# 相关笔记
- [[Kubernetes]]
- [[Docker]]
- [[容器编排]]
- [[云原生]]
- [[微服务]]
- [[DevOps]]































