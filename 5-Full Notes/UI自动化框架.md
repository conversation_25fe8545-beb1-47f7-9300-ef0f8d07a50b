---
created: 2024-11-26
aliases: []
---

状态: [[进行中]]

标签: [[自动化测试]] [[UI测试]] [[Selenium]] [[测试框架]] [[WebDriver]] [[测试工具]] [[CSS选择器]] [[浏览器自动化]]

# 自动化测试基础概念

## 自动化测试概述

### 定义

#### 使用工具和脚本代替手工执行测试用例

#### 通过程序实现测试过程自动化

### 应用场景

#### 1.大量重复性测试

#### 2.无人值守测试（如夜间测试）

#### 3.性能和并发测试

#### 4.回归测试

### 核心价值

#### 1.提高测试效率

#### 2.保证测试一致性

#### 3.降低人工成本

#### 4.扩展测试覆盖范围

## 自动化测试分析

### 优势

#### 1.替代大量手工测试

#### 2.支持无人值守执行

#### 3.实现高并发测试

#### 4.保证测试一致性和可重复性

### 局限性

#### 1.无法完全替代手工测试

#### 2.依赖测试人员编码能力

#### 3.测试脚本较为脆弱

#### 4.前期投入较大

### 适用项目特征

#### 1.长期维护的项目

#### 2.需求明确稳定

#### 3.频繁回归测试

#### 4.测试人员具备编码能力

#### 5.项目进度压力可控

## 主流自动化测试工具

### QPT

#### 适用于B/S架构系统

#### 收费工具

#### 使用频率较低

### Robot Framework

#### 基于Python开发

#### 关键字驱动框架

#### 使用表格方式开发

#### 流行度约65%

#### 封装函数作为关键字

### Selenium

#### 开源免费

#### 多语言支持

#### 适用B/S架构

#### 多浏览器支持

#### 跨平台特性

## Selenium架构

### SeleniumIDE

#### Chrome/Firefox插件

#### 支持录制回放

#### 可生成测试脚本

### WebDriver

#### 浏览器操作接口

#### 支持多种编程语言

#### 模拟用户操作

#### 提供完整API

### Selenium Grid

#### 分布式执行

#### 支持大规模测试

#### 多机器并行执行

## WebDriver使用

### 基础配置

#### 导入webdriver

#### 创建浏览器实例

#### 访问网页

### 元素定位方法

#### 1.ID定位

#### 2.Name定位

#### 3.ClassName定位

#### 4.LinkText定位

#### 5.PartialLinkText定位

#### 6.TagName定位

#### 7.XPath定位

#### 8.CSS定位

## CSS选择器

### 基本选择器

#### 标签选择器：input、div、a

#### ID选择器：#id

#### 类选择器：.class

#### 组合选择器

##### 标签+ID：input#kw

##### 标签+类：input.s_ipt

##### 属性选择器：[属性名=属性值]

### 模糊匹配

#### 开头匹配：[属性名^=值]

#### 结尾匹配：[属性名$=值]

#### 包含匹配：[属性名*=值]

#### 分隔匹配：[属性名~=值]

### 层级选择器

#### 子元素：>

#### 后代：空格

#### 兄弟：~

#### 相邻：+

### 特殊选择器

#### first-child

#### last-child

#### nth-child(n)

## 浏览器操作

### 页面控制

#### 打开网页：get(url)

#### 后退：back()

#### 前进：forward()

#### 刷新：refresh()

### 窗口控制

#### 设置大小：set_window_size()

#### 最小化：minimize_window()

#### 最大化：maximize_window()

### 窗口句柄

#### 获取所有句柄：window_handles

#### 获取当前句柄：current_window_handle

#### 切换窗口：switch_to.window()

## 元素操作

### 基本操作

#### 获取文本：text

#### 输入文本：send_keys()

#### 清除文本：clear()

#### 点击：click()

### 状态检查

#### 是否显示：is_displayed()

#### 是否选中：is_selected()

#### 获取大小：size

### 其他操作

#### 截图：get_screenshot_as_file()

## iframe操作

### 切换到iframe：switch_to.frame()

### 返回父级：switch_to.parent_frame()

### 返回主页面：switch_to.default_content()

## 下拉框操作

### 选择方式

#### 通过文本：select_by_visible_text()

#### 通过索引：select_by_index()

#### 通过值：select_by_value()

## 鼠标键盘事件

### 鼠标操作

#### 移动到元素：move_to_element()

#### 右键点击：context_click()

#### 拖放：drag_and_drop()

#### 双击：double_click()

### 键盘操作

#### 按下按键：key_down()

#### 释放按键：key_up()

#### 执行操作：perform()

## WebDriver 高级特性

### 1. 鼠标操作

#### 基本使用

```python
from selenium.webdriver.common.action_chains import ActionChains
# 创建 ActionChains 对象
actions = ActionChains(driver)
```

#### 鼠标悬停

actions.move_to_element(element).perform()

#### 拖放操作

actions.drag_and_drop(source_element, target_element).perform()

#### 右键点击

actions.context_click(element).perform()

#### 双击操作

actions.double_click(element).perform()

### 2. 键盘事件处理

#### 基本使用

```python
from selenium.webdriver.common.keys import Keys

# 文本框输入
element.send_keys("python")

# 组合键操作
element.send_keys(Keys.CONTROL, 'a')  # 全选
element.send_keys(Keys.CONTROL, 'c')  # 复制
element.send_keys(Keys.CONTROL, 'v')  # 粘贴
element.send_keys(Keys.ENTER)         # 回车
```

### 3. 单元测试框架

#### 基本结构

```python
import unittest
from selenium import webdriver

class TestCase(unittest.TestCase):
    def setUp(self):
        self.driver = webdriver.Chrome()

    def tearDown(self):
        self.driver.quit()

    def test_example(self):
        # 测试代码
        pass
```

#### 测试数据管理

```python
from faker import Faker

class TestCase(unittest.TestCase):
    def setUp(self):
        self.fake = Faker(['zh_CN'])
        self.test_data = {
            'username': self.fake.user_name(),
            'email': self.fake.email(),
            'name': self.fake.name()
        }
```

### 4. 最佳实践建议

#### 代码组织

1. 测试用例分类管理
2. 使用Page Object模式
3. 合理使用配置文件
4. 做好异常处理

#### 测试策略

1. 保持测试用例独立性
2. 合理使用断言
3. 注意测试数据清理
4. 控制测试用例粒度

#### 性能优化

1. 使用显式等待代替强制等待
2. 优化元素定位策略
3. 合理使用日志记录
4. 控制资源占用

## 进阶实践 - Day 02

### 1. 键盘事件处理

#### 基本使用

```python
from selenium.webdriver.common.keys import Keys

# 文本框输入
element.send_keys("python")

# 组合键操作
element.send_keys(Keys.CONTROL, 'a')  # 全选
element.send_keys(Keys.CONTROL, 'c')  # 复制
element.send_keys(Keys.CONTROL, 'v')  # 粘贴
element.send_keys(Keys.ENTER)         # 回车
```

### 2. 单元测试框架

#### 基本结构

```python
import unittest
from selenium import webdriver

class TestCase(unittest.TestCase):
    def setUp(self):
        self.driver = webdriver.Chrome()

    def tearDown(self):
        self.driver.quit()

    def test_example(self):
        # 测试代码
        pass
```

#### 测试数据管理

```python
from faker import Faker

class TestCase(unittest.TestCase):
    def setUp(self):
        self.fake = Faker(['zh_CN'])
        self.test_data = {
            'username': self.fake.user_name(),
            'email': self.fake.email(),
            'name': self.fake.name()
        }
```

### 3. 自动化测试实践

#### 登录功能测试示例

```python
def test_login(self):
    # 打开登录页面
    self.driver.get("http://127.0.0.1:80/ranzhi/www")

    # 输入登录信息
    self.driver.find_element(By.ID, "account").send_keys("admin")
    self.driver.find_element(By.ID, "password").send_keys("123456")
    self.driver.find_element(By.ID, "submit").click()

    # 等待登录完成
    sleep(2)
```

#### 添加成员测试示例

```python
def test_add_member(self):
    # 切换到iframe
    iframe = self.driver.find_element(By.ID, "iframe-superadmin")
    self.driver.switch_to.frame(iframe)

    # 点击添加成员
    self.driver.find_element(By.LINK_TEXT, "添加成员").click()

    # 填写表单
    self.driver.find_element(By.ID, "account").send_keys(self.fake.user_name())
    self.driver.find_element(By.ID, "realname").send_keys(self.fake.name())
    self.driver.find_element(By.ID, "email").send_keys(self.fake.email())

    # 提交表单
    self.driver.find_element(By.ID, "submit").click()
```

### 4. 最佳实践建议

#### 测试用例设计

1. 保持用例独立性
2. 合理使用断言
3. 清理测试数据
4. 控制用例粒度

#### 性能优化

1. 使用显式等待替代强制等待
2. 优化元素定位策略
3. 合理使用日志记录
4. 控制资源占用

#### 框架使用建议

1. 测试用例分类管理
2. 使用Page Object模式
3. 合理使用配置文件
4. 做好异常处理

## Selenium进阶

### 元素定位优先级

#### 1.优先使用id属性

#### 2.使用name属性

#### 3.使用class属性

#### 4.使用link text（链接文本）

#### 5.使用CSS选择器（性能好）

#### 6.最后考虑XPath（性能较差）

## Day02 - 自动化测试

### 1. 键盘事件处理

#### 基本使用

```python
from selenium.webdriver.common.keys import Keys

# 文本框输入
element.send_keys("python")

# 组合键操作
element.send_keys(Keys.CONTROL, 'a')  # 全选
element.send_keys(Keys.CONTROL, 'c')  # 复制
element.send_keys(Keys.CONTROL, 'v')  # 粘贴
element.send_keys(Keys.ENTER)         # 回车
```

### 2. 单元测试框架

#### 基本结构

```python
import unittest
from selenium import webdriver

class TestCase(unittest.TestCase):
    def setUp(self):
        self.driver = webdriver.Chrome()

    def tearDown(self):
        self.driver.quit()

    def test_example(self):
        # 测试代码
        pass
```

#### 测试数据管理

```python
from faker import Faker

class TestCase(unittest.TestCase):
    def setUp(self):
        self.fake = Faker(['zh_CN'])
        self.test_data = {
            'username': self.fake.user_name(),
            'email': self.fake.email(),
            'name': self.fake.name()
        }
```

### 3. 自动化测试实践

#### 登录功能测试示例

```python
def test_login(self):
    # 打开登录页面
    self.driver.get("http://127.0.0.1:80/ranzhi/www")

    # 输入登录信息
    self.driver.find_element(By.ID, "account").send_keys("admin")
    self.driver.find_element(By.ID, "password").send_keys("123456")
    self.driver.find_element(By.ID, "submit").click()

    # 等待登录完成
    sleep(2)
```

#### 添加成员测试示例

```python
def test_add_member(self):
    # 切换到iframe
    iframe = self.driver.find_element(By.ID, "iframe-superadmin")
    self.driver.switch_to.frame(iframe)

    # 点击添加成员
    self.driver.find_element(By.LINK_TEXT, "添加成员").click()

    # 填写表单
    self.driver.find_element(By.ID, "account").send_keys(self.fake.user_name())
    self.driver.find_element(By.ID, "realname").send_keys(self.fake.name())
    self.driver.find_element(By.ID, "email").send_keys(self.fake.email())

    # 提交表单
    self.driver.find_element(By.ID, "submit").click()
```

