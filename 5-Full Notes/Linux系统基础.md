---
created: 2024-10-24
aliases: []
---

状态: [[进行中]]

标签: [[Linux]] [[命令行]] [[软件测试]] [[测试用例]] [[测试理论]] [[系统管理]]

# Linux基础命令和软件测试入门

### 1. 用户名设置规则（文件 
- 用户名一旦设置成功后，将无法进行修改。
- 用户名必须包含6到30个字符，可以是字母、数字或下划线，且必须以字母开头。

### 2. ls命令使用示例（文件 1）
- 描述了ls命令的不同用法，包括显示文件大小（`ls -lh`）、长格式显示（`ls -l`）、显示路径等。

### 3. mkdir命令使用示例（文件 3）
- 提供了mkdir命令的用法和示例，包括创建目录、设置权限、创建父目录等。
- 例如：`mkdir -p /data` 用于创建目录并包括父目录。

### 4. vi编辑器使用（文件 4）
- 描述了vi编辑器的基本使用，包括创建或编辑文件、认识vi布局、常用命令和按键。
- 例如：`vi +文件路径` 用于创建或编辑文件。

### 5. Linux安装与远程连接（文件 5）
- 描述了如何在Windows上安装远程连接软件以支持SSH协议连接Linux。
- 提供了Linux命令行操作的基本命令，如`pwd`、`ls`、`cd`、`mkdir`、`rm`等。

### 6. 测试管理表格（文件 6）
- 提供了一个测试管理的表格，包括部署、运维、制品仓库、测试计划、测试设计、测试用例、测试执行等列。

### 7. 等价类和边界值测试（文件 7）
- 描述了等价类和边界值测试的概念和方法。
- 讨论了有效等价类和无效等价类，以及边界值的优先使用方法。

### 8. 递归函数示例（文件 8）
- 提供了一个计算阶乘的递归函数示例，包括基本情况和递归调用的代码。

### 9. 测试用例编写规则（文件 9）
- 描述了新建测试用例的步骤和预期结果。
- 强调了测试步骤描述应准确、详细，可操作性强，确保可以按照步骤正确执行。

### 10. 用户名输入规则（文件 10）
- 如果在用户名输入框中输入不符合规则的内容，输入框会变红，并在输入框下方显示提示信息“用户名只能由字母、数字和下划线组成，必须以字母开头”。

### 11. 软件测试流程（文件 11）
- 描述了软件测试的流程，包括需求分析、功能列表、用户分类、场景梳理、测试设计、编码、测试执行等环节。

### 12. 软件测试流程与理论（文件 12）
- 强调了对行业的认识、思维培养、认知和格局的重要性。
- 介绍了软件测试流程、需求分析、功能列表、用户分类、开发模型、测试设计等概念。

### 13. Linux远程连接（文件 13）
- 描述了如何在Windows上安装支持SSH协议的远程连接软件，以连接到Linux系统。
- 提供了Linux命令行操作的基本命令，如`pwd`（显示当前位置路径）、`ls`（显示当前目录内容）、`cd`（切换目录）、`mkdir`（创建目录）、`rmdir`（删除目录）、`del`（删除文件）、`echo`（显示文本）。

### 14. 测试用例表格（文件 14）
- 提供了一个测试用例的表格模板，包括用例标题描述性、模块、测试数据、前提条件、测试步骤、期望结果、测试类型等列。

### 15. 测试数据设计（文件 15）
- 描述了如何对具体的测试点进行测试设计、场景和测试数据。
- 提到了有效等价类和无效等价类的概念。
- 讨论了正向思维和逆向思维在测试用例设计中的应用。

### 16. 注册用户时的故障现象描述（文件 16）
- 在注册用户时，如果输入的用户名包含了单引号，会提示“用户名已注册”。
- 提供了详细的故障复现步骤，包括在浏览器打开12306网站，点击注册，输入特定格式的用户名，离开输入框或点击空白区域后，提示“用户名已注册”。
- 描述了实际结果与期望结果的差异，期望结果应提示“用户名应该由字母、数字、下划线组成，且以字母开头”。

### 17. 新建测试用例（文件 17）
- 描述了在浏览器地址栏输入华为云登录页面的URL并按回车键后，浏览器应跳转到华为云登录页面，并在登录框中展示“华为账号登录”。
- 输入测试账号后，账号输入框内应显示输入的账号。
- 输入测试账号密码后，密码输入框内应显示与输入密码位数一致的星号。
- 点击“登录”按钮后，应登录成功，跳转到华为云首页，并在网页右上角以红色字体显示用户昵称，昵称与输入账号一致。

### 18. 敏捷开发流程（文件 18）
- 敏捷开发遵循特定的开发流程和测试流程。
- 适用于10人以内的团队。
- 流程包括需求分析、设计、编码、单元测试、打包、迭代、任务分配、执行测试、回归测试等环节。

### 19. Linux安装与管理（文件 19）
- 描述了在Windows系统上使用VMware软件安装Linux操作系统的过程。
- 讨论了Linux系统的管理，包括创建、运行和文件形式的管理。

### 20. 递归的字面意思理解（文件 20）
- 递归这个词由“递”和“归”两个部分组成。
- “递”可以理解为“逐步”或“向下”。
- “归”可以理解为“返回”或“回到原来”。

### 21. Linux安装（文件 2）
- 描述了如何在Windows上安装远程连接软件以支持SSH协议连接Linux。
- 提供了Linux命令行操作的基本命令，如`pwd`、`ls`、`cd`、`mkdir`、`rm`等。

# 参考资料
- Linux系统管理手册
- 软件测试指南
- 敏捷开发手册
- 测试用例设计指南

# 相关笔记
- [[linux全部笔记]]
- [[软件测试]]
- [[Linux系统管理与服务部署]]
- [[Linux环境下的Java项目部署]]
