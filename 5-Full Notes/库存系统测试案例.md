---
created: 2024-11-21
aliases: []
---

状态: [[进行中]]

标签: [[软件测试]] [[功能测试]] [[性能测试]] [[登录功能]] [[Web测试]] [[UI测试]] [[安全测试]] [[库存管理]]

# 库存管理系统登录功能测试用例

# 功能测试
## 用户名
### 验证用户名合法，其他参数正确，登录成功
### 验证用户名为空，其他参数正确，登录失败
### 验证用户名未注册，登录失败
### 验证用户名超过最大长度，登录失败
### 验证用户名是否大小写敏感
## 密码
### 验证用户名正确，密码错误，登录失败
### 验证用户名正确，密码为空，登录失败
### 验证用户名正确，密码为字母+数字+字符 登录成功
# 性能测试
## 单个用户登录时，系统响应时时间要求在3s以内
## 1000个用户同时登录，系统响应时间在3s以内
## 性能测试1000个用户同时登录，后台服务器CPU、内存、磁盘、网络资源占用情况不超过xxx
## 大量用户频繁登录/退出登录，运行一段时间后，是否有内存泄露
# 界面UI
## 浏览器放大、缩小，界面是否会错位
## 界面UI一些常用的快捷键，比如tab键、回车是否能正常使用
## 界面颜色、布局、字体大小、风格是否统一
# 兼容性
## Chrome常见的版本上，功能是否正常
## edge常见的版本上，功能是否正常
## firefox常见的版本上，功能是否正常
# 安全
## 退出登录后，点击浏览器的前进按钮，是否可以继续进行操作
## 密码存储在数据库时，是否加密了，加密算法是否太简单
## 密码不支持复制粘贴，密码不可见
## XSS攻击
## SQL注入
# 异常测试
## 弱网环境下，进行登录
## 弱网情况下，多次点击登录按钮
## 无网络时，提示信息是否正常
