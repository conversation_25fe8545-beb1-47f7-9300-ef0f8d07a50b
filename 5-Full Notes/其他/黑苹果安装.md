---
created: 2024-12-18
aliases: []
sticker: emoji//1f601
---

状态: [[进行中]]

标签: [[黑苹果]] [[系统安装]] [[MacOS]]

# 黑苹果安装

将黑苹果（Hackintosh）安装在移动硬盘上并实现Windows和Mac双系统的步骤如下：
1. 准备工作：
    
    - 硬件要求：你需要一台支持UEFI启动的主板和一个足够大的移动硬盘（建议至少64GB，最好是128GB或更大）。
        
    - 软件工具：
        
        - TransMac 或 balenaEtcher：用于创建启动盘。
            
        - DiskGenius：用于管理分区。
            
        - 下载适配你硬件的macOS镜像文件（.dmg格式），可以从黑果小兵等网站获取。
            
        - 下载适合你硬件的EFI文件，这些文件可以从GitHub等平台找到。
            
2. 制作启动盘：
    
    - 使用TransMac或balenaEtcher将macOS镜像烧录到一个8GB以上的U盘上。
        
3. 移动硬盘的分区：
    
    - 使用DiskGenius将移动硬盘格式化为GUID分区表（GPT）。
        
    - 创建一个用于安装macOS的分区（如300GB），确保分区格式为Mac OS扩展（日志式），以及一个用于Windows的分区（格式为NTFS）。
        
4. 安装macOS：
    
    - 插入你的macOS启动U盘，进入BIOS设置，确保启动顺序将U盘置顶，禁用安全启动（Secure Boot）。
        
    - 启动电脑，选择从U盘启动，进入macOS安装程序。
        
    - 在安装过程中，选择你之前在移动硬盘上创建的分区来安装macOS。
        
5. 安装EFI：
    
    - 安装完成后，进入macOS，替换或添加EFI文件以支持硬件驱动。你可能需要从U盘或其他存储设备加载EFI文件到移动硬盘的EFI分区。
        
6. 安装Windows：
    
    - 用你熟悉的方法创建一个Windows安装盘。
        
    - 重新启动电脑，进入BIOS/UEFI，选择从Windows安装盘启动。
        
    - 在安装过程中，选择移动硬盘上分配给Windows的分区进行安装。
        
7. 配置双系统引导：
    
    - 安装完成后，可能会需要手动配置引导加载程序（如Clover或OpenCore），确保可以从移动硬盘启动到两个系统。
        
    - 在macOS中，你可能需要使用Multibeast或其他工具来安装多引导支持。
        
8. 测试与调整：
    
    - 重启电脑，检查是否能从移动硬盘启动到macOS和Windows。
        
    - 根据实际情况调整EFI文件和引导设置，以确保系统稳定性和兼容性。
        
注意事项：

- 黑苹果的安装可能会遇到硬件兼容性问题，建议在安装前做好充分的硬件和软件兼容性研究。
    
- 由于苹果官方不支持在非苹果硬件上运行macOS，这样的安装可能违反苹果的使用协议。
    


# 参考资料
- 黑苹果安装指南
- MacOS系统配置手册
- 双系统引导教程

# 相关笔记
- [[黑苹果]]
- [[系统安装]]
- [[MacOS]]
- [[Windows]]
- [[双系统]]