---
created: 2024-11-25
aliases: []
---

状态: [[进行中]]

标签: [[HTML]] [[前端开发]] [[Web开发]] [[网页设计]] [[HTML5]] [[表单]] [[CSS]] [[JavaScript]] [[DOM]] [[语义化标签]]

# HTML基础知识总结

## 1. HTML文档基本结构

html

深色版本

```
<!DOCTYPE html>
<html>
<head>
<!-- 头部信息 -->
</head>
<body>
<!-- 主体内容 -->
</body>
</html>
```

## 2. 头部元素

- **`<meta>`**: 定义元数据，如字符集、描述、关键词等
- **`<title>`**: 定义网页标题
- **`<link>`**: 引入外部资源
- **`<style>`**: 定义CSS样式
- **`<script>`**: 定义JavaScript代码

## 3. 基本文本标签

- **标题**: `<h1>` 到 `<h6>`
- **段落**: `<p>`
- **换行**: `<br>`
- **水平线**: `<hr>`

### 文本修饰标签

- **加粗**: `<strong>` 或 `<b>`
- **斜体**: `<em>` 或 `<i>`
- **下划线**: `<u>`
- **删除线**: ``
- **上标**: `<sup>`
- **下标**: `<sub>`

## 4. 特殊符号

- **空格**: `&nbsp;`
- **小于号**: `&lt;`
- **大于号**: `&gt;`
- **与符号**: `&amp;`
- **引号**: `&quot;`（双引号），`&apos;`（单引号）

## 5. 列表

### 无序列表

html

深色版本

```
<ul>
    <li>列表项</li>
</ul>
```

### 有序列表

html

深色版本

```
<ol>
    <li>列表项</li>
</ol>
```

### 定义列表

html

深色版本

```
<dl>
    <dt>术语</dt>
    <dd>描述</dd>
</dl>
```

## 6. 表格

html

深色版本

```
<table>
    <caption>表格标题</caption>
    <thead>
        <tr>
            <th>表头</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>单元格</td>
        </tr>
    </tbody>
</table>
```

## 7. 图片和链接

### 图片

html

深色版本

```
<img src="图片路径" alt="替代文本" title="标题" width="宽度" height="高度">
```

### 链接

html

深色版本

```
<a href="链接地址" target="_blank">链接文本</a>
```

## 8. 表单

html

深色版本

```
<form method="post" action="提交地址">
    <!-- 文本输入 -->
    <input type="text">
    
    <!-- 密码输入 -->
    <input type="password">
    
    <!-- 单选按钮 -->
    <input type="radio">
    
    <!-- 复选框 -->
    <input type="checkbox">
    
    <!-- 下拉列表 -->
    <select>
        <option>选项</option>
    </select>
    
    <!-- 文本域 -->
    <textarea></textarea>
    
    <!-- 文件上传 -->
    <input type="file">
    
    <!-- 按钮 -->
    <button>按钮文本</button>
</form>
```

## 9. 全局属性

- **`id`**: 唯一标识符
- **`class`**: 类名，用于样式
- **`style`**: 行内样式
- **`title`**: 提示文本

# 参考资料
- HTML5规范文档
- MDN Web文档
- W3Schools HTML教程
- HTML最佳实践指南

# 相关笔记
- [[HTML]]
- [[CSS]]
- [[JavaScript]]
- [[前端开发]]
- [[Web开发]]
- [[网页设计]]