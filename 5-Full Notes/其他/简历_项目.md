、
---
created: 2024-12-30 15:25
aliases:
---

状态:

标签:  [[简历/简历/简历要求]]

# 简历_项目
## 多版本简历 
### web 项目简历 （必须）
### 银行项目简历 （ 可选）
### 手机测试简历
#### OPPO 手机整机测试
#### 小众手机整机测试
#### 小众平板测试
### app项目简历
### 车载项目简历
## 选好项目后，再写技能
### web
#### 界面自动化、接口自动化、性能测试
### app
#### adb 命令、弱网测试、手机性能等
### 车载
#### canoe协议、车载相关的
## 项目
### 2024年应届生，至少2个项目
### 2023年毕业生，至少3个项目
### 2022年毕业生，至少4个项目
## 怎么选择
### 之前工作中使用到的web系统、app
### 有朋友、同学在IT行业，开发，测试、使用的web，app
### https://gitee.com  github 上搜索开源的项目，有小公司开发的，商用版、可以根据项目文档进行
### https://www.axureshop.com/  只有前端，没有后台业务，可以看到前端有那些模块，那些功能点
#### 搜关键字
### 不能在简历中写的项目（简历中有这样的项目，企业HR直接拉黑了）：
#### 论坛、商城、医院挂号、影城、交友、教育培训、电网、办公系统、点餐系统
### 同一个简历中，项目跨度不要太大
# 参考资料
# 项目列表

| 项目名称       | 类型  | 代码地址                                                                  | 备注                                                                                                                                                     |
| ---------- | --- | --------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------ |
| 水务管理系统     | web | https://gitee.com/w2017/iotwater                                      | 演示地址：https://water.blazorserver.com/ 用户名：Demo 密码：123456                                                                                                |
| 资产管理系统     | web | https://gitee.com/lank/eam                                            | 演示环境：http://resource.rainbooow.com:8088/login.html 超级管理员账户：admin 密码：123456 固定资产管理员账户：eamadmin 密码：123456 固定资产普通员工账户：eamuser 密码：123456 流程测试账号统一密码：123456 |
| 生产管理系统     | web | https://gitee.com/getrebuild/rebuild                                  | 演示地址：https://nightly.getrebuild.com/ 默认超级管理员用户名密码：admin admin                                                                                          |
| 智能仓库温度监测系统 | web | https://gitee.com/a-little-turtle-running-hard/mqtt                   | -                                                                                                                                                      |
| 电力管理系统     | web | 前端：https://gitee.com/Ynode/ecms_fe 后端：https://gitee.com/Ynode/ecms_be | 地址1：http://vue.zhidian admin/admin123 地址2：http://vue.ruoyi.vip admin/admin123                                                                          |
| 小象智慧美容     | web | https://gitee.com/xiaoxiangopen/xiaoxiang-meiye                       | 地址：http://beauty.weixinai.cn/index 账户：admin 密码：admin123                                                                                                |
| 知识付费系统     | web | https://gitee.com/WanYueKeJi/wanyue-knowledge-payment-UNI             | 教师端：https://demo.sdwanyue.com/teacher 账号：13800000000 密码：123456 后台：https://demo.sdwanyue.com/admin 账号：demo 密码：123456                                    |
| 万岳直播商城     | web | https://gitee.com/WanYueKeJi/wanyue_zhibo_ios                         | 总后台：https://malldemo.sdwanyue.com/admin/login/index 账号：visitor 密码：visitor 商户后台：https://malldemo.sdwanyue.com/shop/index 账号：15711449029 验证码：123456      |
| 云豹直播       | web | https://gitee.com/yunbaokii888/yunbaolive                             | 后台：http://gitlive.yunbaozb.com/admin 账号：admin 密码：visitor                                                                                               |
# 相关笔记