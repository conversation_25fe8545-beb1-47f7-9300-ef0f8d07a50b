---
created: 2024-12-18
aliases: []
---

状态: [[进行中]]

标签: [[learnpython]] [[编程练习]] [[算法]]

# Python编程100题练习

##### [lesson_01](lesson_01.py)
题目：有四个数字：1、2、3、4，能组成多少个互不相同且无重复数字的三位数？各是多少？
##### [lesson_02](lesson_02.py)
题目：企业发放的奖金根据利润提成。
利润(I)低于或等于10万元时，奖金可提10%；
利润高于10万元，低于20万元时，低于10万元的部分按10%提成， 高于10万元的部分，可提成7.5%；
20万到40万之间时，高于20万元的部分，可提成5%；
40万到60万之间时高于40万元的部分，可提成3%；
60万到100万之间时，高于60万元的部分，可提成1.5%，
高于100万元时，超过100万元的部分按1%提成，
从键盘输入当月利润I，求应发放奖金总数？

##### [lesson_03](lesson_03.py)
题目：一个整数，它加上100后是一个完全平方数，再加上168又是一个完全平方数，请问该数是多少？
##### [lesson_04](lesson_04.py)
题目：输入某年某月某日，判断这一天是这一年的第几天？
##### [lesson_05](lesson_05.py)
题目：输入三个整数x,y,z，请把这三个数由小到大输出。
##### [lesson_06](lesson_06.py)
题目：斐波那契数列。
##### [lesson_07](lesson_07.py)
题目：将一个列表的数据复制到另一个列表中。程序分析：使用列表`[:]`。
##### [lesson_08](lesson_08.py)
题目：输出 9*9 乘法口诀表。
##### [lesson_09](lesson_09.py)
题目：暂停一秒输出。
##### [lesson_10](lesson_10.py)
题目：暂停一秒输出，并格式化当前时间。
##### [lesson_11](lesson_11.py)
题目：古典问题：有一对兔子，从出生后第3个月起每个月都生一对兔子，小兔子长到第三个月后每个月又生一对兔子，假如兔子都不死，问每个月的兔子总数为多少？
##### [lesson_12](lesson_12.py)
题目：判断101-200之间有多少个素数，并输出所有素数。
##### [lesson_13](lesson_13.py)
题目：打印出所有的"水仙花数"，所谓"水仙花数"是指一个三位数，其各位数字立方和等于该数本身。例如：153是一个"水仙花数"，因为153=1的三次方＋5的三次方＋3的三次方。
##### [lesson_14](lesson_14.py)
题目：将一个正整数分解质因数。例如：输入90,打印出90=2*3*3*5。
##### [lesson_15](lesson_15.py)
题目：利用条件运算符的嵌套来完成此题：学习成绩>=90分的同学用A表示，60-89分之间的用B表示，60分以下的用C表示。
##### [lesson_16](lesson_16.py)
题目：输出指定格式的日期。
##### [lesson_17](lesson_17.py)
题目：输入一行字符，分别统计出其中英文字母、空格、数字和其它字符的个数。
##### [lesson_18](lesson_18.py)
题目：求s=a+aa+aaa+aaaa+aa...a的值，其中a是一个数字。例如2+22+222+2222+22222(此时共有5个数相加)，几个数相加由键盘控制。
##### [lesson_19](lesson_19.py)
题目：一个数如果恰好等于它的因子之和，这个数就称为"完数"。例如6=1＋2＋3.编程找出1000以内的所有完数。
##### [lesson_20](lesson_20.py)
题目：一球从100米高度自由落下，每次落地后反跳回原高度的一半；再落下，求它在第10次落地时，共经过多少米？第10次反弹多高？
##### [lesson_21](lesson_21.py)
题目：猴子吃桃问题：猴子第一天摘下若干个桃子，当即吃了一半，还不瘾，又多吃了一个第二天早上又将剩下的桃子吃掉一半，又多吃了一个。以后每天早上都吃了前一天剩下的一半零一个。到第10天早上想再吃时，见只剩下一个桃子了。求第一天共摘了多少。
##### [lesson_22](lesson_22.py)
题目：两个乒乓球队进行比赛，各出三人。甲队为a,b,c三人，乙队为x,y,z三人。已抽签决定比赛名单。有人向队员打听比赛的名单。a说他不和x比，c说他不和x,z比，请编程序找出三队赛手的名单。
##### [lesson_23](lesson_23.py)
题目：打印出如下图案（菱形）:
```
   *
  ***
 *****
*******
 *****
  ***
   *
```
##### [lesson_24](lesson_24.py)
题目：有一分数序列：2/1，3/2，5/3，8/5，13/8，21/13...求出这个数列的前20项之和。
##### [lesson_25](lesson_25.py)
题目：求1+2!+3!+...+20!的和。
##### [lesson_26](lesson_26.py)
题目：利用递归方法求5!。
##### [lesson_27](lesson_27.py)
题目：利用递归函数调用方式，将所输入的5个字符，以相反顺序打印出来。
##### [lesson_28](lesson_28.py)
题目：有5个人坐在一起，问第五个人多少岁？他说比第4个人大2岁。问第4个人岁数，他说比第3个人大2岁。问第三个人，又说比第2人大两岁。问第2个人，说比第一个人大两岁。最后问第一个人，他说是10岁。请问第五个人多大？
##### [lesson_29](lesson_29.py)
题目：给一个不多于5位的正整数，要求：一、求它是几位数，二、逆序打印出各位数字。
##### [lesson_30](lesson_30.py)
题目：一个5位数，判断它是不是回文数。即12321是回文数，个位与万位相同，十位与千位相同。
##### [lesson_31](lesson_31.py)
题目：请输入星期几的第一个字母来判断一下是星期几，如果第一个字母一样，则继续判断第二个字母。
##### [lesson_32](lesson_32.py)
题目：按相反的顺序输出列表的值。
##### [lesson_33](lesson_33.py)
题目：按逗号分隔列表。
##### [lesson_34](lesson_34.py)
题目：练习函数调用。

程序分析：使用函数，输出三次 RUNOOB 字符串。
##### [lesson_35](lesson_35.py)
题目：文本颜色设置。
##### [lesson_36](lesson_36.py)
题目：求100之内的素数。
##### [lesson_37](lesson_37.py)
题目：对10个数进行排序。
##### [lesson_38](lesson_38.py)
题目：求一个3*3矩阵主对角线元素之和。
##### [lesson_39](lesson_39.py)
题目：有一个已经排好序的数组。现输入一个数，要求按原来的规律将它插入数组中。
##### [lesson_40](lesson_40.py)
题目：将一个数组逆序输出。
##### [lesson_41](lesson_41.py)
题目：模仿静态变量的用法。
##### [lesson_42](lesson_42.py)
题目：学习使用auto定义变量的用法。(全局变量/局部变量)
##### [lesson_43](lesson_43.py)
题目：模仿静态变量(static)另一案例。
##### [lesson_44](lesson_44.py)
题目：两个 3 行 3 列的矩阵，实现其对应位置的数据相加，并返回一个新矩阵：
```
X = [[12,7,3],
    [4 ,5,6],
    [7 ,8,9]]

Y = [[5,8,1],
    [6,7,3],
    [4,5,9]]
```
##### [lesson_45](lesson_45.py)
题目：统计 1 到 100 之和。
##### [lesson_46](lesson_46.py)
题目：求输入数字的平方，如果平方运算后小于 50 则退出。
##### [lesson_47](lesson_47.py)
题目：两个变量值互换。
##### [lesson_48](lesson_48.py)
题目：数字比较。
##### [lesson_49](lesson_49.py)
题目：使用lambda来创建匿名函数。
##### [lesson_50](lesson_50.py)
题目：输出一个随机数。
##### [lesson_51](lesson_51.py)
题目：学习使用按位与 & 。
##### [lesson_52](lesson_52.py)
题目：学习使用按位或 | 。
##### [lesson_53](lesson_53.py)
题目：学习使用按位异或 ^ 。
##### [lesson_54](lesson_54.py)
题目：取一个整数a从右端开始的4〜7位。
##### [lesson_55](lesson_55.py)
题目：学习使用按位取反~。
##### [lesson_56](lesson_56.py)
题目：画图，学用circle画圆形。
##### [lesson_57](lesson_57.py)
题目：画图，学用line画直线。
##### [lesson_58](lesson_58.py)
题目：画图，学用rectangle画方形。
##### [lesson_59](lesson_59.py)
题目：画图，综合例子。
##### [lesson_60](lesson_60.py)
题目：计算字符串长度。
##### [lesson_61](lesson_61.py)
题目：打印出杨辉三角形（要求打印出10行如下图）。
```
1 
1 1 
1 2 1 
1 3 3 1 
1 4 6 4 1 
1 5 10 10 5 1 
1 6 15 20 15 6 1 
1 7 21 35 35 21 7 1 
1 8 28 56 70 56 28 8 1 
1 9 36 84 126 126 84 36 9 1
```
##### [lesson_62](lesson_62.py)
题目：查找字符串。
##### [lesson_63](lesson_63.py)
题目：画椭圆。
##### [lesson_64](lesson_64.py)
题目：利用ellipse 和 rectangle 画图。
##### [lesson_65](lesson_65.py)
题目：一个最优美的图案。　
##### [lesson_66](lesson_66.py)
题目：输入3个数a,b,c，按大小顺序输出。
##### [lesson_67](lesson_67.py)
题目：输入数组，最大的与第一个元素交换，最小的与最后一个元素交换，输出数组。
##### [lesson_68](lesson_68.py)
题目：有 n 个整数，使其前面各数顺序向后移 m 个位置，最后 m 个数变成最前面的 m 个数
##### [lesson_69](lesson_69.py)
题目：有n个人围成一圈，顺序排号。从第一个人开始报数（从1到3报数），凡报到3的人退出圈子，问最后留下的是原来第几号的那位。
##### [lesson_70](lesson_70.py)
题目：写一个函数，求一个字符串的长度，在main函数中输入字符串，并输出其长度。
##### [lesson_71](lesson_71.py)
题目：编写input()和output()函数输入，输出5个学生的数据记录。
##### [lesson_72](lesson_72.py)
题目：创建一个链表。
##### [lesson_73](lesson_73.py)
题目：反向输出一个链表。
##### [lesson_74](lesson_74.py)
题目：列表排序及连接。
##### [lesson_75](lesson_75.py)
题目：放松一下，算一道简单的题目。
##### [lesson_76](lesson_76.py)
题目：编写一个函数，输入n为偶数时，调用函数求1/2+1/4+...+1/n,当输入n为奇数时，调用函数1/1+1/3+...+1/n
##### [lesson_77](lesson_77.py)
题目：循环输出列表
##### [lesson_78](lesson_78.py)
题目：找到年龄最大的人，并输出。请找出程序中有什么问题。
##### [lesson_79](lesson_79.py)
题目：字符串排序。
##### [lesson_80](lesson_80.py)
题目：海滩上有一堆桃子，五只猴子来分。第一只猴子把这堆桃子平均分为五份，多了一个，这只猴子把多的一个扔入海中，拿走了一份。第二只猴子把剩下的桃子又平均分成五份，又多了一个，它同样把多的一个扔入海中，拿走了一份，第三、第四、第五只猴子都是这样做的，问海滩上原来最少有多少个桃子？
##### [lesson_81](lesson_81.py)
题目：809*??=800*??+9*?? 其中??代表的两位数, 809*??为四位数，8*??的结果为两位数，9*??的结果为3位数。求??代表的两位数，及809*??后的结果。
##### [lesson_82](lesson_82.py)
题目：八进制转换为十进制
##### [lesson_83](lesson_83.py)
题目：求0-7所能组成的奇数个数。
##### [lesson_84](lesson_84.py)
题目：连接字符串。
##### [lesson_85](lesson_85.py)
题目：输入一个奇数，然后判断最少几个 9 除于该数的结果为整数。
##### [lesson_86](lesson_86.py)
题目：两个字符串连接程序。
##### [lesson_87](lesson_87.py)
题目：回答结果（结构体变量传递）。
```python
if __name__ == '__main__':
    class student:
        x = 0
        c = 0


    def f(stu):
        stu.x = 20
        stu.c = 'c'


    a = student()
    a.x = 3
    a.c = 'a'
    f(a)
    print(a.x, a.c)
```
##### [lesson_88](lesson_88.py)
题目：读取7个数（1—50）的整数值，每读取一个值，程序打印出该值个数的＊。
##### [lesson_89](lesson_89.py)
题目：某个公司采用公用电话传递数据，数据是四位的整数，在传递过程中是加密的，加密规则如下：每位数字都加上5,然后用和除以10的余数代替该数字，再将第一位和第四位交换，第二位和第三位交换。
##### [lesson_90](lesson_90.py)
题目：列表使用实例。
##### [lesson_91](lesson_91.py)
题目：时间函数举例1。
```python
if __name__ == '__main__':
    import time
    print(time.ctime(time.time()))
    print(time.asctime(time.localtime(time.time())))
    print(time.asctime(time.gmtime(time.time())))
```
##### [lesson_92](lesson_92.py)
题目：时间函数举例2。
```python
if __name__ == '__main__':
    import time

    start = time.time()
    for i in range(3000):
        print(i)
    end = time.time()

    print(end - start)
```
##### [lesson_93](lesson_93.py)
题目：时间函数举例3。
```python
# Python 3.8 已移除 clock() 方法 可以使用 time.perf_counter() 或 time.process_time() 方法替代。
if __name__ == '__main__':
    import time
    start = time.clock()
    for i in range(10000):
        print(i)
    end = time.clock()
    print('different is %6.3f' % (end - start))
```
##### [lesson_94](lesson_94.py)
题目：时间函数举例4,一个猜数游戏，判断一个人反应快慢。
```python
if __name__ == '__main__':
    import time
    import random

    play_it = input('do you want to play it.(\'y\' or \'n\')')
    while play_it == 'y':
        c = input('input a character:\n')
        i = random.randint(0, 2 ** 32) % 100
        print('please input number you guess:\n')
        start = time.process_time()
        a = time.time()
        guess = int(input('input your guess:\n'))
        while guess != i:
            if guess > i:
                print('please input a little smaller')
                guess = int(input('input your guess:\n'))
            else:
                print('please input a little bigger')
                guess = int(input('input your guess:\n'))
        end = time.process_time()
        b = time.time()
        var = (end - start) / 18.2
        print(var)
        # print('It took you %6.3 seconds' % time.difftime(b,a)))
        if var < 15:
            print('you are very clever!')
        elif var < 25:
            print('you are normal!')
        else:
            print('you are stupid!')
        print('Congradulations')
        print('The number you guess is %d' % i)
        play_it = input('do you want to play it.')
```
##### [lesson_95](lesson_95.py)
题目：字符串日期转换为易读的日期格式。
##### [lesson_96](lesson_96.py)
题目：计算字符串中子串出现的次数。
##### [lesson_97](lesson_97.py)
题目：从键盘输入一些字符，逐个把它们写到磁盘文件上，直到输入一个 # 为止。
##### [lesson_98](lesson_98.py)
题目：从键盘输入一个字符串，将小写字母全部转换成大写字母，然后输出到一个磁盘文件"test"中保存。
##### [lesson_99](lesson_99.py)
题目：有两个磁盘文件A和B,各存放一行字母,要求把这两个文件中的信息合并(按字母顺序排列), 输出到一个新文件C中。
##### [lesson_100](lesson_100.py)
题目：列表转换为字典。

# 参考资料
- Python编程指南
- 算法练习题集
- Python标准库文档

# 相关笔记
- [[learnpython]]
- [[编程练习]]
- [[算法]]
- [[数据结构]]
- [[递归]]
- [[字符串处理]]
- [[数组操作]]