# Java基础

## Day01

### 1. 基础语法（Demo01）
#### 注释
- 单行注释 //
  - 用于简单说明
  - 只注释一行
- 多行注释 /* */
  - 可以跨多行
  - 不能嵌套使用
- 文档注释 /** */
  - 用于生成API文档
  - 可以添加@author等标记

#### 关键字
- public：公共的访问修饰符
- class：定义类
- static：静态修饰符
- void：空返回值
- 所有关键字都是小写
- 不能用作标识符

#### 标识符
- 命名规则
  - 由字母、数字、_、$组成
  - 不能以数字开头
  - 不能是关键字
- 命名规范
  - 类名：首字母大写，驼峰命名
  - 变量名：首字母小写，驼峰命名
  - 方法名：首字母小写，驼峰命名
  - 常量名：全部大写，下划线分隔

### 2. 变量（Demo02）
#### 变量定义
- 格式：数据类型 变量名 = 初始值;
- 先声明后使用
- 作用域在{}内

#### 基本数据类型
- 整数型
  - byte：1字节，-128~127
  - short：2字节，-32768~32767
  - int：4字节（默认），-2^31~2^31-1
  - long：8字节，-2^63~2^63-1，需要加L

- 浮点型
  - float：4字节，需要加F
  - double：8字节（默认）
  - 精度：double > float

- 字符型
  - char：2字节
  - 存储单个字符
  - 使用单引号
  - 可以存储ASCII码

- 布尔型
  - boolean：1字节
  - 只有true和false两个值

### 3. 数据类型转换（Demo03）
#### 自动类型转换
- 转换规则
  - byte → short → int → long → float → double
  - char → int
- 小范围向大范围转换
- 不会损失精度
- 系统自动完成

#### 强制类型转换
- 语法：(目标类型)被转换数据
- 大范围向小范围转换
- 可能损失精度
- 可能发生溢出
- 需要手动完成

#### 特殊情况
- byte/short/char运算时自动提升为int
- 不同类型运算，结果为最大类型
- boolean类型不能转换为其他类型

### 4. 运算符（Demo04）
#### 算术运算符
- 基本运算：+, -, *, /, %
  - +：加法，字符串连接
  - -：减法
  - *：乘法
  - /：除法（整数相除结果取整）
  - %：取余
- 自增自减：++, --
  - 前缀：++a 先自增再使用
  - 后缀：a++ 先使用再自增

#### 赋值运算符
- 基本赋值：=
- 复合赋值：+=, -=, *=, /=, %=
  - a += b 等价于 a = a + b
  - 包含了强制类型转换

#### 关系运算符
- 大小比较：>, <, >=, <=
- 相等比较：==, !=
- 结果都是boolean类型

#### 逻辑运算符
- &&：与（短路）
  - 第一个为false就不再判断第二个
- ||：或（短路）
  - 第一个为true就不再判断第二个
- !：非
- 结果都是boolean类型

#### 位运算符
- 左移运算符 <<
  - 每左移1位相当于乘以2
- 右移运算符 >>
  - 每右移1位相当于除以2

#### 三元运算符
- 格式：条件 ? 表达式1 : 表达式2
- 条件为true取表达式1
- 条件为false取表达式2

## Day02

### 1. 一维数组（Demo01）
#### 数组定义
- 声明方式
  - 数据类型[] 数组名
  - 数据类型 数组名[]
- 创建方式
  - new 数据类型[长度]
  - {元素1, 元素2, ...}
- 初始化方式
  - 动态初始化：指定长度
  - 静态初始化：指定元素

#### 数组访问
- 索引访问
  - 数组名[索引]
  - 索引从0开始
  - 范围：0 ~ length-1
- 属性访问
  - 数组名.length
  - 获取数组长度
  - 长度是固定的

#### 数组遍历
- 普通for循环
  - for(int i = 0; i < arr.length; i++)
  - 可以获取索引
  - 可以修改元素
- 增强for循环
  - for(元素类型 变量名 : 数组名)
  - 简化数组遍历
  - 只能获取元素

### 2. 二维数组（Demo02）
#### 数组定义
- 声明方式
  - 数据类型[][] 数组名
  - 数据类型 数组名[][]
- 创建方式
  - new 数据类型[行数][列数]
  - {{元素1,元素2},{元素3,元素4}}
- 初始化方式
  - 动态初始化：指定行列数
  - 静态初始化：指定元素值

#### 数组访问
- 元素访问
  - 数组名[行索引][列索引]
  - 两个索引都从0开始
- 属性访问
  - 行数：数组名.length
  - 列数：数组名[i].length

#### 数组遍历
- 嵌套for循环
  - 外循环控制行
  - 内循环控制列
- 增强for循环
  - 外层遍历行数组
  - 内层遍历列元素

### 3. 数组算法（Demo03）
#### 基础算法
- 最值查找
  - 定义变量记录最值
  - 遍历比较更新
- 数组反转
  - 首尾交换
  - 循环length/2次

#### 排序算法
- 冒泡排序
  - 相邻元素比较交换
  - 每轮将最大值冒泡到末尾
- 选择排序
  - 每轮选择最小值
  - 与首位置交换

## Day03

### 1. 类和对象（Demo01）
#### 类的定义
- 类的组成
  - 成员变量：属性
  - 成员方法：行为
  - 构造方法：初始化
- 访问修饰符
  - public：公共访问
  - private：私有访问
  - protected：保护访问
  - default：默认访问

#### 对象的创建
- new关键字
  - 分配内存空间
  - 调用构造方法
  - 返回对象引用
- 构造方法
  - 与类名相同
  - 无返回值
  - 可以重载

#### 对象的使用
- 访问成员变量
  - 对象名.变量名
  - 遵循访问权限
- 调用成员方法
  - 对象名.方法名()
  - 传递必要参数

### 2. 封装（Demo02）
#### 概念理解
- 隐藏实现细节
- 提供公共访问方式
- 提高代码安全性

#### 实现方式
- private关键字
  - 私有成员变量
  - 私有成员方法
- getter/setter方法
  - 获取私有成员变量
  - 设置私有成员变量

### 3. 构造方法（Demo03）
#### 基本特征
- 方法名与类名相同
- 无返回值声明
- 创建对象时调用

#### 注意事项
- 默认构造方法
  - 系统自动提供
  - 无参构造
- 构造方法重载
  - 参数个数不同
  - 参数类型不同