---
created: 2024-12-25 14:22
aliases:
---

状态:

标签:[[性能测试]]

# 性能测试基础
概念：
 1. 性能测试是性能测试就是通过特定的方式对被测系统按照一定测试策略施加压力，获取该系统的响应时间、TPS、吞吐量、资源利用率等指标，来检测系统上线后能否满足用户需求的过程。
 2. 性能测试的目的：性能测试的目的是评估当前系统的性能，预测系统以后的性能，找到系统的瓶颈点，进行调优优化。
## 性能测试时，测试多大的并发？
1. 需求文档中有明确要求，按照需求文档的要求测试。测试：1000并发，1200，1500并发。
2. 需求文档中没有要求，
评估当前系统：检测系统性能，评估系统性能。类似体检，对系统的性能状况有一个了解

基准测试
产品之前没有做过性能测试
之前在V1R1版本上，使用xx软件、硬件、网络环境、测试一组数据（基线）
V1R2版本上，使用xx软件、硬件、网络环境下测试一组数据，与之前的基线相比，看性能是优化还是劣化
针对资源的使用
CPU
内存
磁盘I/O
网络I/O
不同系统，性能指标不同
登录接口：响应时间3s以内

# 参考资料

# 相关笔记