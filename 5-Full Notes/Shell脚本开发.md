---
created: 2024-10-29
aliases: []
---

状态: [[进行中]]

标签: [[Shell]] [[Linux]] [[编程练习]] [[Shell命令]] [[命令行]]

# Shell脚本编程：猜数字游戏实现

## shell命令
### 
## 写一个猜大小的数字游戏
## 需求分析
### 功能有 生成一个1-100的随机数，在屏幕上输入一次，并且判断输入值与随机数的大小，接下来需要重复输入，与生成的这一个随机数比较大小，如果输入的值比随机值大了，那么输出：你猜大了，小了的话输出：你猜小了，刚好等于的话就输出：你猜对了
#### secret_number=$((RANDoM % 100 + 1))
### 一个能够在屏幕上输入的值
#### read input_var
### 首先使用if语句列出程序的主体功能代码实现
#### if ["$input_var"-eq "$secret_number"];then
#### echo"你猜对了！"
#### elif [ "$input_var" -lt "$secret_number" ]; then
#### echo"你猜小了。"
#### else
#### echo"你猜大了。"
#### fi
### 以上代码已经可以在屏幕上输入一次，并且判断输入值与随机数的大小，接下来需要重复输入，与生成的这一个随机数比较大小，使用while语句
#### while [ ]; do  {……} done
### 在使用while中需要一个判断语句，来判断当输入数字的次数与规定次数之间的关系
#### $attempt -1t $attempt_ma  
#### 其中attempt以及attempt 需要在开头定义  attempt=0    attempt_max=3  这样就可以在输完第三次时停下如果在三次以内输对了，那么需要停止再次输入，并输出你猜对了的结果 需要在 echo "你猜对了！ "的下一行加上break命令
三次以内没有输对就重复执行  ((attempt++)) 来增加尝试次数
## 代码实现

# 参考资料
- Shell编程指南
- Linux命令参考
- Shell脚本示例

# 相关笔记
- [[linux全部笔记]]
- [[Linux系统管理与服务部署]]
- [[Linux基础命令和软件测试入门]]


  


