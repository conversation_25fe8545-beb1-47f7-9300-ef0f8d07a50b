---
created: 2024-10-24
aliases: []
---

状态: #进行中

标签: [[Linux]] [[操作系统]] [[命令行]] [[系统管理]]

# Linux系统学习笔记

## linux系统

### 课程目标

```mysql
# 掌握linux系统部署 - 安装配置linux，独立搭建linux环境以及测试环境(动物实验管理系统)
# 掌握linux系统命令 - 命令行使用命令操作软件，配置软件，运行，停止等等操作
# 掌握linux部署项目，部署应用程序(nginx，redis(key-value, 内存型), minio,mysql，java开发应用程序)
# 理解shell编程 - 基于shell开发脚本完成项目启动
```

### 虚拟化技术

- vmware (esxi)
- linux开源 kvm(kernel virtual machine)
- 集群化(多台机器)
- openstack
- SaaS， PaaS（Platform as a Service）， IaaS（虚拟机）

### VMware Workstation

- 安装

- 常用操作

  ```shell
  ctr+alt -- 释放鼠标，让虚拟机释放鼠标
  ```

#### 安装Linux系统

```shell
# 遵照安装向导
# 分区
# 挂载点 - 给分区起一个名字一样，让我们清楚目标分区放在哪里了、
# /boot - 启动分区，引导操作系统启动程序，都放在这里，当计算机上电后，将这部分程序放到内存中，开始引导操作系统启动，直到启动完成
# swap - 交换分区，当程序暂时不需要了，但是又不退出，将程序的数据转移到硬盘上，然后当你再需要的时候，再从硬盘放到内存中，存放这些数据的地方，或者分区，称之为swap，交换分区；大小一般是内存的2倍。
# / - 根分区，这个分区是linux系统的根部，让所有的分区挂载这个分区下面
```

### 连接linux

```shell
# 1. 通过命令 - ssh
# 2. 软件 - mobaxterm， 实际上是对ssh进行了包装，
# 远程 - 通过ip连接远程机器
# 要素 - 协议,ip地址，端口号,账号(用户名，密码)
ssh -p 22 root@ip地址
ssh root@ip地址

# 软件
```

### 命令

#### ip a

```shell
# 查看ip地址
1: lo: <LOOPBACK,UP,LOWER_UP> mtu 65536 qdisc noqueue state UNKNOWN group default qlen 1000
    link/loopback 00:00:00:00:00:00 brd 00:00:00:00:00:00
    inet 127.0.0.1/8 scope host lo
       valid_lft forever preferred_lft forever
    inet6 ::1/128 scope host 
       valid_lft forever preferred_lft forever
2: ens33: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc pfifo_fast state UP group default qlen 1000
    link/ether 00:0c:29:db:1a:b1 brd ff:ff:ff:ff:ff:ff
    inet **************/24 brd ************** scope global noprefixroute dynamic ens33
       valid_lft 625762sec preferred_lft 625762sec
    inet6 fe80::474f:219:19d9:2f3f/64 scope link noprefixroute 
       valid_lft forever preferred_lft forever
```
#### ping

```shell
ping 地址(域名或者ip地址)
# 判断网路通不通
# 判断网络环境好坏， time内网<1ms 50ms, 太大发生请求超时
# TTL(生存时间)，判断操作系统大体分类，windows 128， linux 64, 值在网络中如果遇到一次转发，TTL就要减1，直到0
# 为零之后，将会被在网络中抛弃，不要占用网络资源
```
#### 格式

```shell
command 选项(参数) 对象
# 使用空格
```

#### 命令帮助

```shell
command --help
command subcommand help
ip a help
man command
```

#### 目录结构

从CentOS 7 开始就不区分系统是32位还是64位了，全部为64位

- /home:：用户目录

- /bin , /sbin , /usr/bin , /usr/sbin: 这些目录下都是存的咱们经常用的命令

- /bin ：目录下，root和普通用户可以使用的命令

- /sbin：目录下，一般只是root用户才能使用的命令s代表super 超级用户的意思

- /boot：系统启动相关的文件

- /dev：linux系统中特有的设备文件，例 光盘，鼠标，硬盘等

- /etc：系统配置文件，咱们经常编辑的配置文件都在此目录下

- /lib和/lib64：存放系统的库文件，例：ls

- 如何查看某个命令依赖什么库呢？

  `#ldd /bin/ls`

- /media：媒介目录，通常为空，插一个U盘，默认挂载在/media下

- /mnt：也是一个临时挂载的目录，通常为空，临时挂载光驱之类的

- /proc： 系统启动的一些进程文件

- /run：产生的一些临时文件。已重启或关机就消失的文件

- /srv：service的缩写，一般存放些服务产生的文件

- /sys：系统内核存放的文件，不太关注

- /tmp：临时目录

- /usr：系统用户存放的地方

- /var：存放日志的地方，例：#/var/message   存放系统日志的地方


咱们经常用的也就是 /bin, /sbin, /usr/bin, /usr/sbin, /etc, /var, /usr/local, /proc

**参考**：[CentOS目录结构详细](https://cloud.tencent.com/developer/article/1497078)

#### pwd

```shell
# 打印当前工作的目录或者路径
pwd
# 结果是返回整个目录的路径
```

#### ls

```shell
ls
# 默认情况下，列出当前目录中的内容（文件以及目录）
ls -lh
# 默认情况显示当前目录中内容和大小，但是依长格式，显示信息更多
```

#### cd

```shell
# 类似于windows下的cd命令
cd /var/log
cd /
cd /opt
# 确认切换后的位置
```



####  路径

```shell
# windows 路径带盘符和冒号 比如：c:\user\wxf\目录
# linux路径没有盘符的概念 /usr/wxf/目录
# 相对路径和绝对路径
# . 表示当前位置， .. 表示当前位置的父级目录
```

#### tab键

```shell
# 命令 - 使用命令的时候敲tab有提示的功能，如果能够提示出来，说明你敲的正确的，反之，错误
# 路径方面 - cd /var/
# 提高敲命令和路径的速度以及准确率
```

#### mkdir

```shell
# 在你的系统的根路径下创建一个你自己知道目录，以后别在外边玩
```

#### rm

```shell
rm -rf 路径
# 路径是文件或者是目录的，一般给给绝对路径
# -r 含义是递归
# 使用rm -r 删除/data/d1目录，理解什么是递归
```

#### vi

```shell
# vi或者vim
vi 文件的路径或者名字 /opt/filename
# vi的对象必须是文件，不能vi目录
# 如果路径中没有这个文件，那么就创一个新的文件，你都给名字作为文件名字
# 状态行 - 在编辑区域的下面
# 按i - 写入内容 - 编辑内容,正常使用输入法编写内容或者代码
# 按esc - 退出编辑模式
# 命令行 - 按下esc后，输入冒号: ，在命令行在输入wq (w - write and q - quit)
# 放弃修改 - 命令行 - 按下esc后，输入冒号: ，在命令行在输入 q! (放弃修改并退出)

# 练习写入200字内容或者英语一段话
# 循环完成 保存退出，输入不保存退出，重复5次

# 查找和替换
:s/vivian/sky/         #替换当前行第一个 vivian 为 sky
:s/vivian/sky/g     #替换当前行所有 vivian 为 sky
:n,$s/vivian/sky/     #替换第 n 行开始到最后一行中每一行的第一个 vivian 为 sky
:n,$s/vivian/sky/g     #替换第 n 行开始到最后一行中每一行所有 vivian 为 sky
（n 为数字，若 n 为 .，表示从当前行开始到最后一行）

:%s/vivian/sky/        #（等同于 :g/vivian/s//sky/） 替换每一行的第一个 vivian 为 sky
:%s/vivian/sky/g    #（等同于 :g/vivian/s//sky/g） 替换每一行中所有 vivian 为 sky
# % 表示所有行
# 正则表达式可能类似于\(\d\{4\}\)-\(\d\{2\}\)-\(\d\{2\}\)，但请注意，vi/vim的正则表达式语法可能与某些其他工具（如sed或grep）略有不同。
# 替换命令可能需要结合使用&（代表整个匹配项）和\1、\2、\3（分别代表第一个、第二个、第三个括号内的匹配项）来实现格式转换。例如，:%s/\(\d\{4\}\)-\(\d\{2\}\)-\(\d\{2\}\)/\3-\2-\1/g（但请注意，vi/vim中直接使用\d可能不工作，你可能需要使用[0-9]来匹配数字）。
%s/\(.*wangxiaofeng.*\)\(wangxiaofeng\)/\1wxf/g
```

#### cat

```shell
# 查看文件的内容，主要看的文本文件
cat -n /data/fileanme
```

#### cp

```shell
# 源对象和目的对象， 附带复制并重命名
# 文件到文件
cp file1 file2
# file1 带路径

# 文件到目录
cp file1 ./d1/file11
# 1. 目录没有文件
# 2. 目录中有同样的文件

# 目录到目录
cp -r 目录1 目录2
```

#### mv

```shell
# 移动 - 剪切，相当于源文件移动在原来位置，将不存在
# 待会练习的时候，完成mv练习，用法和cp一样
```



####  别名-alias

```shell
# 查看系统中已有的别名
alias
# 创建一个别名
alias cp='cp -i'

# 取消一个别名
unalias cp
```

#### ln 链接

```shell
# 路径必须使用绝对路径
# 创建硬链接，不生成新的index号，但是会生成新的文件，自然删除源文件，不影响硬链接的文件
# 硬链接源内容不允许是目录
ln 源文件 目标

# 软连接生成新的index号，但是不会生成新的文件，一旦源文件删除，链接文件也就不存在
ln -s 源文件 目标

# 任务是，创建两个链接，验证给源文件中添加新的内容，查看内容是否存在两个文件中
# 查看索引号
ls -li 文件或者目录
```

#### find

```shell
# 1. 找文件，找目录
# 2. 找到符合条件的文件或者目录后干什么呢？
find 找的路径 -type [f|d] -name "xx"
# 找的路径，就是在哪找，指定查找的位置
# f|d表示找的类型是文件还是目录， f - file， d - directory
# -name 给查找对象大概的名字
# 找文件
find / -type f -name "*file*"
# 找目录
find / -type d -name "*file*"

# 找到指定的文件或者目录，确认内容后，先执行ls然后再执行删除文件操作。
# 实验出-ok是什么意思，应该放在哪？
find /data -type f -name "filename2" -exec rm -rf {} \;

find / -type d -name "a" -exec echo "Directory: {}" \; -exec ls -l {} \;
# 如果查找的目录，同时要执行ls -l 这个命令，无法看到目录的名字
# 需要增加一个命令用来显示目录的名字
# -exec echo "找到的目录是:{}" \;

find / -type d -name "a" -exec echo "找到的目录是: {}, 目录中的内容如下:" \; -exec ls -l {} \;
```

#### grep - 掌握

```shell
# 类似于findstr
# 在类似于文本文件的内容中查找指定的内容，指定的内容可是一种匹配规则，规则称为正则表达式(regular expression)
# 在指定的内容中，查找指定的内容(pattern)，符合一定规则的表达式
# 基本用法
grep -i "hello world" filename # filename是一个文件，文件是一个完整的路径
```

##### 正则表达式

```shell
# 正则表达式 - 规则，使用一些特殊的符号表示了特殊的含义
# ^ - 依什么开头，放在什么开头的前面
# $ - 依什么结尾，放在什么结尾的后面
# * - 表示前面的一个字符出现了0次或者无限次，比如5*， sfdjs，sdk555555555555555555
# . - 表示任意的字符，当前位置是个任意的字符,必须有个字符，字符个数一个
# ? - 表示前面的字符出现了0次或者1次
# + - 表示前面的字符出现了1次或者多次
# 使用正则表达式查找（扩展）目的是练脑
grep -e "正则表达式的规则" 文件
grep -e "^th" demo # 表示以th开头的内容进行匹配
grep -e "^th." demo # 表示以th开头后边是任意一个符号开头的
grep -e "^[0-9]" demo # 表示以数字开头的内容 []表示了取值范围 0-9之间任意一个数字
grep -e "[^0-9]" demo # ^放在[]里边的时候，表示取反，找出不是数字的内容
grep -e "\".*\"" demo # 找出引号里边以及引号的内容
# grep 支持的正则表达式 基本，扩展，-P 
# 找出文章中我爱你出现1-3次
grep -E "(我爱你){1,3}" demo
grep -P "(我爱你){1,3}" demo
grep -e "我\{2\}" demo # -e不支持整体作为一个内容 {}得加斜杠

[root@wxf data]# grep "\\\\" /data/mydemo
this line contain the character \;
[root@wxf data]# grep "\\$" /data/mydemo
this month I earn 100$.

```

![](D:\courses\00-课程截图\正则表达式.jpg)

```shell

```

##### 贪婪匹配

```python
# 有一个字符串102300， 分成 1023,00如何完成？
(\d+?)(0*$)
# ?的作用是防止规则贪婪的匹配，当后边的规则也需要满足的时候，匹配会满足大家所有的规则
```

#### 管道符

```shell
# 管道符 |
# 连接两个命令，一个命令的结果转为另一个命令输入
systemctl list-units | grep ssh
```

#### systemctl

```shell
# 系统控制类的执行
systemctl list-units

# 查看服务的状态,注意你安装软件不是所有的软件都会创建服务的。
# 比如服务名字firewalld
systemctl status 服务的名字

# 启动，停止
systemctl start 服务的名字
systemctl stop 服务的名字
systemctl restart 服务的名字

# 需要系统启动后运行服务，启用开机启动
systemctl enable 服务的名字
# 需要系统启动后禁制服务运行，不想启用开机启动
systemctl disable 服务的名字

# 请问使用systemctl disable后，什么时候有效果？对当前服务的运行是否有影响？
```

#### xargs

```shell
# 将接收到的内容字符串等等，变成命令执行的参数
# 默认使用空格作为定界符
echo "one two three" | xargs mkdir
# 自定义定界符
echo "abcbd" | xargs -d "b" mkdir
# -d就是定义边界符

echo -n "abcbd" | xargs -d "b" mkdir
# echo -n 参数的作用就是不输出尾随的换行符
echo -e "wxf\n123"

# 完成自定义定界符，同时完成命令的操作

# 通过xargs传递多个参数
[root@localhost data]# echo "source target" | xargs -n2 sh -c 'echo $1 to $2' sh
source to target
[root@localhost data]# echo "source target" | xargs -n2 sh -c 'echo $0'
source
[root@localhost data]# echo "source target" | xargs -n2 sh -c 'echo $0 to $1'
source to target
[root@localhost data]# echo "source target" | xargs sh -c 'echo $0 to $1'

# 可以可以find后使用管道符
find /data -type f -name "filename" | xargs -I x grep -E "(我恨你){3}" x
# 解释这调命令的含义是什么？

find /data -type f -name "*.txt" | xargs -I filename sh -c 'printf "filename:\n; `grep wangxiaofeng filename`"; printf "\n"'
```

#### 查看端口号

```shell
# 在同一个地址，需要区分不同的程序，需要使用数字编码，这个编码称为端口号
# 端口号能重复吗？ - 在同一个地址上是不能重复，要就是设置程序的端口号时候，不能冲突
# 如何判断端口号有没有人在使用？
ss -lnp | grep 端口号
# 如果能够列出内容，说明此端口号已经被程序使用
# 
netstat
```

#### 查找进程号

```shell
ps -ef
# -e all process
# -f full format

ps -ef | grep 程序的名字
```



#### 杀进程

```shell
kill -9 pid
# 9 是信号数字，强制退出。
# pid怎么来
```

#### 如何判断服务的状态？

```shell
# 使用所有学习过的命令，判断服务的状态？ 正在运行，dead
```

#### 访问不到服务的时候，如何处理？

```shell
# 1. 服务一般部署在linux，一般在不在自己的身边，远程部署的
# 2. 判断网络是否畅通， ping对方的服务的ip地址
# 3. 如果网通了，依然无法访问，那么就要查看服务的状态？
# 3.1 查看端口是否被占用， ss(netstat), ss -lnp | grep 目标端口号
# 3.2 ss 查看它的进程，确定进程的名字，是不是自己想要的，如果是那么说明端口号正常，没有被占用
# 4. 查看服务的状态，正常，重新启动服务。
# 4.1 systemctl，
# 4.2 还有其他的命令的方式和ps查看进程，启动命令是否正常
# 5. 依然有问题，需要查看服务为什么启动有异常，看日志
```



### 用户和权限

#### 用户(user)和组(group)

```shell
# 用户和组是什么关系？用户隶属于组
# 添加用户
useradd wxf

# 添加组
groupadd testing

# 更改用户所在的组
usermod -g testing wxf
# -g 指定新的组
# testing组的名字
# wxf是要改的用户

# su - 修改当前登录的用户，切换用户
su wxf
exit # 退出当前登录用户

# 获取某个组内的所有的用户
grep -E "root" /etc/group | cut -d: -f3 | xargs -I id grep -E ":id:" /etc/passwd | cut -d: -f1

# 如何查看用户所在组
groups wxf
id wxf
getent passwd wxf | cut -d: -f4 | xargs -I {} getent group {}

# 将用户追加到一个组
usermod -aG testing wxf
```

#### 权限

```shell
# 读（r），写（w），执行（x）
# 分类：用户所拥有的权限；组的权限;其他
# a - all, g- group,u-user,o - other;

# 控制权限
# 添加权限
chmod g+w 对象(目录或者文件)
# + 添加 
# - 删除，取消

# 更改所有者（拥有者），用户和组
# 修改拥有者
# /data - root
chown wxf /data
# 把根路径下的data目录的所有者变更为wxf
# 修改组
chgrp testing /data

# 同时修改用户以及组
chown wxf:testing /data

# 0 - 全无，u,g,o权限没有了 rwx - 4 2 1
# 1 - x
# 2 - w
# 3 = 1 + 2 = wx
# 4 - r
# 5 = 1 + 4 = rx
# 6 = 4+ 2 = r + w
# 7 = 4+2+1=  r+w+x
# 一般赋权限的时候给三个数字
# 第一个位置所有者，第二个所属组，第三个其他
77
# 777
```



#### 练习

```shell
# 创建两个用户
# 让root用户将/data的修改权限添加给两个用户
# 每个用户在目录中间一个文件 a.txt, b.txt,让另一个用户进行r和w操作？
```

### linux项目部署

#### linux安装程序

```shell
# windows - windows 安装程序包， exe, msi 安装向导；解压缩直接可以用(绿色)
# linux - 在线安装；离线安装
# 1. 专门安装程序包，产品不一样安装的方式不一样
# 2. 在线安装- 应用市场
# 3. 离线安装 - 自己准备好安装的程序；tar.gz直接解压缩（归档压缩），类似于绿色软件，直接运行，直接用
```

#### 部署jdk环境

![image-20230912200057449](D:\courses\00-课程截图\image-20230912200057449.png)

![](https://woniumd.oss-cn-hangzhou.aliyuncs.com/test/wangxiaofeng/20230404141441.png)

```shell
# 1. 将 jdk.tar.gz 上传到linux系统
# 1.1 使用MobaXterm带upload，使用右键菜单
# 1.2 使用scp命令 - 基于ssh协议的一种远程复制命令，从你的笔记本windows->vm linux
# 在windows的命令行执行如下命令
scp D:\downloads\jdk-8u251-linux-x64.tar.gz root@192.168.179.146:/opt/jdk-8u251-linux-x64.tar.gz

# 2. 进入/opt目录
cd /opt

# 3. 解压缩
tar -zvxf jdk-8u251-linux-x64.tar.gz

# 4. 验证结果
/opt/jdk1.8.0_371/bin/java -version

# linux要求命令的执行或者脚本的执行必须带路径
# 但是带路径比较麻烦，可以将程序配置环境变量
```

####  配置jdk环境变量

```shell
# 在修改如下文件做备份 
cp /etc/profile /opt/profile    
# 文件到文件
# 配置系统变量
# 用户及变量
# 就在用户的目录的.bashrc文件配置
# 系统变量
# /etc目录
cd /etc
# 编辑文件 profile
vi /etc/profile
# 在文件的末尾，添加如下内容
# the following is jdk enviroments added by brave
export JAVA_HOME=/opt/jdk1.8.0_251
export PATH=$JAVA_HOME/bin:$PATH

# 使环境变量生效
source /etc/profile
# source 在当前shell中，读取profile文件中命令，并且执行

# 验证 - 在任意位置
java -version
```

#### JAVA应用程序

```shell
# 单机版 
# jar包 - 将java程序转成xxx.class二进制文件后，打包一起 java -D xxx.jar
# war包 - war web应用的程序，一般放在tomcat
# 微服务 - 稍微复杂 Springboot + SpringCloud + Eureka，跑的jar包（tomcat+war）
```

####  部署tomcat

```shell
# 1. 将tomcat.tar.gz上传到Linux的opt
# 2. 进入opt目录，解压tomcat
# 3. 执行startup.sh - /opt/apache-tomcat-8.5.68/bin/startup.sh
# 4. 用windows的浏览器访问 http://linux_ip:8080
# 5. 如果打不开，如何排错
# 5.1 ping
# 5.2 确认是不是允许通过防火墙，查看防火墙状态 - 
systemctl status firewalld
systemctl stop firewalld

# 6. 验证
ss -lnp | grep 8080
netstat -anlp | grep 8080
```

#### linux镜像源修改

```shell
# 在/opt目录创建目录 yum.repos.d
mddir /opt/yum.repos.d
mv /etc/yum.repos.d/*.repo /opt/yum.repos.d/
# https://developer.aliyun.com/mirror/centos
# 在线安装 - 类似于应用市场
# yum - 下载rpm包再安装，自动帮我解决依赖包
# 安装源 - 从什么地方下载再安装，清华或者aliyun
# 获取镜像原修改修改方法 - 清华
# 进入/etc/yum.repos.d
cd /etc/yum.repos.d

# 备份原来仓库配置文件
# cp /etc/yum.repos.d/CentOS-Base.repo /etc/yum.repos.d/CentOS-Base.repo.bak
mv /etc/yum.repos.d/CentOS-Base.repo /etc/yum.repos.d/CentOS-Base.repo.bak

# 使用如下命令
# sed -e 's|^mirrorlist=|#mirrorlist=|g' \
#         -e 's|^#baseurl=http://mirror.centos.org|baseurl=https://mirrors.tuna.tsinghua.edu.cn|g' \
#         -i.bak \
#         /etc/yum.repos.d/CentOS-*.repo
         
# 清华站点已无法使用，使用如下方式，改为阿里云
curl -o /etc/yum.repos.d/CentOS-Base.repo http://mirrors.aliyun.com/repo/Centos-7.repo

yum clean all
yum makecache fast
# 验证 - 安装 zip unzip net-tools
yum install zip
yum install unzip
yum install net-tools
ss -lnp | grep 8080
netstat -anlp | grep 8080

# 思考 - 使用yum 如何查看是否安装一个包？
yum help list 
# 查看yum list的用法
yum list installed | grep mysql
```

#### 部署mysql

```shell
# 离线安装 - rpm 安装
# rpm包
rpm -ivh mysql-community-common-5.7.38-1.el7.x86_64.rpm
rpm -ivh mysql-community-libs-5.7.38-1.el7.x86_64.rpm
rpm -ivh mysql-community-client-5.7.38-1.el7.x86_64.rpm
rpm -ivh mysql-community-server-5.7.38-1.el7.x86_64.rpm

# common->libs->client->server

# 查询有没有安装mysql
rpm -qa | grep mysql
```

##### 配置mysql镜像源

```shell
cd /etc/yum.repos.d
vi mysql-community.repo
# 粘贴如下内容
[mysql-connectors-community]
name=MySQL Connectors Community
baseurl=https://mirrors.tuna.tsinghua.edu.cn/mysql/yum/mysql-connectors-community-el7-$basearch/
enabled=1
gpgcheck=1
gpgkey=https://repo.mysql.com/RPM-GPG-KEY-mysql

[mysql-tools-community]
name=MySQL Tools Community
baseurl=https://mirrors.tuna.tsinghua.edu.cn/mysql/yum/mysql-tools-community-el7-$basearch/
enabled=1
gpgcheck=1
gpgkey=https://repo.mysql.com/RPM-GPG-KEY-mysql

[mysql-5.7-community]
name=MySQL 5.7 Community Server
baseurl=https://mirrors.tuna.tsinghua.edu.cn/mysql/yum/mysql-5.7-community-el7-$basearch/
enabled=1
gpgcheck=1
gpgkey=https://repo.mysql.com/RPM-GPG-KEY-mysql

# 清空缓存
yum clean all
# 重做
yum makecache fast
# 安装
yum install mysql-community-server
# 备注 - 如果检查密钥失败，修改配置文件，让gpgcheck=0，重新安装

# 如果失败做如下操作
rpm --import https://repo.mysql.com/RPM-GPG-KEY-mysql-2022
```

##### 配置MySQL服务

```shell
# 1. 启动服务
systemctl status mysqld
systemctl start mysqld
# 自己添加开机自启动
systemctl enable mysqld

# 2. 找mysql生成临时密码
grep "temporary password" /var/log/mysqld.log

# 3. 修改root的密码
mysql -u root -p
# -u 表示要用的用户root
# -p 表示提示你输入密码
# mysql> 表示你登录成功
# 此语句需要在mysql命令行执行
mysql>alter user root@localhost identified by "!QAZ2wsx";
# 刷新权限
flush privileges;
# 4. 退出重新登陆后授权
exit

# 再次登录，使用新密码登录，mysql -u root -p
# 此语句需要在mysql命令行执行
# 让root用户可以再linux以外的任何地方都可以访问数据库，远程访问，指定的密码和本地密码一样
# 但是企业中，两个密码不能一样
grant all privileges on *.* to root@'%' identified by "!QAZ2wsx";
flush privileges;
# 5. 在windows的navicat创建一个链接 linux_vm_mysql_xa69
# 7. 执行相关SQL的文件
```

##### RPM安装MySQL

```shell
# 检查是否存在旧mysql
rpm -qa|grep -i mysql

#  删除旧的mysql
rpm -e –nodeps 包名 
#如果提示错误，尝试用下列命令执行
rpm -ev 包名 --nodeps
rpm  -e --noscripts 包名

# 删除mariadb的库
rpm -qa|grep mariadb
rpm -e mariadb-libs-5.5.64-1.el7.x86_64 --force --nodeps

# 查找之前老版本mysql的目录、并且删除老版本mysql的文件和库
find / -name mysql
# 显示下列结果
/var/lib/mysql
/var/lib/mysql/mysql
/usr/lib64/mysql  
# 依次删除
rm -rf /var/lib/mysql
rm -rf /var/lib/mysql/mysql
rm -rf /usr/lib64/mysql

# 下载rpm安装包
wget https://downloads.mysql.com/archives/get/p/23/file/mysql-5.7.36-1.el7.x86_64.rpm-bundle.tar

# 解压
tar -xvf mysql-5.7.36-1.el7.x86_64.rpm-bundle.tar

# 安装 common->libs->client->server
rpm -ivh mysql-community-common-5.7.19-1.el7.x86_64.rpm
rpm -ivh mysql-community-libs-5.7.19-1.el7.x86_64.rpm
rpm -ivh mysql-community-libs-compat-5.7.39-1.el7.x86_64
rpm -ivh mysql-community-client-5.7.19-1.el7.x86_64.rpm
rpm -ivh mysql-community-server-5.7.19-1.el7.x86_64.rpm
```

#### NGINX

```shell
# 安装源
yum install epel-release

# 安装nginx
yum install nginx

# 配置服务
systemctl start nginx

# 验证是否成功
# 使用你的windows浏览器访问地址，http://linux_ip

# 查看配置文件
/etc/nginx

# 添加一个自己自我介绍
# 文字和图片
# 将你自己的index.html放到如下位置
/usr/share/nginx/html
# 先备份原来的html内容

# 通过你的windows上浏览器访问你的个人介绍的页面
```

#### rabbitmq

```shell
# 启动rabbitmq要注意hostname不能数字，修改主机名不要设为localhost
# 如果启动失败执行命令 
hostnamectl set-hostname wxf
# 重新启动 reboot
yum install erlang 
yum install rabbitmq-server
# 启动服务 - 分别使用两种方式启动服务，停止服务
systemctl start rabbitmq-server
# rabbitmq-server -detached   # 启动服务
# rabbitmqctl stop    # 停止服务
# rabbitmqctl status  # 查看服务状态

# 启用管理端（web）
rabbitmq-plugins enable rabbitmq_management
# 重新启动
systemctl restart rabbitmq-server

# 访问地址：http://xxx.xxx.xxx.xxx:15672/ 默认账号密码：guest guest
# 配置访问账号和权限
rabbitmqctl add_user admin 123456
rabbitmqctl set_user_tags admin administrator
rabbitmqctl set_permissions -p / admin ".*" ".*" ".*"

# 如果项目中启动时遇到timeout或者auto-delete需要重新执行配置访问账号和权限
rabbitmq-server 5672 web端口号15672
```

#### redis

```shell
# 安装redis
# 安装源
yum install epel-release

# 安装redis
yum install redis
systemctl start redis
systemctl status redis

# 配置密码
vi /etc/redis.conf

# 修改如下内容，先找到requirepass
# vi编辑redis.conf文件 # 1. vi /etc/redis.conf
bind 127.0.0.1 #添加井号，注释掉这部分，这是限制redis只能本地访问，而现在需要远程访问
requirepass 123456 # 设置密码
daemonize yes # 让在后台运行

# 验证
redis-cli
# 在redis环境中执行如下命令
ping
# 提示认证
auth 123456
# 再次ping，响应PONG

[root@localhost ~]# redis-cli
127.0.0.1:6379> help

127.0.0.1:6379> ping
(error) NOAUTH Authentication required.
127.0.0.1:6379> auth 123456
OK
127.0.0.1:6379> ping are you ready
(error) ERR wrong number of arguments for 'ping' command
127.0.0.1:6379> ping are
"are"
127.0.0.1:6379> ping "are you ready"
"are you ready"
127.0.0.1:6379> ping
PONG
127.0.0.1:6379> select 0;
(error) ERR invalid DB index
127.0.0.1:6379> select 0
OK
127.0.0.1:6379> help select

  SELECT index
  summary: Change the selected database for the current connection
  since: 1.0.0
  group: connection

127.0.0.1:6379> dbsize
(integer) 0
127.0.0.1:6379> rpush wxf teacher old man
(integer) 3
127.0.0.1:6379> lindex wxf 1
"old"
127.0.0.1:6379>
```

```shell
# 2. 运行命令
redis-server /etc/redis.conf

# 3. 验证
# 3.1 端口法
ss -lnp | grep 6379
netstat -lnp | grep 6379
ps -ef | grep redis
# 3.2 命令法
# 连接redis server 用的ip 127.0.0.1
redis-cli
> auth 123456
ok
```

redis-cli 命令有很多。比如

##### **连接操作相关的命令**

- 默认直接连接 远程连接-h ************ -p 6379
- ping：测试连接是否存活如果正常会返回pong
- echo：打印
- select：切换到指定的数据库，数据库索引号 `index` 用数字值指定，以 `0` 作为起始索引值
- quit：关闭连接（connection）
- auth：简单密码认证

##### **服务端相关命令**

- time：返回当前服务器时间
- client list: 返回所有连接到服务器的客户端信息和统计数据 参见http://redisdoc.com/server/client_list.html
- client kill ip:port：关闭地址为 `ip:port` 的客户端
- save：将数据同步保存到磁盘
- bgsave：将数据异步保存到磁盘
- lastsave：返回上次成功将数据保存到磁盘的Unix时戳
- shundown：将数据同步保存到磁盘，然后关闭服务
- info：提供服务器的信息和统计
- config resetstat：重置info命令中的某些统计数据
- config get：获取配置文件信息
- config set：动态地调整 Redis 服务器的配置(configuration)而无须重启，可以修改的配置参数可以使用命令 `CONFIG GET *` 来列出
- config rewrite：Redis 服务器时所指定的 `redis.conf` 文件进行改写
- monitor：实时转储收到的请求  
- slaveof：改变复制策略设置



##### **发布订阅相关命令**

- psubscribe：订阅一个或多个符合给定模式的频道 例如psubscribe news.* tweet.*
- publish：将信息 `message` 发送到指定的频道 `channel 例如publish msg "good morning"`
- pubsub channels：列出当前的活跃频道 例如PUBSUB CHANNELS news.i*
- pubsub numsub：返回给定频道的订阅者数量 例如PUBSUB NUMSUB news.it news.internet news.sport news.music
- pubsub numpat：返回客户端订阅的所有模式的数量总和
- punsubscribe：指示客户端退订所有给定模式。
- subscribe：订阅给定的一个或多个频道的信息。例如 subscribe msg chat_room
- unsubscribe：指示客户端退订给定的频道。

##### **对KEY操作的命令**

- exists(key)：确认一个key是否存在
- del(key)：删除一个key
- type(key)：返回值的类型
- keys(pattern)：返回满足给定pattern的所有key
- randomkey：随机返回key空间的一个
- keyrename(oldname, newname)：重命名key
- dbsize：返回当前数据库中key的数目
- expire：设定一个key的活动时间（s）
- ttl：获得一个key的活动时间
- move(key, dbindex)：移动当前数据库中的key到dbindex数据库
- flushdb：删除当前选择数据库中的所有key
- flushall：删除所有数据库中的所有key

##### **对String操作的命令**

- set(key, value)：给数据库中名称为key的string赋予值value
- get(key)：返回数据库中名称为key的string的value
- getset(key, value)：给名称为key的string赋予上一次的value
- mget(key1, key2,…, key N)：返回库中多个string的value
- setnx(key, value)：添加string，名称为key，值为value
- setex(key, time, value)：向库中添加string，设定过期时间time
- mset(key N, value N)：批量设置多个string的值
- msetnx(key N, value N)：如果所有名称为key i的string都不存在
- incr(key)：名称为key的string增1操作
- incrby(key, integer)：名称为key的string增加integer
- decr(key)：名称为key的string减1操作
- decrby(key, integer)：名称为key的string减少integer
- append(key, value)：名称为key的string的值附加value
- substr(key, start, end)：返回名称为key的string的value的子串

##### **对List操作的命令**

- rpush(key, value)：在名称为key的list尾添加一个值为value的元素
- lpush(key, value)：在名称为key的list头添加一个值为value的 元素
- llen(key)：返回名称为key的list的长度
- lrange(key, start, end)：返回名称为key的list中start至end之间的元素
- ltrim(key, start, end)：截取名称为key的list
- lindex(key, index)：返回名称为key的list中index位置的元素
- lset(key, index, value)：给名称为key的list中index位置的元素赋值
- lrem(key, count, value)：删除count个key的list中值为value的元素
- lpop(key)：返回并删除名称为key的list中的首元素
- rpop(key)：返回并删除名称为key的list中的尾元素
- blpop(key1, key2,… key N, timeout)：lpop命令的block版本。
- brpop(key1, key2,… key N, timeout)：rpop的block版本。
- rpoplpush(srckey, dstkey)：返回并删除名称为srckey的list的尾元素，并将该元素添加到名称为dstkey的list的头部

##### **对Set操作的命令**

- sadd(key, member)：向名称为key的set中添加元素member
- srem(key, member) ：删除名称为key的set中的元素member
- spop(key) ：随机返回并删除名称为key的set中一个元素
- smove(srckey, dstkey, member) ：移到集合元素
- scard(key) ：返回名称为key的set的基数
- sismember(key, member) ：member是否是名称为key的set的元素
- sinter(key1, key2,…key N) ：求交集
- sinterstore(dstkey, (keys)) ：求交集并将交集保存到dstkey的集合
- sunion(key1, (keys)) ：求并集
- sunionstore(dstkey, (keys)) ：求并集并将并集保存到dstkey的集合
- sdiff(key1, (keys)) ：求差集
- sdiffstore(dstkey, (keys)) ：求差集并将差集保存到dstkey的集合
- smembers(key) ：返回名称为key的set的所有元素
- srandmember(key) ：随机返回名称为key的set的一个元素

##### **对Hash操作的命令**

- hset(key, field, value)：向名称为key的hash中添加元素field
- hget(key, field)：返回名称为key的hash中field对应的value
- hmget(key, (fields))：返回名称为key的hash中field i对应的value
- hmset(key, (fields))：向名称为key的hash中添加元素field
- hincrby(key, field, integer)：将名称为key的hash中field的value增加integer
- hexists(key, field)：名称为key的hash中是否存在键为field的域
- hdel(key, field)：删除名称为key的hash中键为field的域
- hlen(key)：返回名称为key的hash中元素个数
- hkeys(key)：返回名称为key的hash中所有键
- hvals(key)：返回名称为key的hash中所有键对应的value
- hgetall(key)：返回名称为key的hash中所有的键（field）及其对应的value

#### minIO

```shell
cd /opt
[root@localhost opt]# pwd
/opt
[root@localhost opt]# mkdir minio
[root@localhost opt]# cd mioio
-bash: cd: mioio: 没有那个文件或目录
[root@localhost opt]# cd minio/
[root@localhost minio]# ls
[root@localhost minio]# 

# 下载
wget https://dl.minio.io/server/minio/release/linux-amd64/minio
curl -o minio https://dl.minio.io/server/minio/release/linux-amd64/minio

# 添加执行权限
？？？

# 再/var/lib中创建一个一个目录minio，用来放上传的数据
# 指定一个存放数据的路径
/var/lib/minio # 自己创建一个minio文件夹

# 启动 -1 (第一次启动，这种方式启动)
/opt/minio/minio server /var/lib/minio

# 启动 -2  不挂断地运行命令。
nohup /opt/minio/minio server /var/lib/minio > /var/log/minio.log &

# & - 在运行的指令末尾添加"&"符号可以让命令在后台运行
# && - 表示多个命令前一个命令执行成功，执行后边的命令

#-----------------------------------

# Status:         1 Online, 0 Offline. 
# API: http://***************:9000  http://127.0.0.1:9000     
# RootUser: miniominio 
# RootPass: miniominio 
# Console: http://0.0.0.0:18888 
# RootUser: miniominio 
# RootPass: miniominio
```

![](https://woniumd.oss-cn-hangzhou.aliyuncs.com/test/wangxiaofeng/20230407122551.png)
![](https://woniumd.oss-cn-hangzhou.aliyuncs.com/test/wangxiaofeng/20230407122646.png)

### 部署动物实验项目

#### 前端部署

```shell
# 上传dist.zip文件到 /var/www 目录中
# 解压dist.zip文件 - 
unzip dist.zip
[root@wxf www]# cd dist/
[root@wxf dist]# ls
favicon.ico  getAuthorization.js  index.html  index.html.bak1  static
[root@wxf dist]# pwd
/var/www/dist
# 注意确认路径
[root@wxf dist]#
```

```shell
# 添加配置文件
/etc/nginx/conf.d
vi animal.conf
# 在nginx配置文件的目录中的conf.d目录中，创建一个animal_experiment.conf
# 文件中要添加添加如下内容
server {
    listen       8009;
    location /{
        root   /var/www/dist; #对应dist的绝对路径
        index  index.html index.htm;
    }

    location ^~ /api/{
        rewrite /api/(.*) /$1 break; 
        proxy_pass http://127.0.0.1:8188;
    }
}

# 添加完成进行如下的操作, 这里的内容不添加
# Check the NGINX configuration
nginx -t

# reload NGINX configuration
systemctl reload nginx.service

# 重新启动nginx

# 验证
# 浏览器访问你 http://linux_ip:8009
```

##### ISSUES

```shell
# 1.  查看SELinux状态：
/usr/sbin/sestatus -v      ##如果SELinux status参数为enabled即为开启状态
# SELinux status:                 enabled
# getenforce                 ##也可以用这个命令检查
# 2. 关闭SELinux：
# 2.1 临时关闭（不用重启机器）：
setenforce 0                  ##设置SELinux 成为permissive模式
# 2.2 setenforce 1 设置SELinux 成为enforcing模式
# 修改/etc/selinux/config 文件
SELINUX=enforcing # 改为SELINUX=disabled
```

```shell
# 1. 权限问题，通过查看 - /var/log/nginx/error.log
# 修改nginx.conf文件中 user 使用root，linux做什么都要和权限打交道，tomcat的部署都是用的是root，所以这里就要修改权限
# 2. 使用如下命令启动和停止nginx - 
# RELOAD不靠谱啊！！！！必须stop再启动才可以
nginx -s stop
nginx -c /etc/nginx/nginx.conf
```

##### 前端to后端

```shell
# 修改前端文件
# 1. 打开dist目录中 static/js/app.6cc84a26.js文件
# 2. 全局查找 ws://*************** 替换为你的 后端的服务器的IP地址 （有两处）
# 3. 修改后保存
# 4. 补充 - 
sed -e "s|ws:\/\/172\.172\.172\.102|ws:\/\/192\.168\.179\.147|g" -i.bak app.6cc84a26.js
```

![](https://woniumd.oss-cn-hangzhou.aliyuncs.com/test/wangxiaofeng/20220914122622.png)

#### 

#### 后端部署

##### jar包和配置文件

1. 上传后端程序 - Leiren-AnimalExperiment-1.0-SNAPSHOT.jar
2. 上传配置文件和修改项目配置文件，连接到对应的组件（有哪些组件需要沟通）- application-dev.properties
3. 启动项目-找出项目启动的错误 （java -jar  -Dspring.config.location=/opt/animalExperiment/application-test.properties /opt/animalExperiment/animalExperiment.jar）

```shell
# 指定位置
# 创建项目运行的目录
mkdir /opt/animalExperiment

# 创建配置文件使用vscode创建，名字为application-dev.properties，并且使用如下内容
######################################################################################
spring.application.name=Leiren-AnimalExperiment
#后端端口
server.port=8188
spring.profiles.active=test


# 访问排除路径 （不用动）
access.token.exclude.path.patterns=/,/error,/actuator/**,/login,/getTransEncryptKey,/validateCode/**,/swagger-ui.html,/verifyCode/**,/register,/isRegistered/**,/file/download/**
# ws-extends-starter配置
ws.extends.always-force-close=false
ws.extends.enable-api-doc=true


# redis config redis配置
spring.session.store-type=redis
spring.redis.host=xxx.xxx.xxx.xxx
spring.redis.port=6379
# 设置redis密码，不设置为空
spring.redis.password=123456
spring.redis.jedis.pool.max-idle=8
spring.redis.jedis.pool.min-idle=0
spring.redis.jedis.pool.max-active=8
spring.redis.jedis.pool.max-wait=-1
spring.redis.timeout=3000


# mysql config mysql配置
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=jdbc:mysql://本机ip:3306/animal_experiment?useSSL=true&serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=CONVERT_TO_NULL
spring.datasource.username=root
spring.datasource.password=!QAZ2wsx
#spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.jpa.database=MYSQL
spring.jpa.show-sql=true
#解决事务中懒加载session关闭问题
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true

#用户默认密码
default-password=123456

#rabbitmq 配置信息
spring.rabbitmq.host=xxx.xxx.xxx.xxx
spring.rabbitmq.port=5672
spring.rabbitmq.username=admin
spring.rabbitmq.password=123456
spring.rabbitmq.virtual-host=/
spring.rabbitmq.listener.simple.retry.enabled=true

##### minio配置 
minio.server.endpoint=xxx.xxx.xxx.xxx
minio.server.port=9000
minio.server.secure=false
minio.server.access-key=minioadmin
minio.server.secret-key=minioadmin
##### 项目需要用到的bucket 创建对应的bucket
minio.server.must-buckets=system,ethics,experiment,room-cage,animal

# 注意井号不能放在后面，要放只能放在前面
######################################################################################

# 上传配置文件
/opt/animalExperiment/application-dev.properties

# 上传后端jar包，开发转测的被测程序
/opt/animalExperiment/animalExperiment.jar
```

#### 创建数据

```shell
# 使用navicat向数据库中插入基础数据
# 1. 创建库animal_experiment，用的字符集是utf_8_mb4
# 2. 打开库
# 3. 执行插入数据的SQL脚本
```

##### 创建库

右键选择你的数据库连接 -> 新建数据库

![img](D:\courses\00-课程截图\R}}S72{QSP9VQ`UT[HZ6BIW.png)

双击你创建的数据库（打开了数据库）->右键选择运行SQL文件->选择项目的SQL文件

![img](D:\courses\00-课程截图\G_`X5[E]1LC5[[R2%7[PK}I.png)

#### 启动项目

```shell
java -jar  -Dspring.config.location=/opt/animalExperiment/application-test.properties /opt/animalExperiment/animalExperiment.jar
```



```shell
nohup java -jar  -Dspring.config.location=/opt/animalExperiment/application-test.properties /opt/animalExperiment/animalExperiment.jar  > /var/log/animal.log &
```

#### 访问

```shell
http://主机ip:port/
# user - admin
# passwd - 123456
# http://主机ip:port/usermanagement
```

**注意**：全部的指示性信息应为INFO或WARRNING，如果有ERROR表示服务异常，未成功运行！

#### 整体过程

```shell
# 1. 上传jar包（开发转测给我们的产品） - 起合适的名字，
# 2. windows上使用vscode修改配置文件 - vi命令修改（）
# 3. 上传配置文件
# 4. linux上修改前端的js文件，将ip地址变更为你自己的linux的ip地址或者127.。。。
# 5. navicat链接数据库，并创建数据库（注意数据的名字和配置文件中的名字一致），以及执行sql语句
# 6. 启动后端服务，确保没有error，以及重新启动nginx，确保能够看到验证码
#
```

#### 问题总结

```shell
# 1. 对命令内容的理解 比如java 启动jar程序的时候是配置文���，比如systemctl start与restart不同
# 2. 看错误 - 还需要努力的看错误，硬着头皮（看关键字 - error, access denied, NOAUTH required,timeout(服务重新启动)）
# 3. 备份源文件
# 4. 配置文件的内容要理解
# 5. 每个服务部署完了，验证，出了问题的时候再次验证，不要认为自己验证了就没有问题
# 6. 要有怀疑精神
```

### 总结

```python
# 1. 403问题 - systemctl stop nginx 更换命令启动 nginx -c /etc/nginx/nginx.conf
# 2. redis - 不要使用systemctl start redis-server ,更改为 redis-server /etc/redis.conf
# 3. rabbitmq - 在你的配置文件中，用户名改为wxf就可以了，
rabbitmqctl add_user wxf 123456
rabbitmqctl set_user_tags wxf administrator
rabbitmqctl set_permissions -p / wxf ".*" ".*" ".*"
# 3.1 linux hostname名字是xx ,数字，导致rabbitmq无法启动
# 3.2 如果出现了访问mq，timeout的问题，那就重新启动mq
# 4. dist ，js 只需修改ip地址即可
# 5. 检查,获取pid
ss -lnp | grep nginx # 检查前端 - nginx -c /etc/nginx/nginx.conf
ss -lnp | grep 6379  # 检查redis - redis-server /etc/redis.conf
ss -lnp | grep 5672  # 检查rabbitmq - systemctl start rabbitmq-server
ss -lnp | grep 9000  # 检查minio - nohup /opt/minio/minio server /var/lib/minio > /var/log/minio.log &
ss -lnp | grep 3306  # 检查数据库 - systemctl start mysqld

# 6. kill
kill -9 pid数字
```

### docker - 容器技术

linux - namspace, 隔离开来，文件夹

docker - 创建镜像(模版)->创建容器（app）

加速器->华为云->daemon.json

docker login 华为云帮我生成登录临时用户名和密码

docker pull ->拉去镜像，从别人的仓库复制一份，镜像(模版)，镜像不能修改，增加了我们自己的东西(容器)

验证 docker image ls

docker run ->基于镜像，创建容器（名字，涉及端口号，初始内容，比如密码）

验证容器应用，navicat

nginx（提供前端访问内容html, css, js等等）与MySQL（数据存储层），存储数据

```shell
# 虚拟化技术
# 云计算 - CPU，内存，网络存储，网络，底层用的技术是虚拟化技术
```

#### docker介绍

```shell
微服务 - 登录，注册，搜索商品，广告，订单，支付
集装箱 - docker
docker - 支持的系统, window 10,linux任何系统，windows server 2012
docker工作流程
docker是基于linux系统完成，容器内部是一个微小linux
```

![](https://woniumd.oss-cn-hangzhou.aliyuncs.com/test/wangxiaofeng/20220803190815.png)

![](https://woniumd.oss-cn-hangzhou.aliyuncs.com/test/wangxiaofeng/20220602173816.png)

#### 防火墙

```shell
systemctl disable firewalld
systemctl stop firewalld
添加端口：
# firewall-cmd --add-port=3306/tcp --permanent
移除端口：
# firewall-cmd --remove-port=80/tcp --permanent
查看端口状态：
# firewall-cmd --zone=public --query-port=80/tcp
```


#### 部署docker

- 镜像源配置

```shell
# 安装依赖
yum install yum-utils device-mapper-persistent-data lvm2

# 2. 添加国内镜像源 - 为了装docker

# 2.1 或者使用阿里云镜像源
yum-config-manager --add-repo http://mirrors.aliyun.com/docker-ce/linux/centos/docker-ce.repo

# 2.2 或者使用清华站点的方法
# 添加仓库源
yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
# 修改里边内容
sed -i 's+download.docker.com+mirrors.tuna.tsinghua.edu.cn/docker-ce+' /etc/yum.repos.d/docker-ce.repo

# ee(enterprise edition) ce(community editon)
# 3. 进入镜像源目录进行查看验证
# yum 镜像源目录在哪？？？？
```

- 安装docker

```shell
# 安装前确认自己要打开防火墙还是禁用防火墙
# 找到要安装版本
yum list docker-ce --showduplicates | sort -r

# 20.10.6-3.el7 
yum install docker-ce-23.0.2-1.el7
# cli - client, command line

# 验证是否安装成功
# 服务的状态
systemctl status docker
systemctl start docker

# 查看docker信息
docker info

yum clean dbcache
yum clean metadata
yum clean rpmdb
yum clean headers
yum clean all
 
rm -rf /var/cache/yum/timedhosts.txt
rm -rf /var/cache/yum/*
 
yum makecache

```

#### docker业务

```shell
1. client - 发送docker指令给docker server
2. server - 管理控制容器，镜像等对象，接受操作的指令
3. 镜像 - 比tomcat的官方制作了好，发布到仓库，别人在获取这个tomcat，这个tomcat就成为镜像，不允许修改，层
4. 容器 - 基于镜像生成一个对象，可以操作，也可以运行，存放用户的数据
5. 仓库 - 存放镜像地方就叫仓库，本地仓库和远程仓库
```

#### 添加加速器

```shell
# 配置文件
vi /etc/docker/daemon.json
# 使用华为的镜像站点
# 添加私有的镜像加速器，注册来源华为或者是来源
docker login -u cn-north-4@SK5YCKSSQ1G5RARDUP0K -p 0422b32f63ed3eb5d6830b6975a9c290543978050df149d05b7ce2ac4340806f swr.cn-north-4.myhuaweicloud.com
# 输入如下内容
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://docker.mirrors.sjtug.sjtu.edu.cn",
    "https://mirror.ccs.tencentyun.com"
  ]
}

{
  "registry-mirrors": [
        "https://registry.hub.docker.com",
        "http://hub-mirror.c.163.com",
        "https://mirror.baidubce.com",
        "https://docker.mirrors.sjtug.sjtu.edu.cn",
        "https://docker.nju.edu.cn"
  ]
}

{
    "registry-mirrors": [
        "https://docker.m.daocloud.io",
        "https://huecker.io",
        "https://dockerhub.timeweb.cloud",
        "https://noohub.ru"
    ]
}

# 生效
# 服务重新装载配置文件
systemctl daemon-reload
systemctl restart docker
# 验证
docker info
```

#### 镜像及操作

```shell


# 获取镜像
docker pull 仓库:版本
docker pull tomcat:8.5.68-jdk8-openjdk
docker pull mysql:5.7.41

# 查看镜像
docker image ls # 基本是docker 命令 + 类似于ls
docker images

# 删除
docker image rm tomcat:8.5.68-jdk8-openjdk
docker image rm id或者名字
```

#### 容器及操作

```shell
# 创建容器的方法
# 1. docker create
# docker create --name tom1 -p 18080:8080 tomcat:8.5.68-jdk8-openjdk
docker create --name mq1 -p 25672:15672 -p 6672:5672 rabbitmq:3.9.22
# 容器处于关机状态，没有运行
# docker 主命令
# create 创建容器
# --name 容器的名字 不要重复
# -p 端口映射也就是将linux上的端口号，关联到容器的端口号，前面的是linux的端口号，后边是容器的端口号
# tomcat:8.5.68-jdk8-openjdk - 表示的是创建容器使用的镜像
# 前面的端口号切记不可重复，和现在linux正在使用的服务的端口号重复

# 1.1 运行容器
docker container start id或者容器的名字

# 1.2 停止容器
docker container stop id或者容器的名字

# 1.3 重新启动
docker container restart id或者容器的名字

# 2 验证
# 从你的windows上浏览器访问地址http://linuxip:linux的端口号
# 2.1 查看容器的状态
docker container ls # 仅看到正在运行的容器

# 2.2 查看所有的容器，包括已经关闭的容器
docker container ls -a
docker container ps -a

# 3. 删除容器
docker container rm 容器的id或者名字
# 删除的时候，首先把容器停止关机

# 4. 查看容器的信息
docker inspect 容器的id或者名字 | grep 查看目标内容
```

#### 与容器交互

```shell
# 数据互通
docker exec # 可以运行命令在正在运行的容器当中
docker exec 容器的名字或者id linux的命令

# ls pwd
docker exec tom1 ls

# 进入容器
docker exec -it tom1 /bin/bash
# exec - 表示执行
# -it - 与容器进行命令行式的交互
# tom1 - 容器的名字
# /bin/bash - 是shell的类型

# 数据共享
docker cp 是在linux执行，不是在容器中

# 从linux 到 容器
docker cp 源路径 目标路径
# 源路径是谁 - 从linux到容器，所以源是linux，linux路径在前，容器在后
docker cp /opt/apache-tomcat-8.5.68/webapps/wns.war tom1:/usr/local/tomcat/webapps/wns.war

# 从容器 到 linux
# 源 - 容器， 目标 - linux
docker cp tom1:/usr/local/tomcat/webapps/wns/WEB-INF/classes/db.properties /opt/db.properties
# 修改好之后，再复制到容器中，覆盖原来的文件
```

#### mysql容器

```shell
# 容器创建的第二个方法
docker run # 创建完容器后是运行状态
docker run -d --name my1 --privileged=true -p 13306:3306 -e MYSQL_ROOT_PASSWORD="123456" mysql:5.7.41
# 注意密码不要使用!开始
# -d 表示创建容器，运行再后台
# --privileged=true命令，扩大容器的权限解决挂载目录没有权限的问题，
# -e 设置容器的中环境变量，设置的是数据库root的密码
```

#### 数据持久化

```shell
# docker 特点
# 1. 容器之间互相隔离
# 2. 容器与宿主机(linux)公用cpu，内存等资源，但是网络是独立

# 卷 - 目录，是容器管理目录的方式或者方法，在使用直接告诉容器使用哪个卷即可
docker volume create xa71
# 创建一个卷，也就是一个目录
docker inspect xa71
# 查看的卷的信息，主要是卷存放的目录
docker run -d --name=tom2 -p 18082:8080 -v xa71:/usr/local/tomcat/webapps tomcat:8.5.68-jdk8-openjdk
# -v 表示将卷x71与容器中路径/usr/local/tomcat/webapps关联起来，或者映射起来，让容器不再将数据放在容器中，而是放在卷中

# 目录 - 指定的时候，要使用完整路径，绝对路径
# 再根目录中创建一个目录share,再share中创建两个子目录project ,mysqldata
# project - 项目， mysqldata放数据
docker run -d --name=tom3 -p 18083:8080 -v /share/project:/usr/local/tomcat/webapps 
tomcat:8.5.68-jdk8-openjdk

docker run --name my2 -d --privileged=true -p 13307:3306 -v /share/mysqldata:/var/lib/mysql -e MYSQL_ROOT_PASSWORD="123456" mysql:5.7.39

# 注意清楚要关联的容器的路径
```

#### rabbitmq

```shell
docker pull rabbitmq:3.9.22
docker run -d -p 5672:5672 -p 15672:15672 --name mq1 rabbitmq:3.9.22
docker exec mq1 rabbitmq-plugins enable rabbitmq_management
docker exec mq1 rabbitmqctl add_user admin 123456
docker exec mq1 rabbitmqctl set_user_tags admin administrator
docker exec mq1 rabbitmqctl set_permissions -p / admin ".*" ".*" ".*"
```

#### redis

```shell
# 在根部目录创建一个data目录，用于容器挂载
mkdir /data
mkdir /data/redis
# 下载redis.conf
cd /data/redis
wget http://download.redis.io/redis-stable/redis.conf

# vi编辑redis.conf文件
bind 127.0.0.1 #添加井号，注释掉这部分，这是限制redis只能本地访问，而现在需要远程访问
requirepass 123456 # 设置密码
# 其他先暂不做修改

docker run -it --name myredis -v /data/redis/redis.conf:/usr/local/etc/redis/redis.conf -d -p 16379:6379 redis redis-server /usr/local/etc/redis/redis.conf

# 验证 - 在linux
docker container ls
redis-cli -h 192.168.179.129 -p 16379
> auth 123456
ok
> ping
PONG
```



```shell
docker pull redis

# 修改默认配置文件 		
bind 127.0.0.1 #注释掉这部分，这是限制redis只能本地访问
protected-mode no #默认yes，开启保护模式，限制为本地访问
daemonize no#默认no，改为yes意为以守护进程方式启动，可后台运行，除非kill进程（可选），改为yes会使配置文件方式启动redis失败
dir  ./ #输入本地redis数据库存放文件夹（可选）
appendonly yes #redis持久化（可选）

# 启动
docker run --privileged=true -p 16379:6379 --name redis -v /share/redis/conf/redis.conf:/etc/redis/redis.conf -v /share/redis/data:/data -d redis redis-server /etc/redis/redis.conf
# -p 16379:6379 端口映射：前表示主机部分，：后表示容器部分。
# --name myredis  指定该容器名称，查看和进行操作都比较方便。
# -v 挂载目录，规则与端口映射相同。
# -d redis 表示后台启动redis
# redis-server /etc/redis/redis.conf  以配置文件启动redis，加载容器内的conf文件，最终找到的是挂载的目录/usr/local/docker/redis.conf
# appendonly yes 开启redis 持久化
# --requirepass 配置密码
# --privileged=true 容器内的root拥有真正root权限，否则容器内root只是外部普通用户权限
docker run --privileged=true -p 16379:6379 --name redis -v /share/redis/data:/data -d redis redis-server --requirepass 123456

docker run \
-itd \
--name redis4 \
--privileged=true \
-p 16379:6379 \
-v /share/redis/data:/data \
-v /share/redis/redis.conf:/etc/redis/redis.conf \
redis:7.0.0 \
/usr/local/bin/redis-server /etc/redis/redis.conf


# 运行容(命令换行展示)
docker run \
-itd \
--name redis7 \
--privileged=true \
-p 6379:6379 \
-v /docker/redis/redis.conf:/etc/redis/redis.conf \
-v /docker/redis/data:/data \
redis:7.0.0 \
redis-server /etc/redis/redis.conf
```

#### minIO

```shell
docker run -p 19000:9000 -p 19090:9090 --name minio1 -d -e "MINIO_ACCESS_KEY=minioadmin" -e "MINIO_SECRET_KEY=minioadmin" -v /share/miniodata:/data minio/minio server /data --console-address ":9090" -address ":19000"

docker run -d -p 19000:9000 --name minio2 \
-e "MINIO_ACCESS_KEY=minioadmin" \
-e "MINIO_SECRET_KEY=minioadmin" \
-v /share/miniodata/data:/data \
-v /share/miniodata/config:/root/.minio \
minio/minio server /data \
--console-address ":9000" --address ":9090"


docker run -itd -p 19000:9000 -p 19090:19090 \
  --name minio1 \
  -e "MINIO_ROOT_USER=minioadmin" \
  -e "MINIO_ROOT_PASSWORD=minioadmin" \
  -v /share/data:/data \
  -v /share/config:/root/.minio \
  minio/minio server /data \
  --console-address ":19090"

# 使用这里的进行项目部署，上边的为演练
docker run -itd -p 19000:9000 -p 19090:19090 \
  --name minio1 \
  -e "MINIO_ACCESS_KEY=minioadmin" \
  -e "MINIO_SECRET_KEY=minioadmin" \
  -v /share/data:/data \
  -v /share/config:/root/.minio \
  minio/minio server /data \
  --console-address ":19090"
  
  
docker run -itd -p 19000:9000 -p 19090:19090 \
  --name minio1 \
  -e "MINIO_ACCESS_KEY=minioadmin" \
  -e "MINIO_SECRET_KEY=minioadmin" \
  minio/minio server /data --console-address ":19090"
```

#### nginx

```shell
docker run  -p 18880:80 -p 18889:8009 --name mynginx \
-v /etc/nginx/nginx.conf:/etc/nginx/nginx.conf \
-v /etc/nginx/conf.d:/etc/nginx/conf.d \
-v /var/www:/var/www \
-d  nginx
```

#### animal_experiment

```shell
docker run -dit --name=animal \
-p 18188:8188 \
-v /opt/animalExperiment:/opt \
kdvolder/jdk8 \
java -jar -Dspring.config.location=/opt/animal.properties /opt/animal.jar
# 这里的路径是谁的路径呢？
```

### docker部署项目总结

```shell
# 1. 端口到端口 - 怎么做？ 画图法，随时使用纸和笔，进行构思梳理
# 2. 路径问题 - a -》 容器的b目录 /opt/animalExperiment -> /opt 容器/opt=/opt/animalExperiment（怎么做 - 推理法，推断法）
# 3. 规划性
```

### dockerfile

```shell
# 添加如下内容
FROM redis
COPY redis.conf /usr/local/etc/redis/redis.conf
CMD [ "redis-server", "/usr/local/etc/redis/redis.conf" ]

# 构建镜像
docker build  -t 仓库:TagName dir
docker build -t myredis:1.0 -f redis.dockerfile .
# -t − 给镜像加一个Tag
# ImageName − 给镜像起的名称
# TagName − 给镜像的Tag名
# Dir − Dockerfile所在目录
```

## linux高级命令

### grep

```shell
# 参考grep命令
```

### sed

```shell
# 流编辑器 - 非交互式的，按行工作\n
```

```shell
sed -e 's|^mirrorlist=|#mirrorlist=|g' \
        -e 's|^#baseurl=http://mirror.centos.org|baseurl=https://mirrors.tuna.tsinghua.edu.cn|g' \
         -i.bak \
        /etc/yum.repos.d/CentOS-*.repo
```



#### 命令格式

```shell
sed [选项]... {脚本(如果没有其他脚本)} [输入文件]...
# -n 只选择匹配
# -i 原地编辑，在原来的位置进行编辑，就是要注意风险比较大，使用的时候��备份
# -r 表示使用正则表达式
# -n选项 和 p命令 一起使用表示只打印那些发生替换的行
# -e 可以让sed执行多个命令，一个-e可以执行一个脚本
```

### sed命令

```shell
a\ # 在当前行下面插入文本。
i\ # 在当前行上面插入文本。
c\ # 把选定的行改为新的文本。
d # 删除，删除选择的行。
D # 删除模板块的第一行。
s # 替换指定字符
h # 拷贝模板块的内容到内存中的缓冲区。
H # 追加模板块的内容到内存中的缓冲区。
g # 获得内存缓冲区的内容，并替代当前模板块中的文本。
G # 获得内存缓冲区的内容，并追加到当前模板块文本的后面。
l # 列表不能打印字符的清单。
n # 读取下一个输入行，用下一个命令处理新的行而不是用第一个命令。
N # 追加下一个输入行到模板块后面并在二者间嵌入一个新行，改变当前行号码。
p # 打印模板块的行。
P # (大写) 打印模板块的第一行。
q # 退出Sed。
b lable # 分支到脚本中带有标记的地方，如果分支不存在则分支到脚本的末尾。
r file # 从file中读行。
t label # if分支，从最后一行开始，条件一旦满足或者T，t命令，将导致分支到带有标号的命令处，或者到脚本的末尾。
T label # 错误分支，从最后一行开始，一旦发生错误或者T，t命令，将导致分支到带有标号的命令处，或者到脚本的末尾。
w file # 写并追加模板块到file末尾。  
W file # 写并追加模板块的第一行到file末尾。  
! # 表示后面的命令对所有没有被选定的行发生作用。  
= # 打印当前行号码。  
# # 把注释扩展到下一个换行符以前。  
```

#### sed替换标记

```shell
g # 表示行内全面替换。  
p # 表示打印行。  
w # 表示把行写入一个文件。  
x # 表示互换模板块中的文本和缓冲区中的文本。  
y # 表示把一个字符翻译为另外的字符（但是不用于正则表达式）
\1 # 子串匹配标记
& # 已匹配字符串标记
```

#### sed元字符集

```shell
^ # 匹配行开始，如：/^sed/匹配所有以sed开头的行。
$ # 匹配行结束，如：/sed$/匹配所有以sed结尾的行。
. # 匹配一个非换行符的任意字符，如：/s.d/匹配s后接一个任意字符，最后是d。
* # 匹配0个或多个字符，如：/*sed/匹配所有模板是一个或多个空格后紧跟sed的行。
[] # 匹配一个指定范围内的字符，如/[sS]ed/匹配sed和Sed。  
[^] # 匹配一个不在指定范围内的字符，如：/[^A-RT-Z]ed/匹配不包含A-R和T-Z的一个字母开头，紧跟ed的行。
\(..\) # 匹配子串，保存匹配的字符，如s/\(love\)able/\1rs，loveable被替换成lovers。
& # 保存搜索字符用来替换其他字符，如s/love/ **&** /，love这成 **love** 。
\< # 匹配单词的开始，如:/\<love/匹配包含以love开头的单词的行。
\> # 匹配单词的结束，如/love\>/匹配包含以love结尾的单词的行。
x\{m\} # 重复字符x，m次，如：/0\{5\}/匹配包含5个0的行。
x\{m,\} # 重复字符x，至少m次，如：/0\{5,\}/匹配至少有5个0的行。
x\{m,n\} # 重复字符x，至少m次，不多于n次，如：/0\{5,10\}/匹配5~10个0的行。  
```

#### 替换

```shell
# 查找某个字符换变成另外的字符串
```

#### 定界符

以上命令中字符 / 在sed中作为定界符使用，也可以使用任意的定界符：

```shell
sed 's:test:TEXT:g'
sed 's|test|TEXT|g'
# : # / | - 自定义定界符
```

定界符出现在样式内部时，需要进行转义：

```shell
sed 's/\/bin/\/usr\/local\/bin/g'
```

#### 已匹配字符串标记-&和\1

```shell
echo this is a test line | sed 's/\w/[&]/g'
echo this is a test line | sed -r 's/\w+/[&]/g'
echo this is a test line | sed 's/\w\+/[&]/g'
```

```shell
echo this is a test line | sed -r 's/\w+/[\1]/g'
# 这个有结果吗？为什么？
```

```shell
echo this is digit 7 in a number
# sed如何实现只有数字7输出
echo this is digit 7 in a number | sed -r 's/.*?digit ([0-9]+).*?/\1/g'
```

```shell
echo this is digit 4 9 3 in a number
# 怎么输出只有4 9 3

# 怎么输出未9 3 4
echo this is digit 400 90 3 in a number | sed -r 's/.*? ([0-9]+) ([0-9]+) ([0-9]+) .*?/\3 \2 \1/g'
```

#### 写入新的文件

```shell
sed 's/我爱你/I love you/w new.txt' filename
```

#### 追加内容 a\

```shell
sed -e '/^:/a\************ mylinux' -e '/^:/a\************ mylinux' /etc/hosts
```

#### 打印奇数行或偶数行

方法1：

```shell
sed -n 'p;n' test.txt  #奇数行
sed -n 'n;p' test.txt  #偶数行
```

方法2：

```shell
sed -n '1~2p' test.txt  #奇数行
sed -n '2~2p' test.txt  #偶数行
```

#### sed提取ip地址

```shell
# sed 提取ip地址
ip addr|sed -nr 's#^.*inet (.*)/24.*$#\1#gp'
ip addr|sed -nr 's#.*inet\s(.*)/[0-9]{1,2}.*$#\1#gp'
# 使用不同间隔符，需要再 替换的情况下，其他可能不生效，标准的定界符 /
# -r 使用标准的正则表达式不需要 \(.*\)这种表达的方式
# 注意\s不等于空格，某些情况下无法使用代表空格


echo "aou 1 aio 7 aio 3 ifa 9 adi 2 aui 1 dua 0 ai 6 ica 2 aic 6 ao 8" | sed  -r 's/[^0-9]+([0-9]+)/\1/g' | sed -r 's/^[0-9]+$/\n&/'
```

### awk

```shell
# 它是一种强大的文本处理工具（编程语言），特别适用于数据分析和报告生成。awk通过读取输入文件（或标准输入），对每一行或特定的字段执行指定的操作，并将结果输出到标准输出（通常是屏幕）或文件。
# 我最近想要做一个饮料销售方案;我-最近-想要-做-一个-饮料-销售-方案
# I recently need to design a scheme about the drinks.
# 标签：
```

#### 语法结构

```shell
awk 'BEGIN {print "this is starting....."} pattern {print "this is main"} END {print "this is end......"}' file
awk 'BEGIN{ print "start" } pattern{ commands } END{ print "end" }' file
pattern 是条件表达式，用于选择哪些行需要执行action。
action 是对符合条件的行执行的操作。
如果省略pattern，则对每一行都执行action。
如果省略action，则默认打印当前行。
```

#### 内置变量

- `$0`：当前记录（通常是一行）。
- `$1, $2, ...`：当前记录的字段，默认由空格或制表符分隔。
- `NR`：已读取的记录数（行号）。
- `NF`：当前记录中的字段数。
- `FS`：输入字段分隔符（默认为空格）。
- `OFS`：输出字段分隔符（默认为空格）。

![](D:\courses\00-课程截图\20230413122705.png)

#### 案例

```shell
# 找出每个用户使用shell的环境的类型
awk -f script.awk /etc/passwd
# script.awk代码内容如下:
BEGIN{
    FS=":"
    print "Field1"  "       " "Field2"
}
{
   print $1 "    " FILENAME
}
END {

   print "awk 执行结束"
}
```

```shell
# 找出5-8行的内容，并且依什么开头的
[root@localhost ~]# awk '{ if (NR>=5 && NR <=8) print $0}' /etc/passwd
lp:x:4:7:lp:/var/spool/lpd:/sbin/nologin
sync:x:5:0:sync:/sbin:/bin/sync
shutdown:x:6:0:shutdown:/sbin:/sbin/shutdown
halt:x:7:0:halt:/sbin:/sbin/halt
[root@localhost ~]# awk '{ if (NR>=5 && NR <=8 && $0~/^s/) print $0}' /etc/passwd
sync:x:5:0:sync:/sbin:/bin/sync
shutdown:x:6:0:shutdown:/sbin:/sbin/shutdown
[root@localhost ~]# awk '{ if (NR>=5 && NR <=8 && $0!~/^s/) print $0}' /etc/passwd
lp:x:4:7:lp:/var/spool/lpd:/sbin/nologin
halt:x:7:0:halt:/sbin:/sbin/halt
[root@localhost ~]# 
```

```shell
# awk获取ip地址
# 如何使用awk提取ip地址
ip a | awk -F "[ /]" '{if ($5 == "inet") print $6}'
ip a | awk -F "[ /]" '{print $1}'
# if语句使用
# 分割内容 使用了[ /]表示使用其中任何一个，同时使用多个进行分割 
```

#### 练习

下面统计/etc/passwd的账户人数

```
awk '{count++;print $0;} END{print "user count is ", count}' /etc/passwd
root:x:0:0:root:/root:/bin/bash
......
user count is  40
```

count是自定义变量。之前的action{}里都是只有一个print,其实print只是一个语句，而action{}可以有多个语句，以;号隔开。

 

这里没有初始化count，虽然默认是0，但是妥当的做法还是初始化为0:

```
awk 'BEGIN {count=0;print "[start]user count is ", count} {count=count+1;print $0;} END{print "[end]user count is ", count}' /etc/passwd
[start]user count is  0
root:x:0:0:root:/root:/bin/bash
...
[end]user count is  40
```

 

统计某个文件夹下的文件占用的字节数

```
ls -l |awk 'BEGIN {size=0;} {size=size+$5;} END{print "[end]size is ", size}'
[end]size is  8657198
```

 

如果以M为单位显示:

```
ls -l |awk 'BEGIN {size=0;} {size=size+$5;} END{print "[end]size is ", size/1024/1024,"M"}' 
[end]size is  8.25889 M
```

注意，统计不包括文件夹的子目录。

#### 案例

score文本文件中有如下内容

```shell
Alice 85 A  
Bob 92 B  
Charlie 78 A  
David 88 C  
Eve 95 B
```

```shell
awk 'BEGIN { print "this is staring....." }  /Alice/ {print "Alice的行:" $0 } $2 > 90 {print "高分学生:" $1, "分数:", $2} END {print "处理结束"}' score
```



### shell编程

```shell
# shell - 壳，保护这linux内核
Shell是系统的用户界面，提供了用户与内核进行交互操作的一种接口。它接收用户输入的命令并把它送入内核去执行。
实际上Shell是一个命令解释器，它解释由用户输入的命令并且把它们送到内核。不仅如此，Shell有自己的编程语言用于对命令的编辑，它允许用户编写由shell命令组成的程序。Shell编程语言具有普通编程语言的很多特点，比如它也有循环结构和分支控制结构等，用这种编程语言编写的Shell程序与其他应用程序具有同样的效果。

BASH：是GNU的Bourne Again Shell，是GNU操作系统上默认的shell。
Korn Shell：是对Bourne SHell的发展，在大部分内容上与Bourne Shell兼容。
C Shell：是SUN公司Shell的BSD版本。
Z Shell：The last shell you'll ever need! Z是最后一个字母，也就是终极Shell。它集成了bash、ksh的重要特性，同时又增加了自己独有的特性。

# 解释器 - 负责将写的字符，按照语法规则进行翻译，变成计算机可以执行的代码
# SQL解释器， shell解释器，python
```

#### 编写脚本

```shell
# 1. 创建一个文件
# 2. 写入shell的命令完成自己的目标
# 3. 如何执行 - ....
# 4. 执行脚本的时候，路径是必须的吗？
```

#### 编程基本要素

```shell
# 代码结构
# 代码逻辑性
# 可读性 - 注释等等
# 命名规则 - shell
# a. 文件命名 - 小写的字母+_+数字+文件的类型(doc, py, java, sh,....)
# b. 变量命名 - 小写的字母+下划线+字母
# c. 函数命名 - 小写的字母+下划线+字母
# d. 类
```

#### 变量

```shell
# 定义或者声明变量
var_name = value # var_name就是变量
# 引用变量, $符号实际上告诉shell，哥们这是个变量，没$就是一个普通的字符
$var_name
echo ${message}
```

#### 命令结果赋值给变量

```shell
# 方法1：
cmd_result=`ip a | sed ...`
# 方法2
# $( ) 让shell可以执行命令，并保存结果
cmd_reuslt=$(ip a | grep ...)
```

#### shell中特殊的符号

![image-20230918162224189](D:\courses\00-课程截图\image-20230918162224189.png)

#### 内置变量

| 参数处理 | 说明                                                         |
| :------: | ------------------------------------------------------------ |
|    $#    | 传递到脚本或函数的参数个数                                   |
|    $*    | 以一个单字符串显示所有向脚本传递的参数                       |
|    $$    | 脚本运行的当前进程ID号                                       |
|    $!    | 后台运行的最后一个进程的ID号                                 |
|    $@    | 与$*相同，但是使用时加引号，并在引号中返回每个参数。         |
|    $-    | 显示Shell使用的当前选项，与set命令功能相同。                 |
|    $?    | 显示最后命令的退出状态。0表示没有错误，其他任何值表明有错误。 |
|    $n    | n是数字，表示传递给shell脚本的参数是第几个（$0, $1, ....）   |

#### if语句

```shell
if [[ $2 -eq $3 ]]; then
    echo "他们是相等"
elif [[ $2 -ge $3 ]]; then
    echo "大于等于"
else
    echo "小于等于"
fi
# 备注 [] 一般用来数字比较 ，两个[[]] 一般用来字符串 
# 等于 ==，!=, =~ 表示的是字符串满足这个正则表达式的条件

```

##### 样例

```shell
#!/bin/sh
# 指定脚本解释使用的解释是谁，shell类型
message=$1
#$1表示的shell预定义的变脸内置变量
# 定义一个变量
# 引用
echo ${message}

if [[ $2 -eq $3 ]]; then
    echo "他们是相等"
elif [[ $2 -ge $3 ]]; then
    echo "大于等于"
else
    echo "小于等于"
fi
```

#### for循环

```shell
#!/bin/bash  
# C++/C语言风格 
# 循环有个作用遍历
for((i=1;i<=10;i++)); # i++，也i+1进行下一次循环  
do  
    # 变与不变
    # 变，i，实际上我们操作对象要变，
    # 不变的是动作
    echo $(expr $i \* 3 + 1);  # i * 3 +1

done  
```

```shell
#!/bin/bash  
# for ... in 集合  
for i in $(seq 1 10) #（1,2,3,4,5.。。10） 
do   
   echo $(expr $i \* 3 + 1);  
done  
```

#### while

```shell
while [ 条件表达式 ]  
do  
   # 命令序列  
done

#!/bin/bash  
  
counter=1  
while [ $counter -le 5 ]  
do  
  echo "Counter: $counter"  
  counter=$((counter + 1))  
done
```

#### 命令行传参

```shell
#!/bin/bash
var1=$1
var2=$2
# var3=$*
# var4=$@

# echo "var1的值是$var1"
# echo "var2的值是$var2"
# echo "var3的值是$var3"
# echo "var4的是$var4"
echo "请输入你的用户名:"
# 停止，等待你输入
read name
echo "请输入你的密码:"
read password

# if语句
if [ $name == $1 ] && [ $password == $2 ]
then
  echo "你输的用户名正确"
else
  echo "用户名或者密码错误"
fi
```

### 数组

Shell 编程中，数组是一种非常有用的数据结构，允许你存储一系列的元素，这些元素可以是数字、字符串或者更复杂的数据结构（尽管在基本的 shell 脚本中，通常使用字符串形式）。不同的 shell（如 Bash、Zsh、Ksh 等）对数组的支持程度可能有所不同，但这里我将以 Bash 为例，详细解释 Shell 编程中的数组使用。

#### 1. 声明数组

在 Bash 中，你不需要显式地声明数组及其大小。当你开始给数组赋值时，它就被声明了。

```bash
# 声明并初始化数组
arr=(element1 element2 element3)

# 单独给数组元素赋值
arr[0]="First"
arr[1]="Second"
arr[2]="Third"
```

#### 2. 访问数组元素

使用 `${array_name[index]}` 语法访问数组元素。

```bash
echo ${arr[0]}  # 输出：First
echo ${arr[1]}  # 输出：Second
```

#### 3. 遍历数组

你可以使用循环来遍历数组中的所有元素。

```bash
# 使用 for 循环遍历数组
for element in "${arr[@]}" # 按照元素循环，直接取值
do
  echo $element
done

# 或者使用 C 风格的 for 循环
for (( i=0; i<${#arr[@]}; i++ )) # 按照索引进行循环，取值靠的是索引
do
  echo ${arr[$i]}
done
```

`${#arr[@]}` 用于获取数组的长度（即元素的个数）。

#### 4. 数组长度

获取数组的长度（即元素的数量）可以使用 `${#array_name[@]}` 或 `${#array_name[*]}`。

```bash
echo ${#arr[@]}  # 输出数组的长度
```

#### 5. 数组切片

Bash 数组支持切片操作，允许你获取数组的一部分元素。

```bash
echo ${arr[@]:1:2}  # 从索引 1 开始，获取 2 个元素（即 Second Third）
```

#### 6. 关联数组（Bash 4.0+）

Bash 4.0 及以上版本支持关联数组（或称为索引数组），即数组的索引可以是字符串。

```bash
# 声明关联数组（Bash 4.0+）
declare -A assoc_arr
assoc_arr[key1]="value1"
assoc_arr[key2]="value2"

# 访问关联数组
echo ${assoc_arr[key1]}  # 输出：value1

# 遍历关联数组
for key in "${!assoc_arr[@]}"; do
  echo "$key -> ${assoc_arr[$key]}"
done
```

#### 7. 删除数组元素

删除数组中的元素可以使用 `unset` 命令。

```bash
# 删除索引为 1 的元素
unset arr[1]

# 删除整个数组
unset arr
```

#### 总结

Bash 中的数组提供了灵活的方式来处理一系列的值。通过上面的介绍，你应该能够掌握如何在 Bash 脚本中使用数组了。记得，不同的 shell 对数组的支持可能有所不同，因此在实际应用中，最好查阅你所使用的 shell 的官方文档。

### shell编程案例

#### 提取ip地址

##### 预备知识

```shell
# 计算机的IP地址是由两部分组成 网络地址+计算机地址=计算机IP地址
# 网络地址怎么来的呢？局域网地址和互联网地址
# 子网掩码 通过ip地址和子网掩码相与，通俗用的1个个数 ip/20, 20 表示的20个1 255=11111111,用这个数表示子网掩码
# 网关 - 就是一个ip地址，是路由器的ip地址
# NAT - 地址转换
# DHCP - 动态获取ip地址
# DNS - 域名解析 将域名转成ip地址
```

##### 代码

```shell
# ip a执行的结果提取要ip地址
# 设计ip地址匹配正则表达式 ip_pattern="[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}"
# ip_pattern跟谁去匹配
# for循环 每行内容与ip_pattern,匹配成功就输出，最终都没有找到，输出0

#!/bin/bash
ip_contents=`ip a`
ip_pattern="[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}"
i=1
# i在这是为了统计循环���次数，或者理解为行
flag=0
# flag是一个标志，如果for循环后还没有找到，通过这个标志来告诉最终结果没有找到任何ip地址。
for line in $ip_contents
do
    # echo "输出每$i行内容"
    # echo $line
    # shell编程中，默认变量的值类型都是字符串，需要使用专门数学计算的方式
    # 在这里用的$(())
    i=$(($i+1))
    # if语句中匹配的规则使用的两个中括号 + =~,进行匹配，满足匹配成功就进入if语句，并输出line
    if [[ $line =~ $ip_pattern  ]]
    then
       echo "匹配了一个，请确认"
       echo $line
       # 在if语句中一旦找到，那么就需要把flag置1，用来进行最终结果的判断，1表示找到，0表示没有找到
       flag=1
    fi
done

if [ $flag -eq 0 ]
# -eq = equal
then
   echo "I'm sorry, any not found!!!"
fi
```

### shell编程提取ip地址v2

业务：提取IP

```shell
1. ip地址在哪？ - ip a
2. 找出127.0.0.1类似这样的东西，怎么找？找的方法是什么？提取数字，怎么提取？正则匹配 =~，匹配规则是什么？
3. 匹配成功，匹配失败 - if 语句判断
4. 输出结果 - echo
5. 定义哪些变量
6. 涉及的不是一行，那我怎么做，得循环 - for
1: lo: <LOOPBACK,UP,LOWER_UP> mtu 65536 qdisc noqueue state UNKNOWN group default qlen 1000
    link/loopback 00:00:00:00:00:00 brd 00:00:00:00:00:00
    inet 127.0.0.1/8 scope host lo
       valid_lft forever preferred_lft forever
    inet6 ::1/128 scope host
       valid_lft forever preferred_lft forever
2: ens33: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc pfifo_fast state UP group default qlen 1000
    link/ether 00:0c:29:59:b6:1e brd ff:ff:ff:ff:ff:ff
    inet **************/24 brd ************** scope global noprefixroute dynamic ens33
       valid_lft 640253sec preferred_lft 640253sec
    inet6 fe80::a26a:726:4053:5044/64 scope link noprefixroute
       valid_lft forever preferred_lft forever
```



```shell
#!/bin/bash

ip_contents=`ip a`
ip_pattern="\([0-9]\{1,3\}.[0-9]\{1,3\}.[0-9]\{1,3\}.[0-9]\{1,3\}\)"
i=1
# i在这是为了统计循环的次数，或者理解为行
flag=0
# flag是一个标志，如果for循环后还没有找到，通过这个标志来告诉最终结果没有找到任何ip地址。
for line in $ip_contents
do
   # 使用expr和match方法，提取匹配到的地址
   substring=$(expr match "$line" ${ip_pattern})   
   # substring=$(expr match "$line" '.*192.*')
   if [ -n "$substring" ]; then
       # -n 判断字符串是否为空???
       str_length=$(expr length ${substring})
       echo "${substring}  ${str_length}"
       flag=1
   fi
done

if [ $flag -eq 0 ]
then
   echo "I'm sorry, any not found!!!"
fi
```

##### get_ip

```shell
#!/bin/bash
ip_address=`ip a`
ip_pattern="([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3})\/24"
# 表示将命令的执行的结果赋值给ip_address
# echo ${ip_address}
echo 开始执行for循环----------------------------------
flag=0 # 表示没有找到

for i in ${ip_address}
do
# 开始执行

# 过程
# echo $i
if [[ ${i} =~ ${ip_pattern} ]];then
    # echo ${i}
    flag=1
    echo ${BASH_REMATCH[1]}
    # BASH_REMATCH内置的变量，用来存放匹配的内容
fi

done

if [[ $flag -eq 0 ]];then
    echo 很抱歉没有找到
fi
# 执行结束
```



```shell
#!/bin/bash
ip_address=`ip a`

if [[ $netmask == "-n" ]];then
    netmask=$2
fi

ip_pattern="([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3})\/${netmask}"
# 表示将命令的执行的结果赋值给ip_address
# echo ${ip_address}
echo 开始执行for循环----------------------------------

for i in ${ip_address}
do
# 开始执行

# 过程
# echo $i
if [[ ${i} =~ ${ip_pattern} ]];then
    # echo ${i}
    echo ${BASH_REMATCH[1]}
    # BASH_REMATCH内置的变量，用来存放匹配的内容
fi

done
# 执行结束
```

### 

### shell实现检查服务

```shell
# 业务  - 查看服务的状态，告诉我哪些服务是ok
# 怎么查 - systemctl， ps, ss (端口)
# 怎么查看端口 ss， netstat ss 
# 服务有哪些？服务名字，服务的名字放在-list，array（循环）
# 不要停留在想，想多少，做多少

# 第一版本
#!/bin/bash
# 说明脚本是什么类型的解释
echo "check the nginx"
# 如何将命令的执行结果赋值给一个变量
# $(cmd) 或者`cmd
chk_ngx=$(ss -lnp | grep nginx)
chk_redis=$(ss -lnp | grep redis)
chk_mysql=$(ss -lnp | grep mysql)
# 如何判断命令返回结果是否为空
# 判断结果是否为空  
if [[ -z $chk_ngx ]]; then  
    echo "grep结果为空，xxx程序没有启动。"  
else  
    echo "xxx程序已启动"  
fi
echo $chk_ngx
# 字符串中应用变量 "$var_name"
```

这是一个Bash脚本，用于检查一系列服务（如nginx，redis，rabbitmq和mysql）是否正在运行，并通过ss命令和grep来查找这些服务。然后，它会进一步提取并比较服务名与原始服务名是否匹配。下面是对这段代码的详细逐行注释：

```shell
# 数组的长度是${#array[@]}表示数组的长度
# ${array[i]}表示数组中指定索引位置的元素
# ${array[@]}表示将数组展开为所有元素的列表
# 循环检查服务
# 如何将获取的参数变成数组 params=($@)
#  ${params[*]} - 取出所有的列表（数组）中的数据
```


```bash
#!/bin/bash  # 声明使用的shell类型，这里是bash
service_array=("nginx" "redis" "rabbitmq" "mysql")  # 创建一个数组，存放需要检查的服务名
# 使用for循环遍历数组中的每一个服务
for ((i=0; i<${#service_array[@]}; i++))  
do  
    service="${service_array[i]}"  # 获取当前遍历到的服务名

    echo "starting to check: $service"  # 打印出正在检查的服务名

    # 构建一个命令，使用ss命令和grep来查找当前服务是否在运行
    chk_cmd="ss -lnp | grep $service" 
    echo "命令是:-------$chk_cmd"  # 打印出执行的命令

    # 执行命令并将结果存入变量result中
    # eval -作用是将字符串当做命令，字符串转成一条语句
    result=$(eval $chk_cmd) 

    # 如果result为空，说明没有找到服务，打印出服务未启动的信息
    if [[ -z "$result" ]]; then  
        echo "grep结果为空，$service程序没有启动。"  
        # 加任务
        # 服务没有启动，那么如启动？？？？？？
    else  
        echo "$service程序已启动"  # 如果找到了服务，打印出服务已启动的信息

        echo "提取service名字进行比较"  # 打印出正在提取服务名的信息

        # 提取服务名，这里用了两次awk命令，第一次提取出整个引号内的内容，第二次提取出引号内的内容（即服务名）
        srv_name=$(eval $chk_cmd | awk '{print $9}' | awk -F'\"' '{print $2}') 

        # 使用 == 和 =~ 进行匹配，如果服务名匹配成功，打印出匹配成功的信息，否则打印出匹配失败的信息
        if [[ "$srv_name" == *"$service"* ]]; then
            echo "匹配成功"
        else
            echo "匹配失败"
        fi
    fi
done
```


### shell实现容器运维的自动化

```shell
# 用shell控制启动容器
# 5个容器，参数 - 1 或者多个
# docker container start 容器的名字
# 启动animal 等待5秒钟 - if语句进行判断
# 获取所有的参数 - $@
# 如何将获取的参数变成数组 params=($@)

#!/bin/bash
# animal=$1
params=($@)
# 将命令行传入的参数作为整体转变成了数组
# params=(naginx,mq1,redis,minio,my1,animal)
# 将参数数组化，便于for循环
# 变量名观其名，知其意
for name in ${params[*]};
# ${params[*]} - 取出所有的列表（数组）中的数据
do
    echo "启动容器$name...."
    if [[ $name == "animal" ]];then
        echo "执行animal容器，所以将强制等待5秒"
        sleep 5
        # 等前面完了，你5秒后在执行。
    fi
    docker container start $name
done
```

### 日志

```shell
日志 - 按照时间，将程序执行的所有情况记录下来，写到文件中
# 作用
帮助开发，测试进行问题的定位，梳理逻辑
教学相长
```

#### 位置

```shell
widnows 日志
# 计算机管理->事件查看器->应用类，系统类，启动类

linux 日志
# /var/log

docker 日志
# /var/log
容器日志
# 属于应用的日志，在各自的容器当中
docker logs 容器的名字或者id
```

#### 关键字

```shell
# 1. error - 错误
# 2. exception - 异常，意外
# 3. abnormal - 不正常
# 4. unkown - 不知道
# 5. critial - 致命的
# 6. timeout - 超时
```

#### 分级

```shell
# bug - 优先级，严重等级
# 判断清楚轻重缓急
info - 打印相关的信息
warning - 警告
error - 错误的信息
critial - 致命的
```



## 补充



