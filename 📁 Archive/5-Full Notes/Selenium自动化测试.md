---
created: 2024-11-30
aliases: []
---

状态: [[进行中]]

标签: [[Selenium]] [[自动化测试]] [[learnpython]] [[Web测试]] [[UI自动化]] [[测试框架]] [[CSS选择器]] [[XPath]] [[ActionChains]] [[浏览器自动化]]

# Selenium 自动化测试学习笔记

## 1. CSS选择器详解 (Demo06)
CSS选择器是一种强大的元素定位方式，速度快且功能强大。

### 基本选择器
- 标签选择器：`input`、`div`、`a`
- ID选择器：`#id`
- 类选择器：`.class`
- 组合选择器：
  - 标签+ID：`input#kw`
  - 标签+类：`input.s_ipt`
  - 属性选择器：`[属性名=属性值]`

### 模糊匹配
- 开头匹配：`[属性名^=属性值]`
- 结尾匹配：`[属性名$=属性值]`
- 包含匹配：`[属性名*=属性值]`
- 分隔匹配：`[属性名~=属性值]`

### 层级选择器
- 子元素选择器：`>`
- 后代选择器：空格
- 兄弟选择器：`~`
- 相邻选择器：`+`
### 特殊选择器
- 第一个子元素：`:first-child`
- 最后一个子元素：`:last-child`
- 第n个子元素：`:nth-child(n)`

## 2. 浏览器操作 (Demo07)
浏览器常用操作方法：
```python
d.get(url)          # 打开网页
d.back()            # 后退
d.forward()         # 前进
d.refresh()         # 刷新
d.set_window_size() # 设置窗口大小
d.minimize_window() # 最小化窗口
d.maximize_window() # 最大化窗口
```

### 窗口句柄操作
```python
d.window_handles        # 获取所有窗口句柄
d.current_window_handle # 获取当前窗口句柄
d.switch_to.window()    # 切换窗口
```

## 3. 元素操作 (Demo08)
常用的元素操作方法：
```python
element.text            # 获取文本
element.send_keys()     # 输入文本
element.clear()         # 清除文本
element.is_displayed()  # 是否显示
element.is_selected()   # 是否选中
element.size           # 获取大小
element.get_screenshot_as_file() # 截图
```

## 4. iframe操作 (Demo09)
处理网页中的iframe：
```python
d.switch_to.frame()         # 切换到指定iframe
d.switch_to.parent_frame()  # 切换到父级iframe
d.switch_to.default_content() # 切换到最外层页面
```

## 5. 下拉框操作 (Demo10)
使用Select类处理下拉框：
```python
from selenium.webdriver.support.select import Select

select = Select(element)
select.select_by_visible_text("文本")  # 通过文本选择
select.select_by_index(2)             # 通过索引选择
select.select_by_value("value")       # 通过value值选择
```

## 6. 鼠标键盘事件 (Demo11)
使用ActionChains处理复杂的鼠标操作：
```python
from selenium.webdriver import ActionChains

ac = ActionChains(driver)
ac.move_to_element()    # 移动到元素
ac.context_click()      # 右键点击
ac.drag_and_drop()      # 拖拽
ac.double_click()       # 双击
ac.key_down()          # 按下键盘按键
ac.key_up()            # 释放键盘按键
ac.perform()           # 执行操作
```

## 定位方法选择建议
1. 优先使用id、name、class等固定且唯一的属性
2. 对于链接文本，优先使用link text
3. 其次选择CSS选择器（速度快、功能强大）
4. 最后考虑XPath（功能最强但速度最慢）


基础层：与业务无关，可复用。

# 参考资料
- Selenium官方文档
- Web自动化测试指南
- Python Selenium教程
- Web测试实践手册

# 相关笔记
- [[Selenium]]
- [[自动化测试]]
- [[learnpython]]
- [[Web测试]]
- [[UI自动化]]
- [[测试框架]]