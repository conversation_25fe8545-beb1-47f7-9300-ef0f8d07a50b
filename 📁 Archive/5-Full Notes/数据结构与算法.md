---
created: 2024-12-31 15:02
aliases:
---

状态:

标签: [[算法]]

# 数据结构
## 算法

### .递归

#### 概念：函数自己调用自己 ，把一个大问题分解成与自己相似的小问题

#### 例子 ：阶乘：循环,5! = 5 * 4* 3* 2 *1 递归 5! = 4! * 5 1！= 1 递归：必须有结束条件，终止条件、出口条件

#### for i in range(1, n + 1): r = r * i return r

### 顺序查找：

#### 概念：无序的列表中，查找某个元素是否存在。查找元素在那些位置存在 遍历序列，检查每个元素是否与目标一致

### 二分查找：

#### 概念： 有序的序列中，查找某个元素是否存在。查找元素在序列中的位置有哪些。 有序查找。 从小到大序列，key是不是存在 先用key 与中间值进行比较，如果比中间值小

### 排序

#### 排序分类

##### 冒泡排序，选择排序，插入排序，快速排序，归并排序

#### 冒泡排序：最简单，必须知道的，比较相邻元素，如果顺序不对，就将他们交换过来，直到没有可以交换的元素为止．数组中的元素排序-冒泡排序，比较相邻元素，顺序与要求的不一样，交换位置，直到没有可交换的为止。

##### 例子 ：4 3 6 8 1 0 9 10 进行冒泡排序 第一次：4 3 比较 3 4 6 8 1 0 9 10 第二次;4 6 比较，顺序不变 第三次 6 8比较顺序不变 ，第四次8 1 比较，交换位置3 4 6 1 8 0 9 10 第五次比较 8 0 ，多次重复上述操作，最大的元素到了最后，最小的到了最前方
##### 从小到大排序。{90，100，99，80，10}
##### 第一趟比较：
##### 90 100比较：90 100 99 80，10
##### 100 99比较：90 99 100 80，10
##### 100 80比较：90 99 80 100 10
##### 100 10比较：90 99 80 10 100最大的元素放到了最后
##### 外层循环，控制趟数
#### 快速排序：效率最高的

##### 例子：4 3 6 8 1 0 9 10 基准值 中间、最开始的元素作为基准 比基准值小的放前面，比基准值大的放后面，与基准值一样的放中间 4作为基准 3 1 0 4 6 8 9 10 ， 3 1 0 3作为基准 1 0 3 1 作为基准 0 1 3 出口条件，序列中只有一个元素，就是排序好的序列。




# 参考资料

# 相关笔记