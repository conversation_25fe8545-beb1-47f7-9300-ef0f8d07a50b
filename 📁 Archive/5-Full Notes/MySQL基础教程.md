---
created: 2024-10-30
aliases: []
---

状态: [[进行中]]

标签: [[MySQL]] [[数据库]] [[SQL]] [[后端开发]] [[数据库设计]] [[数据库优化]] [[事务]] [[存储过程]] [[索引]] [[视图]]

# MySQL学习笔记
## database
### 创建数据库 create database 数据库名  character set utf8;   # character设置字符格式
### 查看数据库 show databases;
### 使用数据库 use 数据库名；
### 删除数据 drop database 数据库名；
### 修改数据库 alter database 数据库名 character set  gbk;
## table 1 

### 数据类型
#### 字符串 char 、varchar  char是固定字符长度，varchar是可变的
#### 整数 int 、integer
#### 浮点数 float 、double（m , d) m:整数的长度+小数的长度  d : 小数的长度
#### 日期 date
#### 日期和时间 datetime
#### 时间 time
#### 文本 text 
### 约束
#### 主键约束 primary key 
#### 非空约束 not null
#### 唯一约束 unique
#### 默认值约束 default
#### 自增 auto_increment
### 创建表 create table 表名（列 类型 约束，列  类型  约束）;
### 重命名表 rename  to 新的表名
### 修改表 alter table 表名 修改内容
增加列 add 列名 类型 约束;
修改列 modify 列名 类型 约束;
删除列 drop 列名;
增加主键 add parimary key 
删除表 drop table 表名；
查看数据库中所有表：show tables；
查看建表语句 show create table 表名；
查看表结构 desc 表名; describe 表名;
## table 2
### 插入数据

#### INSERT INTO 表名（列名1，列名2，...） VALUES (值1，值2，...），（值1，值2，...）;  向表中插入多条记录。
### 更新数据

#### UPDATE 表名 SET 列名='值' WHERE 条件;  根据条件更新表中的记录。

### 删除数据

#### DELETE FROM 表名 WHERE 条件;

#### 根据条件删除表中的记录。

### 清空表

#### TRUNCATE TABLE 表名;  清空表中的所有记录
### 查询数据
#### SELECT * FROM 表名;  查询表中的所有行
### 分页查询
#### SELECT * FROM 表名 LIMIT 起始位置, 数量;    查询表中的记录并进行分页
### 排序查询
#### SELECT * FROM 表名 ORDER BY 列名 ASC/DESC;  按列名升序或降序查询表中的记录
### 条件查询
#### SELECT * FROM 表名 WHERE 条件;  根据条件查询表中的记录
### 聚合查询
#### SELECT DISTINCT 列名 FROM 表名; 查询表中列名的唯一值。 
### 函数
#### DELIMITER **     CREATE FUNCTION 函数名（参数列表）  RETURNS 返回类型  BEGIN    返回值;     END **    创建自定义函数。

### 调用函数

#### SELECT 函数名(参数列表);
#### 调用自定义函数，并传入参数。



## day3
### CONCAT  连接
#### SELECT carnumber, CONCAT(shengfen, location) AS 位置 FROM cars;  使用 `CONCAT` 函数连接 shengfen和 location字段，并为结果字段命名为 位置

### SUBSTRING  子字符串
#### SELECT carnumber, SUBSTRING(location, 1, 3) AS 城市 FROM cars;   使用 `SUBSTRING` 函数从 location字段中提取前三个字符，并为结果字段命名为城市。

### 数学函数
#### SELECT abs(-10); 绝对值
#### SELECT round(596.6); 596.6 四舍五入为597
#### SELECT round(596.4); 为596
#### SELECT rand() AS 随机数; 随机数函数
#### SELECT floor(3.6); 向下取整 3
#### SELECT ceiling(3.2); 结果为 4
#### SELECT ceiling(rand() * 100); 
#### SELECT floor(rand() * 100); 100以内的向下取整的随机数
### 聚合函数
#### SELECT count(*) AS 购买记录 FROM cart WHERE buyer = 'Tom';  满足条件的总行数
#### SELECT max(quantity) FROM cart WHERE buyer = 'Tom';  max:最大值
#### SELECT avg(quantity) FROM cart; avg 平均数
#### SELECT min(quantity) FROM cart; 最小值
#### SELECT sum(quantity) FROM cart; 满足条件的
### GROUP BY 
#### SELECT color 颜色, count( *) 数量 FROM cars GROUP BY color;  使用 `GROUP BY` 子句按 color字段分组，并统计每组的记录数。
###  HAVING 
#### SELECT color 颜色, count(*) 数量 FROM cars GROUP BY color HAVING 数量 > 1;   使用 `HAVING` 子句过滤 `GROUP BY` 的结果，只显示数量大于 1 的颜色。
### 子查询
#### SELECT Product FROM cart WHERE quantity = (SELECT MAX(quantity) FROM cart);    使用子查询获取 cart表中 quantity 最大值，并查询 quantity 等于该值的 Product。
### 多表联合查询
#### SELECT * FROM tb_student AS A, tb_score AS B WHERE A.sno = B.sno;     使用多表联合查询，连接 `tb_student` 和 `tb_score` 表。
 ### JOIN
#### SELECT * FROM tb_student A INNER JOIN tb_score B ON A.sno = B.sno;
#### SELECT * FROM tb_student A LEFT OUTER JOIN tb_score B ON A.sno = B.sno;
#### SELECT * FROM tb_student A RIGHT OUTER JOIN tb_score B ON A.sno = B.sno;   使用 `INNER JOIN` 和 `LEFT OUTER JOIN` 进行表连接查询。  
#### INNER: 两个表中都有的数据
#### LETF:只有左表有，右边表没有
#### RIGHT: 只有右边表有，左边表没有的
###  UNION 
#### SELECT sname, sex FROM tb_student WHERE sex = 'female'
#### UNION
#### SELECT t_name, sex FROM tb_teacher WHERE sex = '女';
#### 使用 `UNION` 子句合并两个查询结果。


## day 4

### 索引
#### 显示索引：SHOW INDEX FROM tb_student; -- 显示tb_student表的索引
#### 创建索引
#### CREATE INDEX abc ON tb_student(sname); -- 在tb_student表的sname字段上创建索引abc
### 删除索引
#### DROP INDEX abc ON tb_student; -- 删除tb_student表上的索引abc
### 视图
#### 创建视图
#### CREATE VIEW student_view AS -- 创建视图student_view
#### SELECT a.*,  b.subject, b.score
#### FROM tb_student a
#### LEFT JOIN tb_score b ON a.sno = b.sno; -- 连接学生和分数表
### 存储过程
#### 创建存储过程
#### DELIMITER **
#### CREATE PROCEDURE myAvg(in id INT, out avg_score FLOAT) -- 创建存储过程myAvg，输入学号，输出平均分
#### BEGIN    #### SELECT AVG(b.score) INTO avg_score
 #### FROM tb_student a
#### LEFT JOIN tb_score b ON a.sno = b.sno
#### WHERE a.sno = id; -- 根据学号查询平均分
#### END**
#### 调用存储过程
#### CALL myAvg(1, @avg1); -- 调用存储过程myAvg，传入学号1
#### SELECT @avg1; -- 查询存储过程返回的平均分
#### 删除存储过程
#### DROP PROCEDURE myAvg; -- 删除存储过程myAvg
### 事务操作
#### 创建存储过程并使用事务
#### DELIMITER **
#### CREATE PROCEDURE update_accounts()
#### BEGIN
#### START TRANSACTION; -- 开始事务

#### UPDATE accounts SET balance = balance - 100.00 WHERE account_id = 'a'; -- 更新账户a的余额

#### DECLARE aaa DECIMAL(10,2);
#### SELECT balance INTO aaa FROM accounts WHERE account_id = 'a'; -- 获取账户a的新余额

#### IF aaa <= 0 THEN
#### ROLLBACK; -- 如果余额小于等于0，回滚事务
#### ELSE
python脚本，函数体 ，
#### UPDATE accounts SET balance = balance + 100.00 WHERE account_id = 'b'; -- 否则，更新账户b的余额
#### COMMIT; -- 提交事务
#### END IF;
#### END **
#### DELIMITER ;
### 用户权限管理  

#### 授权
#### GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' IDENTIFIED BY '123456' WITH GRANT OPTION; -- 授权给用户root所有权限
#### FLUSH PRIVILEGES; -- 刷新权限

### 数据库中的引擎以及SQL分类

#### 数据库引擎是数据库管理系统的核心组件，负责处理数据的存储、检索和更新。以下是一些常见的数据库引擎：
### InnoDB
- **支持事务**：支持ACID事务。
- **存储特性**：支持行级锁定和外键约束。
- **适用场景**：适用于需要事务支持和高并发的场景。

### MyISAM
- **不支持事务**：不支持事务。
- **存储特性**：支持全文搜索。
- **适用场景**：适用于读取密集型的应用，不需要事务支持。

### Memory
- **存储特性**：将所有数据存储在内存中，提供快速访问。
- **适用场景**：适用于临时表和快速访问的小规模数据。

### Archive
- **存储特性**：用于存储大量未修改的数据，数据压缩能力强。
- **适用场景**：适用于存储历史数据和备份数据。

### Federated
- **存储特性**：允许访问远程数据库服务器上的表。
- **适用场景**：适用于需要跨数据库服务器操作的场景。

### SQL分类

#### SQL（Structured Query Language）是用于管理和操作关系数据库的标准化编程语言。SQL可以分为以下几类：

#### 数据定义语言 (DDL)
用于定义和管理数据库结构的语言。
CREATE TABLE, ALTER TABLE, DROP TABLE, CREATE INDEX, DROP INDEX 等。
#### 数据操纵语言 (DML)
##### 用于查询和修改数据库中数据的语言。
###### SELECT, INSERT, UPDATE, DELETE 等。
### 数据控制语言 (DCL)
#### 用于定义数据库的安全策略和访问权限的语言。
#### GRANT, REVOKE 等。
### 事务控制语言 (TCL)
#### 用于管理数据库事务的语言。
BEGIN TRANSACTION, COMMIT, ROLLBACK 等。
### 数据查询语言 (DQL)
#### 专门用于查询数据库中数据的语言，主要是 SELECT 语句。
SELECT 用于查询数据 
Docker is 

# 参考资料
- MySQL官方文档
- SQL教程
- 数据库设计指南
- MySQL性能优化手册

# 相关笔记
- [[MySQL]]
- [[数据库]]
- [[SQL]]
- [[数据库设计]]
- [[数据库优化]]
- [[后端开发]]