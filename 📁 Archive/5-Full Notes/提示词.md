
## 前端UI界面设计提示词

我想设计一个类似外卖小程序「美团」的小程序，名为「斑马小超」。请帮助我生成所有界面的UI设计原型，确保这些原型可以直接用于前端UI开发，并满足以下要求：

1. 用户体验基础
    
    - 基于「斑马小超」的主要功能（如商品浏览、购物车、订单管理、个人中心），设计直观的核心界面。
        
    - 注重简洁性和易用性，符合微信小程序用户的操作习惯。
        
2. 界面规划
    
    - 设计关键页面，包括但不限于：首页、商品详情页、购物车、订单确认页、个人中心。
        
    - 使用微信小程序常见的导航结构，例如底部 tabBar，确保页面布局清晰。
        
3. 高保真UI设计
    
    - 设计符合微信小程序风格的界面，使用推荐的颜色、字体和图标样式。
        
    - 界面应美观且一致，贴近真实的微信小程序视觉体验。
        
4. HTML原型输出
    
    - 使用 HTML + Tailwind CSS 生成所有界面的UI原型代码。
        
    - 可结合 FontAwesome 或其他开源图标库增强设计感。
        
    - 每个界面独立保存为单独的 HTML 文件（如 home.html、cart.html），并在 index.html 中通过 iframe 平铺展示所有页面。
        
5. 真实感设计
    
    - 界面尺寸模拟 iPhone 15 Pro（或类似手机尺寸），并添加圆角效果。
        
    - 使用真实的图片资源（可从 Unsplash、Pexels 获取），避免占位符。
        
    - 包含顶部状态栏和底部导航栏，模拟真实的手机应用界面。
        

请根据以上要求生成完整的HTML代码，用于「斑马小超」的前端UI设计。

---

说明

这个提示词专注于UI界面的设计和原型生成，不涉及小程序开发的具体实现（如WXML、WXSS或JS逻辑）。生成的内容将以HTML格式输出，方便你在前端调整后再转换为小程序代码。你可以直接使用这个提示词生成UI原型，调整后再进入开发阶段。