---
created: 2024-10-25
aliases: []
---

状态: [[进行中]]

标签: [[Linux]] [[系统管理]] [[服务部署]] [[命令行]] [[权限管理]] [[进程管理]]

# Linux系统管理与服务部署
## systemctl 服务控制
### systemctl 
#### status 查看目前的服务状态
#### 启动，停止，重启 自启动 禁止启动 start  stop resert enable disable
## find后面可以直接跟管道符 |
## 查看端口号 ss -lnp | grep
## 进程
### 查找进程号 ps -ef  
### ps -ef | grep 检索程序名
### 杀进程 kill -9 pid;9是数字信号，强制退出，pid是进程运行时出现的号
## 访问不到服务的时候，如何处理？
### 1,服务一般署在1iux,一般在不在自己的身边，远程部署的
### 2.判断网路是否畅通，ping对方的服务的ip地址
### 3.如果网通了，依然无法访问，那么就要查看服务的状态？
#### 3.1查看端口是否被占用，ss(netstat).,ss-lnp|grep目标端口号
#### 3.2s5查看它的进程，确定进程的名字，是不是自己想要的，如果是那么说明端口号正常，没有被占用
### 4,查看服务的状态，正常，重新启动服务。
#### 4.1 systemctl,
#### 4.2还有其他的命令的方式和即s查看进程，启动命令是否正常
#### 5,依然有问题，需要查看服务为什么启动有异常，看日志
## 权限
### useradd -xxx 添加用户
### 组相关的操作
#### groupadd -xxx 添加组
##### usermod -aG 将用户添加到某个组。eg: usermod -aG zhuzi lhz 意思是将lhz这个用户添加到zhuzi这个组中
#### groups 查看组
#### 修改组 chgrp zhuzi  /data/b /a.txt 给a.txt文件修改组为zhuzi
#### 组权限分类
##### a - all
##### g - group
##### u - user
##### o - other
### 权限的操作
#### 添加权限 chmod g+w 对象（目录或者文件）
##### + 添加 ，- 删除 

# 参考资料
- Linux系统管理手册
- Linux服务部署指南

# 相关笔记
- [[linux全部笔记]]
- [[Linux基础命令和软件测试入门]]
- [[Linux环境下的Java项目部署]]
- [[Linux中间件服务安装与配置]]












