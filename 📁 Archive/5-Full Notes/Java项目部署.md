---
created: 2024-10-25
aliases: []
---

状态: [[进行中]]

标签: [[Linux]] [[项目部署]] [[3-Tags/Java]] [[JDK]] [[MySQL]] [[Nginx]] [[服务配置]]

# Linux环境下的Java项目部署

## jdk安装
### 1.将 jdk.tar.gz 上传文件到linux系统
#### 使用mobaXterm
#### 使用scp命令
### 2.进入/opt目录 cd /opt
### 3.解压缩  tar
### 4.验证结果 java -version
## linux 镜像源修改
### 进入目录下 cd /etc/yum.repos.d
### 使用cp 备份仓库配置文件
### 使用 curl 命令修改镜像源
### 使用 yum clean all 
### 使用 yum makecache fast
## 配置MySQL服务
### 1启动服务
#### systemctl status mysqld
#### systemctl startt mysqld
#### 添加开机自启动 systemctl enable mysqld
### 2.找MySQL生成的临时密码
#### grep "temporary password" /var/log/mysqld.log
### 3.修改root密码
#### mysql -u root -p
#### mysql>alter user root@localhost identified by "!QAZ2wsx" 密码修改为引号中的内容
#### exit退出后重新使用新密码进入即可
### 安装navicat
#### 使用1.grant all privileges on *,* to root@'%' identified by"!QA?2wsx" 2flush privileges ;
#### MySQL连接Linux主机
## 安装nginx
### 安装源 yum install epel-release
### 安装nginx  ; yum install nginx
### 配置服务使用 systemctl start nginx 来启动服务
### 验证成功  windows的浏览器中输入Linux主机ip地址
## HTML学习
### "<!DOCTYPE html>" 是一个标签，
### <></ >  其中/ 表示该标签结束
## 部署过程中出现的错误
### 完成视频播放页面时，刷新失败，使用F12检查后，发现html文件中视频的地址写错，与存储路径 不一致。

# 参考资料
- Linux部署手册
- MySQL配置指南
- Nginx部署文档

# 相关笔记
- [[linux全部笔记]]
- [[MySQL学习笔记]]
- [[Linux中间件服务安装与配置]]
- [[Linux项目部署流程与服务检查]]










