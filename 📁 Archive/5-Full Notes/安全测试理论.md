---
created: 2024-12-27 09:53
aliases:
---

状态:

标签:

# 安全测试基础
## 安全问题影响比较大
### xx用户的邮箱信息被泄露
### 优衣库用户个人信息、电子右键、地址、个人信息、银行卡等资料被泄露
### B站后台源码被上传到github上
### facebook，用户信息任由下载
### 雅虎邮箱信息泄露
### 2017年，勒索病毒,除非支付，否则删除文件
##  OWASP Open web Applicatio Security  Project 非盈利机构，每隔一段时间会公布TopN的安全问题。
OWASP提供了一个学习的平台
有若干个漏洞的应用
## 文件上传漏洞
大部分的web应用都有文件上传的功能，恶意攻击者，可能上传了木马文件、病毒、恶意脚本等，在服务器上执行，获得非法的数据
原理：
开发人员在处理文件爱你时，合法性未做校验，就把文件上传到服务器中。
测试
解决办法

XSS攻击，跨站脚本攻击


SQL注入
原理：对用户输入的数据没有进行检查，直接将用户输入的内容与后台的sql语句拼成一个完整的sql来执行了。比如拖库，导致用户信息泄露
基于错误的注入
通过输入错误的信息，与后台的sql语句拼接成一个非法的sql语句来执行了

DDos攻击
分布式阻断服务
# 参考资料

# 相关笔记



