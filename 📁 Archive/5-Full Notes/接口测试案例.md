---
created: 2024-12-09
aliases: [API Testing]
---

状态: [[进行中]]

标签: [[接口测试]] [[软件测试]] [[自动化测试]] [[HTTP协议]] [[RESTful API]] [[Postman]] [[性能测试]] [[安全测试]] [[Python]] [[Pytest]]

# 接口测试理论与实践

## 1. 基础概念
### 1.1 接口测试概述
- API (Application Programming Interface) 应用程序接口
- 接口是连接前端和后端的纽带，用于传递数据
- 从功能层面理解，接口是一个黑盒子，接收参数并返回结果
- 从代码层面看，接口就是一个函数/方法

### 1.2 接口类型
- WebService接口 (SOAP协议)
- WebSocket接口 (TCP/UDP)
- HTTP接口 (RESTful API)

## 2. HTTP协议基础
-  http是互联网使用最广泛的一种网络传输协议。协议特点：
    - 无连接：每次请求建立连接，请求结束断开连接。
    - 无状态：没有记忆能力，不知道之前传过什么，不知道客户端当前什么状态。
    - 简单：只能客户端发起请求（request）。服务器处理后给一个响应（response）。
    - 请求过程
- ![[Pasted image 20241210184444.png]]
- 请求报文
    - 请求行（第一行）：GET https://www.baidu.com/HTTP1.1
        - 请求方法：get、post、delete、put、connect、patch、trace、head……
            - get方法
            - post方法
            - get和post的区别
                - - get对数据长度有限制，post没有限制。get参数在URL后面拼接，浏览器对URL的长度有限制。  
                - get对数据类型有限制，post没有限制。get支持ASCII字符，非ASCII字符会使用URLEncoded编码方式进行编码。post可以是任何类型的数据，比如通过接口上传图片、视频、音频，通过post方法可以实现。  
                - get只支持URLEncoded编码方式，post支持多种编码方式。  
                - get在浏览器上点击后退时无害。post的数据会被重新提交。  
                - get的URL可以收藏为书签，post不可收藏为书签  
                - get会被浏览器主动缓存，post不会被浏览器主动缓存，  
                - get相对来说不安全，post更安全。
        - URL,接口地址
        - 协议版本号
    - 请求头（若干行，key：value）：给请求报文添加一些附加信息
        - host：主机地址
        - User-Agent：浏览器、操作系统版本号等
        - Content-Type：请求体中数据的类型
            - application/x-www/from-urlencoded 表单格式的数据（html中的from表单）租车、金融（开发人员设计的）
                - key=value&key=value
            - application/json json 格式的数据 学之思  （开发人员设计的）
                -   {key:value,key:value,key:value}
            - image/png 图片
            - inage/jpeg 图片
            - multipart/from-data 上传文件
            - text/plain 文本格式
            - text/html html格式的
        - Content-Length：请求体中数据的长度
            - Content-Length：22
        - Accept:客户端能够处理哪些类型的数据
        - Accept-Encoding：客户端能够处理的编码类型
        - Cookie：标识客户
        - 空行
        - 请求体（可选）

- 响应报文
    - 状态行
        - 协议版本
        - 状态码
            - 1**  ：（1开头的三位数字）信息，服务器收到请求，客户端继续执行操作
            - 2** ：成功，服务器正确处理了请求
                - 200 OK 服务器成功处理
            - 3**: 重定向。需要客户端进一步完成请求。location字段显示资源到新的URL.
                - 301 永久重定向。所请求的页面资源，永久的换到了别的地方。
                - 302 Move Temporary ：临时重定向。所请求的页面资源临时切换到了其他地方。
            - 4**： 客户端错误 ,客户访问了不存在的、没有权限的连接
                - 400 Bad Request，url有语法错误，服务器无法理解
                - 400 Not Found，请求的资源不存在，服务器无法找到请求的页面，资源被删除了，输入的URL有问题
                - 403 Forbidden，请求的页面禁止访问
            - 5**：服务器端错误。服务器在处理请求时有问题了。
                - 500 Internal Server Error 服务器内部错误
                - 503 Service Unavailable：服务器当前不可用，一段时间之后可恢复正常
                - 504 GateWay timeout：处理超时。
            - 状态码描述
    - 响应头
        - Conntent-Type：标识响应体中的数据类型
        - Set-Cookie：识别用户信息
    - 空行
    - 响应体
- 识别客户
    - http无连接无状态的，不记录之前用户访问了什么，不记得用户什么状态。但是现在的产品，需要识别客户，针对客户的喜好定制化的推送、定制化的，怎么识别客户？
- cookie
    - 原理
    - ![[Pasted image 20241210193150.png]]
    - set-cookie是在响应头中使用的
        - Set-Cookie: lang=zh-cn; expires=Thu, 09-Jan-2025 06:42:46 GMT; Max-Age=2592000; path=/
        - Set-Cookie: device=desktop; expires=Thu, 09-Jan-2025 06:42:46 GMT; Max-Age=2592000; path=/; HttpOnly
        - Set-Cookie: theme=default; expires=Thu, 09-Jan-2025 06:42:46 GMT; Max-Age=2592000; path=/
    - cookie 是在请求头中使用的
    - cookie是什么：cookie是一段文本信息
        - cookie 名字： lang
        - 过期时间：expires、Max-Age
            - 会话cookie：没有设置过期时间。保存在内存中，重启浏览器，重启电脑。，cookie就失效了。
            - 持久cookie：设置了过期时间，保存在硬盘中，重启浏览器，重启电脑，只要在有效期内，cookie就生效。
        - 路径：path，cookie针对哪个path生效
        - HttpOnly：设置了这项，可以防止xss攻击，JavaScript 无法获取着这种cookie
        - secure：只有使用https协议时，才会发送cookie，http协议时，不发送。
    - - session  
        - 原理![](https://api2.mubu.com/v3/document_image/17164948_64aa3423-1451-44c9-dbec-10668cb1f2d7.png)
        - 学之思  
        - POST [http://************:8000/api/user/login](http://************:8000/api/user/login) HTTP/1.1  
            - 响应头中  
                - Set-Cookie: JSESSIONID=RVianDcwke2BgEb_5B_hX7rAeN-rppGiExwRC5p1; path=/  
        - POST [http://************:8000/api/admin/user/page/list](http://************:8000/api/admin/user/page/list) HTTP/1.1  
            - 请求头中  
                - Cookie：JSESSIONID=RVianDcwke2BgEb_5B_hX7rAeN-rppGiExwRC5p1  
        - Cookie与Session的区别是什么？  
            - Cookie相当于服务器给客户端发了一个通行证，每次带着通行证来访问。Session类似于服务器存储了客户明细表，id、身份信息。将id给客户端，客户端每次带着id来访问。  
            - 安全性：Cookie存储在客户端，不安全。Session存储在服务器端，更安全。  
            - 数据类型：Cookie只支持字符串数据，如果要设置其他类型的数据，需要把其他类型转成字符串。Session可以存任意类型数据。  
            - 有效期：Cookie有效期一般比较长，通过Cookie实现免登录。Session一般有效期比较短，客户端一段时间不操作（30分钟），Session就失效了。  
            - 存储大小：单个Cookie存储的数据不超过4K。Session在服务器上存储，没有限制。Session会占用服务器资源。  
        - token  
            - 既解决了Cookie不安全的问题，又解决了Session占用服务器资源的问题。![](https://api2.mubu.com/v3/document_image/17164948_02b8a1f2-7fb0-4fad-f5ff-243c09c70e92.png)
            - Token是服务器生成的一串字符串。uid（用户身份标识）+时间戳+签名+盐值生成的一段文本  
            - token不在服务器上保存，服务器收到token后，用算法和密钥，计算是否正确。
- HTTPS
    - http与https接口测试上没啥区别
    - http缺点
        - 使用明文传输，内容可能会被窃听
        - 不校验通信双方的身份，有可能遭到伪装
        - 不校验报文完整性，报文可能会被篡改
    - https解决了上述问题
        - 通信加密传输
        - 通过证书校验身份
        - 完整性保护
    -    http与https的差别
        - HTTP不安全，https在http的基础上增加了安全层（TLS/SSL）更加安全，通信加密传输，通过证书校验身份，完整性保护。
        - http默认使用80端口，https使用443端口。
        - http不加密和解密，不占用服务器资源，https需要加密和解密，占用服务器资源
        - http成本低，不需要证书，开发成本低，https需要证书，证书是收费的，成本高。 
- Fiddler
    -  用来抓取HTTP协议的报文的工具。Wireshark也是一个抓包工具，抓的包更底层一些。
    - 什么情况下使用fiddler
        - 接口测试时，开发没有提供接口文档，前端页面已经是实现在前端页面操作相应功能，抓取接口
        - 模拟弱网环境
        - 抓取手机的报文
        - 做简单的接口测试
        - 篡改数据，界面对某个功能做了限制，无法输入非法值，但是测试时，需要测试某个非法值，可以通过fiddler抓取报文后篡改数据
        - 定位前端问题还是后端问题
    - fiddler抓包原理
        - 停止抓包时，代理服务是关闭的，启动抓包时，代理服务器是打开的
        - ![[Pasted image 20241211192255.png]]
        - ![[Pasted image 20241211091558.png]]
    - 篡改请求
        - 对请求加断点
            - bpu 接口地址
            - bpu https://www.baidu.com 对百度打断点
            - ![[Pasted image 20241211192451.png]]
            - 对请求取消断点
                - bpu 
            - 对响应加断点
                - bpafter [https://www.baidu.com/s](https://www.baidu.com/s) （breakpoint）
            - 对响应取消断点
                - bpafter

## 3. 接口测试流程
### 3.1 测试准备
- 需求分析
- 测试计划
- 测试分析
- 测试设计
- 环境搭建

### 3.2 测试执行
- 功能测试
- 性能测试
- 安全测试
- 异常测试

## 4. 测试工具
### 4.1 Postman
[原有Postman相关内容]

### 4.2 Python Requests
[原有Python-requests相关内容]

### 4.3 测试框架
[原有测试框架相关内容]

## 5. 最佳实践
### 5.1 测试用例设计
- 参数验证
- 返回值验证
- 异常处理
- 性能验证

### 5.2 自动化测试
- 框架选择
- 数据驱动
- 持续集成
- 报告生成

# 参考资料
- HTTP协议规范
- 接口测试指南
- RESTful API设计指南
- 自动化测试框架文档
- 安全测试指南

# 相关笔记
- [[接口测试]]
- [[软件测试]]
- [[自动化测试]]
- [[HTTP协议]]
- [[RESTful API]]
- [[性能测试]]
- [[安全测试]]
- [[Postman]]
- [[Python]]
- [[Pytest]]