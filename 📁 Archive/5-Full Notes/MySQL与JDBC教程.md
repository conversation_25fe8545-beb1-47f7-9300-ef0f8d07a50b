created: 2024-12-28 13:05
aliases:
---

状态:

标签:

# 01.mysql与jdbc

# 参考资料

# 相关笔记
# MySQL快速入门
## SQL语句
### SQL语句概述

1.SQL 是用于访问和处理数据库的标准的计算机语言。

2.SQL指结构化查询语言，全称是 Structured Query Language。

3.SQL 可以访问和处理数据库。

4.SQL 是一种 ANSI（American National Standards Institute 美国国家标准化组织）标准的计算机语言。

### SQL语句特点
1. 具有综合统一性，不同数据库的支持的sql稍有不同

为许多任务提供了统一的命令，这样方便用户学习和使用，基本的 SQL 命令只需很少时间就能学会，甚至最高级的命令也可以在几天内掌握。数据库的操作任务通常包括以下几方面：增、删、改、查

常见数据库（mysql、sqlserver、oracle、db2等）

2. 非过程化语言 

3.语言简捷，用户容易接受 <font style="color:rgb(51, 51, 51);">select , drop, alter, create, insert, update ,delete</font>

select *  from  mayikt_users ----查询 mayikt_users 的数据

4.集合性

SQL 可以在高层的数据结构上进行工作，工作时不是单条地处理记录，而对数据进行成组的处理语句都接受集合作为输入，并且返回集合作为输出

### SQL语法特点

1.SQL 对关键字大小不铭感;

2.SQL语句可以单行或者多行数据，以分行结束;

3.SQL语句注释：

-- 单行注释 （后面一定要加一个空格）

#单行注释  #后面可以不用加空格

/*
多行注释
多行注释
*/

## 数据库系统简介
数据库是一个以某种有组织的方式存储在硬盘上数据集合；

| id  | name（名称） | age(年龄) |
| --- | -------- | ------- |
| 1   | mayikt   | 23      |
| 2   | meite    | 28      |
| 3   | zhangsan | 18      |
| 4   | lisi     | 19      |

![数据库表格示例](../Attachements-附件/mysql_table_example.png)

数据库应用场景  以后开发JavaWeb项目  背后的数据都是存放在数据库（互联网）中。

## 数据库系统的分类

A.关系型数据库(RDBMS)

1.Oracle数据库  （甲骨文公司） 收费的

2.MySQL数据库（最流行的数据库） 免费版本 源代码开源

3.SQLServer 数据库 （微软开发的数据库）c#（微软公司） windows

4.Sqlite（嵌入式关系数据库） 学习  安卓手机端程序开发

5.db2

B.非关系型数据库(NoSQL)

1.Redis（缓存数据库） 

2.Mongodb（文档数据库��

3.Elasticsearch(搜索服务)

4.Hbase（分布式、列示数据库）

## SQL与数据库的关系

1.SQL是一种用于操作数据库的语言，SQL适用于所有关系型数据库

2.MySQL、Oracle、SQLServer、DB2 是一个数据库软件，这些数据库软件支持标准SQL，也就是通过SQL可以使用这些软件，不过每一个数据库系统会在标准SQL的基础上扩展自己的SQL语法，大部分的3.NoSQL数据库有自己的操作语言，对SQL支持的并不好。

SQL 属于 数据库编程语言  数据库 MySQL、Oracle、SQLServer、DB2 数据库软件

## MySQL简介

简介：

1.MySQL数据库管理系统由瑞典的DataKonsultAB公司研发，该公司被Sun公司收购，现在Sun公司又被Oracle公司收购，因此MySQL目前属于 Oracle 旗下产品。

2.MySQL所使用的 SQL 语言是用于访问数据库的最常用标准化语言。MySQL 软件采用了双授权政策，分为社区版（免费版本）和商业版（收费），由于其体积小、速度快、总体拥有成本低，一般中小型网站的开发都选择 MySQL 作为网站数据库。

![MySQL Logo](../Attachements-附件/mysql_logo.jpeg)

特点：

1.MySQL数据库是用C和C++语言编写的，以保证源码的可移植性

2.支持多个操作系统例如：Windows、Linux、Mac OS等等

3.支持多线程，可以充分的利用CPU资源

4.为多种编程语言提供API，包括C语言，Java，PHP、Python、go语言等

5.MySQL优化了SQL算法，有效的提高了查询速度

6.MySQL开放源代码且无版权制约，自主性强、使用成本低。

7.MySQL历史悠久、社区及用户非常活跃，遇到问题，可以很快获取到帮助。

## MySQL的安装与卸载
### 安装
#### 解压安装
MySQL的安装两种方式（MySQL8.0）

1.解压版本 或者直接安装方式

[https://dev.mysql.com/downloads/mysql/](https://dev.mysql.com/downloads/mysql/) 

![MySQL下载页面](../Attachements-附件/mysql_download_page.png)

![MySQL版本选择](../Attachements-附件/mysql_version_select.png)

1.解压mysql-8.0.27-winx64

![MySQL解压目录结构](../Attachements-附件/mysql_folder_structure.png)

注意：不要放在有中文名字和空格的的目录下

2.在mysql-8.0.27-winx64文件夹下面新建一个my.ini文件和一个data文件夹

![MySQL配置文件和数据目录](../Attachements-附件/mysql_ini_data_folder.png)

使用mysql 数据 ----data文件夹中

3.my.ini 添加以下内容：

```ini
[mysqld]
# 设置3306端口
port=3306
# 设置mysql的安装目录
basedir=D:\\mysql-8.0.27-winx64
# 设置mysql数据库的数据的存放目录
datadir=D:\\mysql-8.0.27-winx64\\data
# 允许最大连接数
max_connections=200
# 允许连接失败的次数。这是为了防止有人从该主机试图攻击数据库系统
max_connect_errors=10
# 服务端使用的字符集默认为UTF8
character-set-server=utf8
# 创建新表时将使用的默认存储引擎
default-storage-engine=INNODB
[mysql]
# 设置mysql客户端默认字符集
default-character-set=utf8
[client]
# 设置mysql客户端连接服务端时默认使用的端口
port=3306
default-character-set=utf8
```

4.配置系统环境

我的电脑→属性→高级→环境变量→系统变量→新建

变量名：MYSQL_HOME
变量值：D:\path\mysql\mysql-8.0.27-winx64  （也就是刚解压的地方）

Path 中新建一段：%MYSQL_HOME%\bin

![MySQL环境变量配置1](../Attachements-附件/mysql_env_path1.png)

![MySQL环境变量配置2](../Attachements-附件/mysql_env_path2.png)

5.以管理员的身份打开cmd窗口跳转路径到D:\path\mysql\mysql-8.0.27-winx64，依次输入以下命令： 注意：是以管理员的身份打开cmd

1.初始化：mysqld --initialize --user=mysql --console

![MySQL初始化结果](../Attachements-附件/mysql_initialize_result.png)

初始化 mysql 成功之后 账户和密码

账户：root
密码：随机密码 ,8s5%IoDpgu,

2.添加服务  mysqld -install

![MySQL安装服务](../Attachements-附件/mysql_install_service.png)

3.启动服务  net start mysql


































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































---





































































created: 2024-12-28 13:05
aliases:
---

状态:

标签:

# 01.mysql与jdbc




# 参考资料

# 相关笔记
# MySQL快速入门
## SQL语句
### SQL语句概述


1.SQL 是用于访问和处理数据库的标准的计算机语言。

2.SQL指结构化查询语言，全称是 Structured Query Language。

3.SQL 可以访问和处理数据库。

4.SQL 是一种 ANSI（American National Standards Institute 美国国家标准化组织）标准的计算机语言。

### SQL语句特点
1. 具有综合统一性，不同数据库的支持的sql稍有不同

为许多任务提供了统一的命令，这样方便用户学习和使用，基本的 SQL 命令只需很少时间就能学会，甚至最高级的命令也可以在几天内掌握。数据库的操作任务通常包括以下几方面：增、删、改、查

 常见数据库（mysql、sqlserver、oracle、db2等）

2. 非过程化语言 

3.语言简捷，用户容易接受 <font style="color:rgb(51, 51, 51);">select , drop, alter, create, insert, update ,delete</font>

  select *  from  mayikt_users ----查询 mayikt_users 的数据

4.集合性

SQL 可���在高层的数据结构上进行工作，工作时不是单条地处理记录，而对数据进行成组的处理语句都接受集合作为输入，并且返回集合作为输出

### SQL语法特点


1.SQL 对关键字大小不铭感;

2.SQL语句可以单行或者多行数据，以分行结束;

3.SQL语句注释：

-- 单行注释 （后面一定要加一个空格）

#单行注释  #后面可以不用加空格

/*

多行注释

多行注释

*

/

## 数据库系统简介
数据库是一个以某种有组织的方式存储在硬盘上数据集合；

| id | name（名称） | age(年龄) |
| --- | --- | --- |
| 1 | mayikt | 23 |
| 2 | meite | 28 |
| 3 | zhangsan | 18 |
| 4 | lisi | 19 |


![数据库表格示例](../Attachements-附件/mysql_table_example.png)

数据库应用场景  以后开发JavaWeb项目  背后的数据都是存放在数据库（互联网）中。
![[Pasted image 20241228130735.png]]
**
## 数据库系统的分类


A.关系型数据库(RDBMS)





































































1.Oracle数据库  （甲骨文公司） 收费的

2.MySQL数据库（最流行的数据库） 免费版本 源代码开源

3.SQLServer 数据库 （微软开发的数据库）c#（微软公司） windows

4.Sqlite（嵌入式关系数据库） 学习  安卓手机端程序开发

5.db2





B.非关系型数据库(NoSQL)

1.Redis（缓存数据库） 

2.Mongodb（文档数据库）

3.Elasticsearch(搜索服务)

4.Hbase（分布式、列示数据库）

## SQL与数据库的关系


1.SQL是一种用于操作数据库的语言，SQL适用于所有关系型数据库

2.MySQL、Oracle、SQLServer、DB2 是一个数据库软件，这些数据库软件支持标准SQL，也就是通过SQL可以使用这些软件，不过每一个数据库系统会在标准SQL的基础上扩展自己的SQL语法，大部分的3.NoSQL数据库有自己的操作语言，对SQL支持的并不好。

SQL 属于 数据库编程语言  数据库 MySQL、Oracle、SQLServer、DB2 数据库软件



## MySQL简介


简介：

1.MySQL数据库管理系统由瑞典的DataKonsultAB公司研发，该公司被Sun公司收购，���在Sun公司又被Oracle公司收购，因此MySQL目前属于 Oracle 旗下产品。

2.MySQL所使用的 SQL 语言是用于访问数据库的最常用标准化语言。MySQL 软件采用了双授权政策，分为社区版（免费版本）和商业版（收费），由于其体积小、速度快、总体拥有成本低，一般中小型网站的开发都选择 MySQL 作为网站数据库。



![MySQL logo](../Attachements-附件/mysql_logo.jpeg)

特点：

1.MySQL数据库是用C和C++语言编写的，以保证源码的可移植性

2.支持多个操作系统例如：Windows、Linux、Mac OS等等

3.支持多线程，可以充分的利用CPU资源

4.为多种编程语言提供API，包括C语言，Java，PHP、Python、go语言等



































5.MySQL优化了SQL算法，有效的提高了查询速度

6.MySQL开放源代码且无版权制约，自主性强、使用成本低。

7.MySQL历史悠久、社区及用户非常活跃，遇到问题，可以很快获取到帮助。

## MySQL的安装与卸载
### 安装
#### 解压安装
MySQL的安装两种方式（MySQL8.0）

1.解压版本 或者直接安装方式

[https://dev.mysql.com/downloads/mysql/](https://dev.mysql.com/downloads/mysql/) 

![MySQL download page](../Attachements-附件/mysql_download_page.png)















![MySQL version select](../Attachements-附件/mysql_version_select.png)










 1.解压mysql-8.0.27-winx64





![MySQL extract folder](../Attachements-附件/mysql_folder_structure.png)






注意：不要放在有中文名字和空格的的目录下





<font style="color:rgb(51, 51, 51);">2.在</font>mysql-8.0.27-winx64<font style="color:rgb(51, 51, 51);">文件夹下面新建一个my.ini文件和一个data文件夹</font>
































































![MySQL ini data folder](../Attachements-附件/mysql_ini_data_folder.png)















使用mysql 数据 ----data文件夹中





3.<font style="color:rgb(51, 51, 51);">my.ini 添加以下内容：</font>






```cpp
[mysqld]
# 设置3306端口
port=3306






















































# 设置mysql的安装目录
basedir=D:\\mysql-8.0.27-winx64
# 设置mysql数据库的数据的存放目录
datadir=D:\\mysql-8.0.27-winx64\\data
# 允许最大连接数
max_connections=200
# 允许连接失败的次数。这是为了防止有人从该主机试图攻击数据库系统
max_connect_errors=10
# 服务端使用的字符集默认为UTF8
character-set-server=utf8
# 创建新表时将使用的默认存储引擎
default-storage-engine=INNODB
[mysql]
# 设置mysql客户端默认字符集
default-character-set=utf8
[client]
# 设置mysql客户端连接服务端时默认使用的端口
port=3306
default-character-set=utf8
```

# 设置mysql的安装目录

basedir=D:\\mysql-8.0.27-winx64

# 设置mysql数据库的数据的存放目录

datadir=D:\\mysql-8.0.27-winx64\\data

改成自己  路径



4.<font style="color:rgb(51, 51, 51);">配置系统环境</font>

<font style="color:rgb(51, 51, 51);">我的电脑→属性→高级→环境变量→系统变量→新建</font>

<font style="color:rgb(51, 51, 51);">变量名：MYSQL_HOME</font>

<font style="color:rgb(51, 51, 51);">变量值：</font>D:\path\mysql\mysql-8.0.27-winx64<font style="color:rgb(51, 51, 51);">  （也就是刚解压的地方）</font>

<font style="color:rgb(51, 51, 51);">Path 中新建一段：%MYSQL_HOME%\bin</font>

![MySQL env path1](../Attachements-附件/mysql_env_path1.png)














![MySQL env path2](../Attachements-附件/mysql_env_path2.png)















<font style="color:rgb(51, 51, 51);">5.以管理员的身份打开cmd窗口跳转路径到</font>D:\path\mysql\mysql-8.0.27-winx64<font style="color:rgb(51, 51, 51);">，依次输入以下命令： 注意：是以管理员的身份打开cmd</font>



























































<font style="color:rgb(51, 51, 51);">1.初始化：</font>mysqld --initialize --user=mysql --console

![MySQL initialize result](../Attachements-附件/mysql_initialize_result.png)















 初始化 mysql 成功之后 账户和密码





账户：root

密码：随机密码 ,8s5%IoDpgu,






2.<font style="color:rgb(51, 51, 51);">添加服务  </font>mysqld -install


















































![MySQL install service](../Attachements-附件/mysql_install_service.png)






3.<font style="color:rgb(51, 51, 51);">启动服务  </font>net start mysql










![MySQL start service](../Attachements-附件/mysql_start_service.png)
















![MySQL start service result](../Attachements-附件/mysql_start_service_result.png)












4.<font style="color:rgb(51, 51, 51);">登进数据库 （密码就输入刚才的随机密码）</font> mysql -u root -p 










![MySQL login](../Attachements-附件/mysql_login.png)










<font style="color:rgb(51, 51, 51);">就是为临时密码 </font>;tTqBrpRs3#d










![MySQL change password](../Attachements-附件/mysql_change_password.png)















测试 

![MySQL test](../Attachements-附件/mysql_test.png)





root 账户的密码 错误 



<font style="color:rgb(51, 51, 51);">5.AL</font>TER USER root@localhost IDENTIFIED  BY 'root';   # 修改密码为：root



















































退出：exit 

![MySQL exit](../Attachements-附件/mysql_exit.png)



















#### 直接安装
2.直接安装方式

[https://downloads.mysql.com/archives/installer/](https://downloads.mysql.com/archives/installer/)





### ![MySQL installer](../Attachements-附件/mysql_installer.png)









**<font style="color:rgb(0, 0, 0);">一、双击运行安装包执行安装</font>**






<font style="color:rgb(0, 0, 0);">1、选择Custom，该种方式可以设置安装位置，仅安装所需的组件，点击Next</font>














































![MySQL installer custom](../Attachements-附件/mysql_installer_custom.png)














<font style="color:rgb(0, 0, 0);">2、选择需要的组件，点击Advanced Options</font>











<font style="color:rgb(0, 0, 0);"></font>![MySQL installer components](../Attachements-附件/mysql_installer_components.png)
















































<font style="color:rgb(0, 0, 0);">3、设置安装位置，点击OK</font>










![MySQL installer location](../Attachements-附件/mysql_installer_location.png)










<font style="color:rgb(0, 0, 0);">4、点击Next</font>










![MySQL installer next](../Attachements-附件/mysql_installer_next.png)









| <font style="color:rgb(51, 51, 51);">=</font> | <font style="color:rgb(51, 51, 51);">等于</font> | |
| <font style="color:rgb(51, 51, 51);"><>, !=</font> | <font style="color:rgb(51, 51, 51);">不等于</font> | |
| <font style="color:rgb(51, 51, 51);">></font> | <font style="color:rgb(51, 51, 51);">大于</font> | |
| <font style="color:rgb(51, 51, 51);"><</font> | <font style="color:rgb(51, 51, 51);">小于</font> | |
| <font style="color:rgb(51, 51, 51);"><=</font> | <font style="color:rgb(51, 51, 51);">小于等于</font> | |
| <font style="color:rgb(51, 51, 51);">>=</font> | <font style="color:rgb(51, 51, 51);">大于等于</font> | |
| <font style="color:rgb(51, 51, 51);">BETWEEN</font> | <font style="color:rgb(51, 51, 51);">在两值之间</font> | <font style="color:rgb(51, 51, 51);">>=min&&<=max</font> |
| <font style="color:rgb(51, 51, 51);">NOT BETWEEN</font> | <font style="color:rgb(51, 51, 51);">不在两值之间</font> | |
| <font style="color:rgb(51, 51, 51);">IN</font> | <font style="color:rgb(51, 51, 51);">在集合中</font> | |
| <font style="color:rgb(51, 51, 51);">NOT IN</font> | <font style="color:rgb(51, 51, 51);">不在集合中</font> | |
| <font style="color:rgb(51, 51, 51);"><=></font> | <font style="color:rgb(51, 51, 51);">严格比较两个NULL值是否相等</font> | <font style="color:rgb(51, 51, 51);">两个操作码均为NULL时，其所得值为1；而当一个操作码为NULL时，其所得值为0</font> |
| <font style="color:rgb(51, 51, 51);">LIKE</font> | <font style="color:rgb(51, 51, 51);">模糊匹配</font> | |
| <font style="color:rgb(51, 51, 51);">REGEXP 或 RLIKE</font> | <font style="color:rgb(51, 51, 51);">正则式匹配</font> | |
| <font style="color:rgb(51, 51, 51);">IS NULL</font> | <font style="color:rgb(51, 51, 51);">为空</font> | |
| <font style="color:rgb(51, 51, 51);">IS NOT NULL</font> | <font style="color:rgb(51, 51, 51);">不为空</font> | |


####   
 逻辑运算符
<font style="color:rgb(0, 0, 0);">逻辑运算符用来判断表达式的真假。如果表达式是真，结果返回1。如果表达式是假，结果返回0。逻辑运算符又称为布尔运算符。MySQL中支持4种逻辑运算符，分别是与、或、非和异或。</font>

| <font style="color:rgb(255, 255, 255);">运算符号</font> | <font style="color:rgb(255, 255, 255);">作用</font> |
| --- | --- |
| <font style="color:rgb(51, 51, 51);">NOT 或 !</font> | <font style="color:rgb(51, 51, 51);">逻辑非</font> |
| <font style="color:rgb(51, 51, 51);">AND</font> | <font style="color:rgb(51, 51, 51);">逻辑与</font> |
| <font style="color:rgb(51, 51, 51);">OR</font> | <font style="color:rgb(51, 51, 51);">逻辑或</font> |
| <font style="color:rgb(51, 51, 51);">XOR</font> | <font style="color:rgb(51, 51, 51);">逻辑异或</font> |


#### 位运算符
<font style="color:rgb(0, 0, 0);">参与运算符的操作数，按二进制位进行运算。包括位与（&）、位或（|）、位非（~）、位异或（^）、左移（<<）、右移(>>)6种。</font>

| <font style="color:rgb(255, 255, 255);">运算符号</font> | <font style="color:rgb(255, 255, 255);">作用</font> |
| --- | --- |
| <font style="color:rgb(51, 51, 51);">&</font> | <font style="color:rgb(51, 51, 51);">按位与</font> |
| <font style="color:rgb(51, 51, 51);">|</font> | <font style="color:rgb(51, 51, 51);">按位或</font> |
| <font style="color:rgb(51, 51, 51);">^</font> | <font style="color:rgb(51, 51, 51);">按位异或</font> |
| <font style="color:rgb(51, 51, 51);">!</font> | <font style="color:rgb(51, 51, 51);">取反</font> |
| <font style="color:rgb(51, 51, 51);"><<</font> | <font style="color:rgb(51, 51, 51);">左移</font> |
| <font style="color:rgb(51, 51, 51);">>></font> | <font style="color:rgb(51, 51, 51);">右移</font> |


比较和逻辑运算符使用

```plsql
--查询学生的名称是为余胜军
SELECT * from mayikt_student where name ='余胜军'
--查询学生的名称不是为余胜军
SELECT * from mayikt_student where name !='余胜军'
SELECT * from mayikt_student where name <>'余胜军'
SELECT * from mayikt_student where not (name ='余胜军')
--查询学生年龄是为17岁
SELECT * from mayikt_student where age=17
--查询学生年龄是大于17岁
SELECT * from mayikt_student where age>17
--查询学生年龄是小于17岁
SELECT * from mayikt_student where age<17
--查询学生年龄是18岁-40岁之间
SELECT * from mayikt_student where age>17 and age<41
SELECT * from mayikt_student where age>17 && age<41
SELECT * from mayikt_student where age  BETWEEN  18 and 40
-- 查询年龄是在17或者 28岁的学生
SELECT * from mayikt_student where age=17 or age=28;
SELECT * from mayikt_student where age=17 || age=28;
SELECT * from mayikt_student where  age in (17,28);

--查询名称含有“军”
SELECT * from mayikt_student where name like '%军%'
--查询名称开头“小”
SELECT * from mayikt_student where name like '小%'
--查询名称第二字“汉”
SELECT * from mayikt_student where name like '_汉%'
--查询地址是为null学生
SELECT * from mayikt_student where address is null;
--查询地址不是为null的学生
SELECT * from mayikt_student where address is not null;

```

```plsql
--查询学生的名称是为余胜军
--查询学生的名称不是为余胜军
--查询学生年龄是为17岁
--查询学生年龄是大于17岁
--查询学生年龄是小于17岁
--查询学生年龄是18岁-40岁之间
-- 查询年龄是在17或者 28岁的学生

--查询名称含有“军”
--查询名称开头“小”
--查询名称第二字“汉”
--查询地址是为null学生
--查询地址不是为null的学生
```

### 排序 
如果我们需要对读取的数据进行排序，可以利用 order by 根据字段来进行升序或者降序排列 再返回结果。

升序： 从小到大

降序：从大到小

 order by 根据字段 数字、字母、汉字

语法格式：

```plsql
以下是 SQL SELECT 语句使用 ORDER BY 子句将查询数据排序后再返回数据：
SELECT field1, field2,…fieldN table_name1, table_name2…
ORDER BY field1, [field2…] [ASC [DESC]]
```

1.asc代表 升序排列 desc代表降序排列 默认是为升序;

升序就是从小到大、降序就是从大到小

2.order by 可以支持单个字段，多个字段 ；

3.order by 放在查询语句最后面，limit 分页除外；

```plsql
1.根据学生年龄从小到大;
SELECT * from mayikt_student  order by age;
2.根据学生年龄从大到小;
SELECT * from mayikt_student  order by age desc;
3.判断学生的年龄大于18岁，在从小到大排序
SELECT * from mayikt_student where age>18  order by age ;
4.根据学生的年龄从大到小排序，以班级id 从小到大排序  当年龄相同 则根据 班级id从小到大排序
SELECT * from mayikt_student order by age desc ,class_id desc ;
5.根据班级id去重，根据班级id从大到小排序
SELECT DISTINCT class_id from mayikt_student ORDER BY class_id desc;
```



```plsql
1.根据学生年龄从小到大;
2.根据学生年龄从大到小;
3.判断学生的年龄大于18岁，在从小到大排序
4.根据学生的年龄从大到小排序，以班级id 从小到大排序  当年龄相同 则根据 班级id从小到大排序
5.根据班级id去重，根据班级id从大到小排序
```

一般的情况下我们使用数字排序，从小到大 或者从大到小；

如果是字母排序 则 根据字母的顺序 从A到Z排序 或者Z到A顺序

如果是汉字的拼音排序，用的比较多是在人名的排序中，按照姓氏的拼音字母，从A到Z排序

### 分页


概述：limit

在mysql中当数据量很大时，显示屏长度有限，我们可以对数据进行分页显示，例如数据总共

100条，每页10条数据，可以分成10页。

![1644369183938-85fc4862-5d47-4319-84d9-2f2335512f1b.png](./img/8X9e0d9ODVPNIerj/1644369183938-85fc4862-5d47-4319-84d9-2f2335512f1b-672056.png)

格式：

```plsql
方式1：显示前n条
select 字段1，字段2 ... from  mayikt_student limit n 
方式2：分页显示
select 字段1，字段2 ... from  mayikt_student limit m,n
```

m: 整数，表示从第几条索引开始 计算方式(当前页-1)*每页显示条数

n：整数，表示查询多少条数据

案例：

```plsql
SELECT * from mayikt_student limit 5;
SELECT * from mayikt_student limit 5,5;
```

需求

```plsql
1.查询用户表中前5条数据
SELECT * from mayikt_student limit 5;
2.从第6条开始显示 显示5条
SELECT * from mayikt_student limit 5,5;
```

### 聚合查询
我们在之前的查询是根据条件一行一行的判断，而使用聚合查询，它是对列的值进行计算，让后返回

一个单一的值。

| 聚合函数 | 作用 |
| --- | --- |
| count(age) | 统计指定列不为null的记录行数 |
| sum() | 计算指定列的数据和 |
| max() | 计算指定列的数据最大值 |
| min() | 计算指定列的数据最小值 |
| avg() | 计算指定列的数据平均值 |


```plsql
1.查询学生表的总人数
SELECT count(*) from mayikt_student 
2.查询学生年龄大于18的 总人数
SELECT count(*) from mayikt_student where age>18
3.查询classid=1 所有学生年龄总和
SELECT SUM(age) from mayikt_student where class_id='1'
4.查询学生最大年龄
SELECT max(age) from mayikt_student 
5.查询学生最小年龄
SELECT max(age),min(age) from mayikt_student
6.求学生年龄的平均值
SELECT avg(age) from mayikt_student 
```

```plsql
1.查询学生表的总人数
2.查询学生年龄大于18的 总人数
3.查询classid=1 所有学生年龄总和
4.查询学生最大年龄
5.查询学生最小年龄
6.求学生年龄的平均值
```

聚合查询 null的处理

1.count函数对null值的处理

如果count函数的参数(*),则统计所有记录的行数，如果参数是为某字段，不统计含null值记录行数

2.sum、avg、max、min 函数对null 也是做忽略

```plsql
1.查询学生年龄为null总人数
SELECT  count(*) from mayikt_student where age is null;

```

### 分组查询
分组查询是指使用 group by 字句对查询信息进行分组。

湖北省

山东省

广东省

格式：

select 字段1，字段2 ... from  表名称   group by  分组字段 having 分组条件

分组的条件使用 having 不是where

案例:

SELECT class_id ,count(*) from mayikt_student group by class_id;

```plsql
1.统计class_id 分类对应学生的个数
SELECT class_id ,count(*) from mayikt_student group by class_id; 
```

注意分组  返回列 只能为 分组的字段或者聚合函数；

分组之后的条件筛选

1.分组之后对统计结果进行分组条件筛选 必须使用having，不能够使用where

2.HAVING语句通常与GROUP BY语句联合使用，用来过滤由GROUP BY语句返回的记录集

语法格式：

```plsql
SELECT 字段1，字段2 ... 
from mayikt_student GROUP BY 分组字段  having 分组条件;
```

案例需求：

```plsql
统计每个班级学生人数大于1人以上的班级

SELECT class_id as 班级, count(*) as 人数 from mayikt_student GROUP BY class_id HAVING 
count(*) >1
```

先from 、  GROUP BY 、count 、HAVING

注意事项 ：

1、where 后不能跟聚合函数，因为where执行顺序大于聚合函数。

2、where 子句的作用是在对查询结果进行分组前，将不符合where条件的行去掉，即在分组之前过滤数据，条件中不能包含聚组函数，使用where条件显示特定的行。

3、having 子句的作用是筛选满足条件的组，即在分组之后过滤数据，条件中经常包含聚组函数，使用having 条件显示特定的组，也可以使用多个分组标准进行分组。



### 作业题
#### 练习题1
```plsql
CREATE TABLE `meite_student` (
  `id` int NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `sex` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `chinese` int DEFAULT NULL,
  `english` int DEFAULT NULL,
  `maths` int DEFAULT NULL
) ;

测试数据
INSERT INTO `mayikt`.`meite_student`
(`id`, `name`, `sex`, `chinese`, `english`, `maths`) 
VALUES ('1', '余胜军', '男', '98', '99', '100');
INSERT INTO `mayikt`.`meite_student`
(`id`, `name`, `sex`, `chinese`, `english`, `maths`) 
VALUES ('2', '刘文', '女', '99', '87', '88');
INSERT INTO `mayikt`.`meite_student`
(`id`, `name`, `sex`, `chinese`, `english`, `maths`) 
VALUES ('3', '王麻子', '女', '22', '55', '33');
INSERT INTO `mayikt`.`meite_student`
(`id`, `name`, `sex`, `chinese`, `english`, `maths`)
VALUES ('4', '黄红军', '男', '55', '33', '22');
INSERT INTO `mayikt`.`meite_student` 
(`id`, `name`, `sex`, `chinese`, `english`, `maths`) 
VALUES ('5', '张玲', '女', '95', '95', '88');
```

```plsql
1.查询表中所有学生信息
2.查询表中所有学生信息 只返回学生名称和数学成绩 字段
3.过滤表中重复数据
4.统计每个学生的总分
5.所有学生总分 加5分
6.查询语文成绩大于80分以上同学
7.查询总分大于180分以上同学
8.查询英语成绩80-90 之间的同学
9.查询英语成绩不在80-90 之间的同学
10.查询英语成绩22,99,33学生 信息
11.查询所有姓余的学生信息 like
12.查询语文、数学、外语 大于等于72分的学生信息
13.查询英语大于等于72分或者总分大于等于180分以上学生信息 or
14.根据英语成绩字段排序 升序和降序
15.根据总分成绩 从高到低排列
16.统计学生表中 性别男和女 分别有多少学生
17.根据英语成绩字段排序 (从高到低排序 取前3名)
SELECT name, chinese+english+maths  as  总分 
FROM meite_student ORDER BY  chinese+english+maths desc 
limit 3;
```

```plsql
1.查询表中所有学生信息
select * from meite_student;
2.查询表中学生名称和数学成绩
select `name`,maths from meite_student;
3.过滤表中重复数据
select DISTINCT *  from meite_student;
4.统计每个学生的总分
select name 姓名, chinese+english+maths 总分  from meite_student 
5.所有学生总分 加5分
select name 姓名, (chinese+english+maths)+5 总分  from meite_student 
6.查询语文成绩大于80分以上同学
select *  from meite_student  where chinese>80
7.查询总分大于180分以上同学
select name 姓名, (chinese+english+maths) as 总分  from meite_student 
where chinese+english+maths>180
8.查询英语成绩80-90 之间的同学
select  *  from meite_student 
where english >80 and english<90
select  *  from meite_student 
where english  BETWEEN 80 and 90  
9.查询英语成绩不在80-90 之间的同学
select  *  from meite_student 
where english  not BETWEEN 80 and 90  
10.查询英语成绩22,99,33学生 信息
select  *  from meite_student 
where english in(22,99,33)
11.查询所有姓余的学生信息
SELECT * from meite_student where name like  '%余%'
12.查询语文、数学、外语 大于等于72分的学生信息
SELECT * from meite_student where chinese>=72 
and english>=72 and maths>=72
13.查询语文和数学大于等于72分或者总分大于180分以上学生信息
SELECT * from meite_student where
 (english>=72 and maths>=72) or   (chinese+english+maths)>180
14.根据英语成绩字段排序 升序和降序
SELECT * from meite_student ORDER BY english desc  
15.根据总分成绩 从高到低排列
SELECT name as  姓名,(chinese+english+maths) as 总分
 from meite_student order by  (chinese+english+maths) asc  
16.统计学生表中 姓名男和女 分表有多少学生
SELECT sex ,count(*) from meite_student GROUP BY sex 
```

#### 练习题2


需求：

```plsql
-- 1. 根据员工的薪资升序或者降序排列
-- 2.根据员工的薪资升序排列，且不查询到财务部门的员工信息
-- 3.查询姓名第二字非“麻”的且薪资>=15000的员工信息，根据薪资升序排列
-- 4.查询每位员工综合年薪 根据年薪降序排列
-- 5.求每个不同部门的平均薪水
-- 6.求每个不同部门的平均薪水且平均薪资大于13000的部门
-- 7.求每个部门中最高薪水
-- 8.求每个部门有多少员工
-- 9.查询员工最高薪资和最低薪资员工信息
-- 10.查询员工最高薪资和最低薪资的差距
```

初始化sql：

```plsql
CREATE TABLE `employee` (
  `number` int DEFAULT NULL COMMENT '员工编号',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '姓名',
  `hiredate` date DEFAULT NULL COMMENT '入职时间',
  `salary` double DEFAULT NULL COMMENT '薪资',
  `bonus` double DEFAULT NULL COMMENT '奖金',
  `department` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '部门编号'
)
INSERT INTO `mayikt`.`employee` (`number`, `name`, `hiredate`, `salary`, `bonus`, `department`) VALUES ('1001', '余胜军', '2020-06-01', '18000', '3000', '研发部门');
INSERT INTO `mayikt`.`employee` (`number`, `name`, `hiredate`, `salary`, `bonus`, `department`) VALUES ('1002', '刘军', '2019-06-01', '16000', '1000', '研发部门');
INSERT INTO `mayikt`.`employee` (`number`, `name`, `hiredate`, `salary`, `bonus`, `department`) VALUES ('1003', '张三', '2018-02-10', '6000', '500', '财务部门');
INSERT INTO `mayikt`.`employee` (`number`, `name`, `hiredate`, `salary`, `bonus`, `department`) VALUES ('1004', '王麻子', '2022-02-10', '27000', '500', '财务部门');
INSERT INTO `mayikt`.`employee` (`number`, `name`, `hiredate`, `salary`, `bonus`, `department`) VALUES ('1005', '刘软', '2013-02-10', '6000', '500', 'UI部门');
INSERT INTO `mayikt`.`employee` (`number`, `name`, `hiredate`, `salary`, `bonus`, `department`) VALUES ('1006', '王麻子', '2022-02-10', '6000', '500', 'UI部门');
INSERT INTO `mayikt`.`employee` (`number`, `name`, `hiredate`, `salary`, `bonus`, `department`) VALUES ('1007', '李四', '2022-02-25', '5000', '100', '财务部门');
INSERT INTO `mayikt`.`employee` (`number`, `name`, `hiredate`, `salary`, `bonus`, `department`) VALUES ('1008', '余国军', '2022-02-25', '10000', '50', '研发部门');
```

```plsql
-- 1. 根据员工的薪资升序或者降序排列
select * from employee order by salary desc ;
select * from employee order by salary asc ;
-- 2.根据员工的薪资升序排列，且不查询到财务部门的员工信息
select * from employee where department!='财务部门' order by salary asc 
-- 3.查询姓名第二字非“麻”的且薪资>=15000的员工信息，根据薪资升序排列
select * from employee where name not  like '_麻%' and salary>=15000
order by salary asc ;
-- 4.查询每位员工综合年薪 根据年薪降序排列
select name as 姓名 ,(salary*12)+bonus as 年薪 from employee
order by (salary*12)+bonus desc 
-- 5.求每个不同部门的平均薪水
select department,avg(salary)  from employee GROUP BY  department
-- 6.求每个不同部门的平均薪水且平均薪资大于13000的部门
select department,avg(salary)  from employee GROUP BY  department 
HAVING avg(salary)>13000
-- 7.求每个部门中最高薪水
select max(salary)   from employee GROUP BY  department
-- 8.求每个部门有多少员工
select  department, count(*)  from employee GROUP BY  department
-- 9.查询员工最高薪资和最低薪资员工信息
select  *  from employee order by  salary desc  limit 1 
select  *  from employee order by  salary asc  limit 1 
-- 10.查询员工最高薪资和最低薪资的差距
select  max(salary)-min(salary)  from employee 
```

## 多表关系


实际的项目开发中，一个项目通常需要很多张表才能够完成，例如在学生系统中 有学生班级表(stu_class)、学生信息表(stu_info)等多张表。这些表存在一定的关系。

会员信息--会员信息表

课程信息---课程详细内容

例如

1.一对一 使用的比较少

2.一对多 

3.多对多

### 一对一
1.一个学生只有一张身份证，一个身份证只能够对应一个学生

2.一般一对一关系使用的比较少



### 一对多
例如 学生班级表(stu_class)、学生信息表(stu_info)等多张表

1.一个学生只要一个班级，学生信息表与班级表一一对应；

2.在表中添加一个外键，指向另一方主键，确保一对一关系;

![1644805270870-744d7e45-691e-4038-bd66-8c9da7cfc5c4.png](./img/8X9e0d9ODVPNIerj/1644805270870-744d7e45-691e-4038-bd66-8c9da7cfc5c4-910033.png)

部门与员工

例如：一个部门有多个员工，一个员工只能够对应一个部门

![1644806016404-2a2add35-4104-4e1c-8ff0-5819cca00039.png](./img/8X9e0d9ODVPNIerj/1644806016404-2a2add35-4104-4e1c-8ff0-5819cca00039-434209.png)

### 多对多


学生和课程

1.一个学生可以选择多门课程学习  ，一个课程也可以被很多学生选择

原则：多对多关系实现需要借助第三章中间表实现，中间表至少需要包含两个字段，

将多对多的关系拆分一对多的关系。

![1644812335482-7743e446-1dca-434d-a4a0-6b838720086a.png](./img/8X9e0d9ODVPNIerj/1644812335482-7743e446-1dca-434d-a4a0-6b838720086a-875934.png)

### 外键约束
MySQL 外键约束（FOREIGN KEY）是表的一个特殊字段，经常与主键约束一起使用。对于两个具有关联关系的表而言，相关联字段中主键所在的表就是主表（父表），外键所在的表就是从表（子表）。

外键用来建立主表与从表的关联关系，为两个表的数据建立连接，约束两个表中数据的一致性和完整性

![1644819561772-a89ed50f-86e8-4a16-90b7-95e3a3568d04.png](./img/8X9e0d9ODVPNIerj/1644819561772-a89ed50f-86e8-4a16-90b7-95e3a3568d04-578763.png)

<font style="color:#F5222D;">注意事项：主表删除某条记录时，从表中与之对应的记录也必须有相应的改变。一个表可以有一个或多个外键，外键可以为空值，若不为空值，则每一个外键的值必须等于主表中主键的某个值。</font>



定义外键时，需要遵守下列规则：

1.主表必须已经存在于数据库中，或者是当前正在创建的表。如果是后一种情况，则主表与从表是同一个表，这样的表称为自参照表，这种结构称为自参照完整性。

2.必须在主表定义主键。

3.主键不能包含空值，但允许在外键中出现空值。也就是说，只要外键的每个非空值出现在指定的主键中；

4.外键中列的数据类型必须和主表主键中对应列的数据类型相同。



创建主外键

在 CREATE TABLE 语句中，通过 FOREIGN KEY 关键字来指定外键，具体的语法格式如下：

```plsql
[CONSTRAINT <外键名>] FOREIGN KEY 字段名 [，字段名2，…]
REFERENCES <主表名> 主键列1 [，主键列2，…]
```

例如：

```plsql
CREATE TABLE `mayikt_class` (
  `id` int NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);
CREATE TABLE `mayikt_student` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `age` tinyint DEFAULT '0',
  `address` varchar(255) DEFAULT NULL,
  `class_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT mayikt_class_id  FOREIGN KEY (class_id)  REFERENCES  mayikt_class(id) -- 外键约束
); 
[CONSTRAINT <外键名>] FOREIGN KEY 字段名 [，字段名2，…]
REFERENCES <主表名> 主键列1 [，主键列2，…]

[CONSTRAINT <mayikt_class_id>] FOREIGN KEY 字段名 [，字段名2，…]
REFERENCES <主表名> 主键列1 [，主键列2，…]
CONSTRAINT mayikt_class_id  FOREIGN KEY (class_id)  REFERENCES  mayikt_class(id)
```

删除外键约束

```plsql
ALTER TABLE <表名> DROP FOREIGN KEY <外键约束名>;
ALTER TABLE mayikt_student DROP FOREIGN KEY mayikt_class_id;
```

验证：外键约束

1.先向主表新增数据，在向从表新增数据

2.外键列的值必须要在主表存在 或者是为空

3.主表的数据不能够随便删除，从表数据可以随便删除 或者先

删除从表所有数据没有任何关联主表的字段 在可以删除主表的对应的数据



### 联表查询
多表查询是指多张表联合一起查询，例如学生信息与学生班级表、部门与员工表。

1.交叉连接查询  (<font style="color:rgb(0, 0, 0);">笛卡尔积</font>)

2.内连接查询

3.外连接查询

4.子查询

5.表自关联



数据的准备：

```sql
CREATE TABLE `mayikt_class` (
  `id` int NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
);
CREATE TABLE `mayikt_student` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `age` tinyint DEFAULT '0',
  `address` varchar(255) DEFAULT NULL,
  `class_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  CONSTRAINT mayikt_class_id  FOREIGN KEY (class_id)  REFERENCES  mayikt_class(id) -- 外键约束
); 


INSERT INTO `mayikt`.`mayikt_class` (`id`, `name`) VALUES ('1', '第一期');
INSERT INTO `mayikt`.`mayikt_class` (`id`, `name`) VALUES ('2', '第二期');

INSERT INTO `mayikt`.`mayikt_student` (`id`, `name`, `age`, `address`, `class_id`) VALUES ('1', 'mayikt', '18', '武汉市', '1');
INSERT INTO `mayikt`.`mayikt_student` (`id`, `name`, `age`, `address`, `class_id`) VALUES ('2', 'meite', '23', '上海市', '2');
INSERT INTO `mayikt`.`mayikt_student` (`id`, `name`, `age`, `address`, `class_id`) VALUES ('3', '李思', '12', '孝感市', '1');
INSERT INTO `mayikt`.`mayikt_student` (`id`, `name`, `age`, `address`, `class_id`) VALUES ('4', '刘流', '27', '武汉市', '1');

```

### 
### 交叉连接查询  (<font style="color:rgb(0, 0, 0);">笛卡尔积</font>)


```sql
语法：select  * from mayikt_class,mayikt_student
```

得到的查询结果是两张表的笛卡尔积，也就是用A表中的每条数据都去匹配B表中的所有数据，获得的结果往往不是我们需要的，一般很少使用交叉连接,缺点数据比较冗余。

### 
### 内连接
显示内连接:

SELECT * FROM A INNER JOIN B ON 条件;

隐示内连接:

SELECT * FROM A,B WHERE 条件;

案例

```plsql
-- 1.查询每个班级下所有学生信息
-- 显示内连接
select * from mayikt_class  INNER JOIN  mayikt_student 
on mayikt_class.id =mayikt_student.class_id;
-- 隐士内连接
SELECT * from mayikt_class ,mayikt_student where mayikt_student.class_id=
mayikt_class.id


-- 2.需求查询第一期所有学生

SELECT * from mayikt_class ,mayikt_student where mayikt_student.class_id=
mayikt_class.id  and mayikt_class.id='1'

select * from mayikt_class  INNER JOIN  mayikt_student 
on mayikt_class.id =mayikt_student.class_id and mayikt_class.id='1'

-- 3.查询第一期和第二期所有的学生
select * from mayikt_class  INNER JOIN  mayikt_student 
on mayikt_class.id =mayikt_student.class_id and (mayikt_class.id='1'
or  mayikt_class.id='2')
select * from mayikt_class  INNER JOIN  mayikt_student 
on mayikt_class.id =mayikt_student.class_id and mayikt_student.class_id  in 
(1,2)

-- 4.查询每个班级下的学生总数 并且学生总数升序排列
select mayikt_class.`name` , count(*) from mayikt_class  INNER JOIN  mayikt_student 
on mayikt_class.id =mayikt_student.class_id  GROUP BY  mayikt_class.id 
order by count(*) asc  
-- 5.查询班级总人数>2的班级，并且人数降序排列
select mayikt_class.`name` , count(*) from mayikt_class  INNER JOIN  mayikt_student 
on mayikt_class.id =mayikt_student.class_id  GROUP BY  mayikt_class.id  HAVING
count(*)>2
order by count(*) asc  

```

2.需求查询第一期所有学生

3.查询第一期和第二期所有的学生

4.查询每个班级下的学生总数 并且学生总数升序排列

5.查询班级总人数>2的班级，并且人数降序排列

### 外连接
外连接:左外连接、右外连接、全外连接（union）。

1.左外链接（left outer join，outer可以省略）

```plsql
语法：SELECT * FROM A LEFT OUTER JOIN B ON 条件;
```

<font style="color:rgb(77, 77, 77);">左外连接获得的查询结果是左边的表A的全部信息和A，B两张表的交集，左边A表的全部包含A表中在B表中没有对应关系的信息</font>

2.右外连接（right outer join，outer可以省略）

```plsql
语法：SELECT * FROM A RIGHT OUTER JOIN B ON 条件;
```

总结：右外连接获得的查询结果是右边的表B的全部信息和A，B两张表的交集，右边B表的全部包含B表中在A表中没有对应关系的信息



3.全外连接

```plsql
select * from mayikt_class left join mayikt_student
on mayikt_class.id=mayikt_student.class_id
union
select * from mayikt_class right join mayikt_student
on mayikt_class.id=mayikt_student.class_id;
```

左连接 以左边为准  左变有该数据 就会返回 右变没有匹配上则直接返回为null

右连接 以右边为准  右变有该数据 就会返回 左变没有匹配上则直接返回为null

内连接左边与右边都是必须匹配才会返回

需求:

```plsql
1.查询哪些班级是有学生 哪些班级是没有学生
select * from mayikt_class left join mayikt_student
on mayikt_class.id=mayikt_student.class_id;
2.查询哪些学生是有班级，哪些学生是没有班级
select * from mayikt_class right join mayikt_student
on mayikt_class.id=mayikt_student.class_id;
3.使用union关键字实现左连接和右连接的并集 让去重复数据

```

MySQL UNION 操作符用于连接两个以上的 SELECT 语句的结果组合到一个结果集合中。多个 SELECT 语句会删除重复的数据。



### 子查询


#### 基本子查询
子查询指一个查询语句嵌套在另一个查询语句内部的查询，这个特性从 MySQL 4.1 开始引入，在 SELECT 子句中先计算子查询，子查询结果作为外层另一个查询的过滤条件，查询可以基于一个表或者多个表。

<font style="color:#E8323C;">通俗易懂也就是</font>SELECT <font style="color:#E8323C;">嵌套的查询</font>

子查询中常用的操作符有 ANY（SOME）、ALL、IN 和 EXISTS。

需求：

1.查询年龄最大的学生信息，显示信息包含 学生的id、学生的名称、学生的年龄

select * from mayikt_student order by age desc limit 1

```sql

select * from mayikt_student 
where age in (select max(age) from mayikt_student)

```

2.查询第一期和第三期学生信息

```sql
select * from mayikt_student join mayikt_class
on mayikt_student.class_id=mayikt_class.id where class_id='1' 
or class_id='3'
```

```sql
select * from mayikt_student where class_id in (select id from mayikt_class where id='1' or id='3'
)
```

3.查询第一期学生年龄大于18岁 学生信息

```sql
select * from mayikt_student  join mayikt_class
on mayikt_student.class_id=mayikt_class.id
and mayikt_student.class_id='1'
and mayikt_student.age>18;
```

```sql
select * from mayikt_student where age >18 and class_id in (
select id from mayikt_class where id='1')
```

```sql
select * from (select * from mayikt_class where id='1') a1 join 
(select * from mayikt_student where age >18) a2
on a1.id=a2.class_id;
```

#### 子查询关键字之all
all关键字用在比较操作操符的后面，表示查询结果的多个数据中的所有都满足该比较操作符才算满足

比较操作符：= 、>、!=、>=、<=等

```sql
select ... from mayikt_user（表的名称） where age（字段）  > all(查询语句)
相当于：
select ... from mayikt_user（表的名称） where age> result1 and age >result2
```



需求1：查询年龄大于第一期所有年龄的学生信息

```sql
select * from mayikt_student where age> all(
select age from mayikt_student where class_id='1'
)
```

需求2：查询没有班级的学生信息

```sql
select * from mayikt_student
where class_id!= all(
select id from mayikt_class 
)
```

#### 子查询关键字之any
```sql
select ... from mayikt_user（表的名称） where age（字段）  > any(查询语句)
相当于：
select ... from mayikt_user（表的名称） where age> result1 or age >result2
```

需求：查询学生年龄大于第一期任意一个学生年龄的信息

```sql
select * from mayikt_student where age> any(
select age from mayikt_student where class_id='1'

)
and class_id!=1;
```

all 底层 多个 and 比较 

any 底层 多个 or 或者比较

#### 子查询关键字之not in和in
语法格式：

```sql
select * from mayikt_student where 字段  in（查询语句）
select * from mayikt_student where 字段 =result1  or   字段 =result2
```

特点：

in关键字 用于判断某个记录的值 是否在指定的集合中

使用 not in 可以实现取反



需求：

查询第一期和第三期所有学生信息  

```sql
select * from mayikt_student where class_id
 in (select id from mayikt_class where name='第一期' 
 or name='第二期' 
)
```

in  子查询语句中  等于 or 连接比较

SELECT * from mayikt_student

where class_id=1 or class_id =3

all  子查询语句中  and   比较符

any  子查询语句中  or 比较符



#### 子查询关键字之exists 
EXISTS用于检查子查询是否至少会返回一行数据，该子查询实际上并不返回任何数据，而是返回值True或False

EXISTS比in关键字运算效率高，实际开发中 如果是数据量大的情况下 推荐使用EXISTS关键字。

语法：

```plain
select * from mayikt_student where EXISTS
(查询语句---查询到结果 就返回true  没有查询到结果 就返回 false)
```

需求1:

1.查询学生年龄大于18岁 学生信息

```sql
select * from mayikt_student s1
where EXISTS(select * from mayikt_student s2
             where  s1.age >18
 )
```

2.查询班级下  有学生的班级 

```sql
select * from mayikt_class a
 where  EXISTS(
select * from mayikt_student  b where b.class_id=a.id
)

```

3.查询有班级的学生信息

```sql
select * from mayikt_student
s1 where EXISTS(
select * from mayikt_class b where s1.class_id =
b.id
)

```

#### 子查询之自关联查询


对mysql数据自身进行关联查询，即一张表自己和自己关联，一张表当成多张表来用。

注意 关联查询时必须给表取别名

```sql
select 字段列表 from 表名称 a ,表 名称b where 条件;
或者
select 字段列表 from 表名称 a left join 表名称 b on 条件
```

以京东电商为例子：

[https://www.jd.com/?cu=true&utm_source=baidu-pinzhuan&utm_medium=cpc&utm_campaign=t_288551095_baidupinzhuan&utm_term=0f3d30c8dba7459bb52f2eb5eba8ac7d_0_b4f8aa41941a4b6093d2e5819ede21aa](https://www.jd.com/?cu=true&utm_source=baidu-pinzhuan&utm_medium=cpc&utm_campaign=t_288551095_baidupinzhuan&utm_term=0f3d30c8dba7459bb52f2eb5eba8ac7d_0_b4f8aa41941a4b6093d2e5819ede21aa) 

![1646184714084-f75f8bb2-f14c-4b36-9bd8-f873d60a5458.png](./img/8X9e0d9ODVPNIerj/1646184714084-f75f8bb2-f14c-4b36-9bd8-f873d60a5458-999609.png)





表结构

```plsql
CREATE TABLE `commodity_type` (
  `id` int NOT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '商品类型名称',
  `parent_id` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;


```

```plsql
select * from   commodity_type as a ,commodity_type
as b where a.id=b.parent_id;

```



#### 多表综合练习题1 
1.创建表结构

```plsql
CREATE TABLE `mayikt_dept` (
  `dept_no` int NOT NULL COMMENT '部门id',
  `dept_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '部门名称',
  `dept_address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '部门地址',
  PRIMARY KEY (`dept_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
CREATE TABLE `mayikt_emp` (
  `emp_number` int DEFAULT NULL COMMENT '员工编号',
  `emp_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '员工编号',
  `emp_post` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '员工职务',
  `emp_leader_number` int DEFAULT NULL COMMENT '员工领导编号',
  `emp_hiredate` datetime DEFAULT NULL COMMENT '员工入职时间',
  `emp_salary` double(10,0) DEFAULT NULL COMMENT '员工薪水',
  `emp_bonus` int DEFAULT NULL COMMENT '员工奖金',
  `emp_deptno` int DEFAULT NULL COMMENT '员工对外部门表外键'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

INSERT INTO `mayikt`.`mayikt_dept` (`dept_no`, `dept_name`, `dept_address`) VALUES ('1', '技术部门', '湖北武汉市');
INSERT INTO `mayikt`.`mayikt_dept` (`dept_no`, `dept_name`, `dept_address`) VALUES ('2', '财务部门', '中国上海市');
INSERT INTO `mayikt`.`mayikt_dept` (`dept_no`, `dept_name`, `dept_address`) VALUES ('3', '设计部门', '湖北孝感市');
INSERT INTO `mayikt`.`mayikt_emp` (`emp_number`, `emp_name`, `emp_post`, `emp_leader_number`, `emp_hiredate`, `emp_salary`, `emp_bonus`, `emp_deptno`) VALUES ('1001', '余胜军', 'CEO', NULL, '2021-11-01 11:32:46', '20000', '10000', '1');
INSERT INTO `mayikt`.`mayikt_emp` (`emp_number`, `emp_name`, `emp_post`, `emp_leader_number`, `emp_hiredate`, `emp_salary`, `emp_bonus`, `emp_deptno`) VALUES ('1002', '小薇', 'CFO', '1001', '2021-10-01 11:32:46', '5000', '10000', '2');
INSERT INTO `mayikt`.`mayikt_emp` (`emp_number`, `emp_name`, `emp_post`, `emp_leader_number`, `emp_hiredate`, `emp_salary`, `emp_bonus`, `emp_deptno`) VALUES ('1004', '张三', 'CTO', NULL, '2021-11-01 11:32:46', '80000', '10000', '1');
INSERT INTO `mayikt`.`mayikt_emp` (`emp_number`, `emp_name`, `emp_post`, `emp_leader_number`, `emp_hiredate`, `emp_salary`, `emp_bonus`, `emp_deptno`) VALUES ('1005', '李四', '技术总监', '1004', '2021-11-01 11:32:46', '20000', '10000', '1');
INSERT INTO `mayikt`.`mayikt_emp` (`emp_number`, `emp_name`, `emp_post`, `emp_leader_number`, `emp_hiredate`, `emp_salary`, `emp_bonus`, `emp_deptno`) VALUES ('1006', '王麻子', '客服', NULL, '2022-03-02 11:49:45', '3500', NULL, NULL);

```



```plsql
1.返回员工拥有部门的 员工信息含员工部门
select * from mayikt_emp as a join mayikt_dept as b
on  a.emp_deptno=b.dept_no;
2.查询员工薪资大于小薇的 员工信息
select * from mayikt_emp where emp_salary>(
select emp_salary from mayikt_emp where emp_number='1002'
)
3.返回员工所属领导信息
select * from mayikt_emp as a, mayikt_emp as b
where a.emp_number=b.emp_leader_number

4.返回入职时间早于领导 入职时间
select * from mayikt_emp as a, mayikt_emp as b
where a.emp_number=b.emp_leader_number
and a.emp_hiredate>b.emp_hiredate
5.返回从事财务工作的员工信息
select * from mayikt_emp as a join mayikt_dept as b
on  a.emp_deptno=b.dept_no
and b.dept_name='财务部门'
6.求每个部门 最低员工薪资
select  emp_deptno,min(emp_salary)
 from mayikt_emp GROUP BY emp_deptno 
7.返回员工薪资大于 平均薪资员工
select  *
 from mayikt_emp where emp_salary >(
select  avg(emp_salary)
 from mayikt_emp
)


```

#### 多表综合练习题2
单独创建员工等级表

```plsql
CREATE TABLE `mayikt_salgrade` (
  `grade` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '员工等级',
  `losal` double(255,0) DEFAULT NULL COMMENT '最低工资',
  `hisal` double DEFAULT NULL COMMENT '最高工资'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
```



```plsql
1.查询工资大于财务部所有员工的 信息
select *
 from  mayikt_emp where emp_salary > all(
select emp_salary
 from  mayikt_emp where emp_salary and  emp_deptno='2'
)

2.求从事相同职务工作最低工资和最高工资
select emp_post,min(emp_salary) as 最低工资,max(emp_salary)  as 最高工资
 from  mayikt_emp  GROUP BY emp_post
3.计算每位员工的年薪，并且以年薪降序排列
select ((emp_salary *12)+emp_bonus) as 年薪
 from  mayikt_emp  ORDER BY 年薪 desc 
 4.返回工资处于P4级别员工的信息
 select *
 from  mayikt_emp where emp_salary BETWEEN (
select losal
 from  mayikt_salgrade where grade='P4'
) and (
select hisal
 from  mayikt_salgrade where grade='P4'
)
 5.返回工资处于P1员工信息含部门名称
 select *
 from  mayikt_emp as e join 
mayikt_dept as d on e.emp_deptno=d.dept_no
join mayikt_salgrade s 
on s.grade='p1'  and e.emp_salary>=s.losal and e.emp_salary
<=s.hisal

```

1.查询工资大于财务部所有员工的 信息

2.求从事相同职务工作最低工资和最高工资

3.计算每位员工的年薪，并且以年薪降序排列

4.返回工资处于P4级别员工的信息

5.返回工资处于P1员工信息含部门名称



# JDBC快速入门


## jdbc的概念


Java数据库连接，（Java Database Connectivity，简称JDBC）是Java语言中用来规范客户端程序如何来访问数据库的应用程序接口，提供了诸如查询和更新数据库中数据的方法。通俗易懂说：jdbc就是java操作数据库

![1646619965613-4eedaa60-509e-4d83-84a0-cfae7b656476.png](./img/8X9e0d9ODVPNIerj/1646619965613-4eedaa60-509e-4d83-84a0-cfae7b656476-979571.png)



jdbc的本质就是：java官方提供的一套规范接口，用于帮助程序员开发者操作不同的关系型数据库(mysql/Oracle/SQLServer)

## jdbc快速入门


1.创建一张表

```sql
CREATE TABLE `mayikt_users` (
  `id` int NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `pwd` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

INSERT INTO `mayikt`.`mayikt_users` (`id`, `name`, `pwd`) VALUES ('1', 'mayikt', '123');
INSERT INTO `mayikt`.`mayikt_users` (`id`, `name`, `pwd`) VALUES ('2', 'meite', '666');

```

2.在java官方只是提供JDBC规范的接口，如果需要连接到具体的数据库 例如

mysql ，我们就需要导入mysql的依赖jar包，具体实现是有不同的数据库

产商实现的。

1.导入mysql驱动jar包;

2.注册驱动 javase 反射机制<font style="color:#000000;">Class</font>.forName()

3.获取数据库连接

4.获取执行者对象

5.执行sql语句并获取返回结果

6.对结果进行处理

7.释放jdbc资源



jdbc驱动依赖下载：[http://note.youdao.com/noteshare?id=61e2cc939390acc9c7e5017907e98044&sub=DAABBA2F350445D2AC6879CCC3F46EA7](http://note.youdao.com/noteshare?id=61e2cc939390acc9c7e5017907e98044&sub=DAABBA2F350445D2AC6879CCC3F46EA7)

## ![1646628157230-08f067d5-5361-4ca5-9422-feb5c9b88a8c.png](./img/8X9e0d9ODVPNIerj/1646628157230-08f067d5-5361-4ca5-9422-feb5c9b88a8c-557319.png)
编写代码

```java
/**
* <AUTHOR>
* @ClassName JdbcDemo01
* @qq 644064779
* @addres www.mayikt.com
* 微信:yushengjun644
*/
public class JdbcDemo01 {
    public static void main(String[] args) throws ClassNotFoundException, SQLException {
        // 1.导入jar包
        // 2.注册驱动
        Class.forName("com.mysql.jdbc.Driver");
        // 3.获取执行者
        Connection connection = DriverManager.getConnection("*****************************************************",
                                                            "root", "root");
        // 4.获取执行者对象
        Statement statement = connection.createStatement();
        // 5.执行sql语句获取返回结果
        ResultSet resultSet = statement.executeQuery("select * from mayikt_users");
        // 6.对返回结果进行处理
        while (resultSet.next()) {
            System.out.println(resultSet.getInt("id") + "," + resultSet.getString("name") + "," +
                               resultSet.getString("pwd"));
        }
        // 7.释放资源
        connection.close();
        statement.close();
    }
}

```

启动该程序报错

```java
Caused by: com.mysql.cj.exceptions.InvalidConnectionAttributeException: The server time zone value 'ÖÐ¹ú±ê×¼Ê±¼ä' is unrecognized or represents more than one time zone. You must configure either the server or JDBC driver (via the serverTimezone configuration property) to use a more specifc time zone value if you want to utilize time zone support.
```

需要在jdbc连接后面加上该参数*****************************************************

在Java中使用JDBC操作数据库，该数据库版本为8.0.15属于高版本(如果是低版本的话，通常是不会出现这些问题的)

```java

import java.sql.*;

/**
 * <AUTHOR>
 * @ClassName JdbcDemo01
 * @qq 644064779
 * @addres www.mayikt.com
 * 微信:yushengjun644
 */
public class JdbcDemo01 {
    public static void main(String[] args) throws ClassNotFoundException, SQLException {
        // 1.导入jar包
        // 2.注册驱动
        Class.forName("com.mysql.jdbc.Driver");
        // 3.获取执行者
        Connection connection = DriverManager.getConnection("*****************************************************",
                "root", "root");
        // 4.获取执行者对象
        Statement statement = connection.createStatement();
        // 5.执行sql语句获取返回结果
        ResultSet resultSet = statement.executeQuery("select * from mayikt_users");
        // 6.对返回结果进行处理
        while (resultSet.next()) {
            System.out.println(resultSet.getInt("id") + "," + resultSet.getString("name") + "," +
                    resultSet.getString("pwd"));
        }
        // 7.释放资源
        connection.close();
        statement.close();
    }
}
```

### 常见问题


1.Exception in thread "main" java.lang.ClassNotFoundException: com.mysql.jdbc.Driver

原因是没有引入mysql 驱动jar包



2. No suitable driver found for jdbc:mysql//127.0.0.1:3306/mayikt1?serverTimezone=UTC

原因jdbc地址填写是错误的



3. Access denied for user 'root1'@'localhost' (using password: YES)

	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.jav

mysql 连接账户和密码 错误



4. Table 'mayikt.mayikt_users11' doesn't exist 在我们的mayikt数据库

是没有mayikt_users11



## jdbcAPI详解


DriverManager   驱动程序管理器是负责管理驱动程序的，驱动注册以后，会保存在DriverManager中的已注册列表中后续的处理就可以对这个列表进行操作.

注册驱动方式

1.<font style="color:#000000;">DriverManager</font>.registerDriver();

2.写代码实现

<font style="color:#000000;">  Class</font>._forName_(<font style="color:#067d17;">"com.mysql.jdbc.Driver"</font>);

3.<font style="color:#067d17;">com.mysql.jdbc.Driver类中存在静态代码快</font>

```java
public class Driver extends NonRegisteringDriver implements java.sql.Driver {
    //
    // Register ourselves with the DriverManager
    //
    static {
        try {
            java.sql.DriverManager.registerDriver(new Driver());
        } catch (SQLException E) {
            throw new RuntimeException("Can't register driver!");
        }
    }

```

4.开发者是不需要调用<font style="color:#000000;">DriverManager</font>.registerDriver();方法，因为我们在使用class._forName 会加载到我们的_

<font style="color:#067d17;">com.mysql.jdbc.Driver 通过</font>Driver静态代码快 注册我们的Driver驱动。

5.mysql5之后，在jar包中存在一个java.sql.Driver配置文件，文件指定加载com.mysql.cj.jdbc.Driver

通过SPI机制实现。





类加载器、spi 反射技术 javase进阶基础

6.获取连接<font style="color:#000000;">Connection 连接对象</font>

<font style="color:#000000;">Connection connection </font>= <font style="color:#000000;">DriverManager</font>._getConnection_(<font style="color:#067d17;">"数据库连接地址"</font>,  
        <font style="color:#067d17;">"用户名称"</font>, <font style="color:#067d17;">"用户密码"</font>);   

参数：指定连接的路径 语法：jdbc://mysql://ip地址:端口号码/数据库名称

 user:用户名称

 pwd:用户的密码



ResultSet结果集对象

1.判断结果集是否有数据: boolean next();

1.1.有数据返回true 并将索引向下移动一行

1.2.没有数据返回false

2.获取结果集中的数据：xxx.getxx(列名称)  注意与 数据库数据类型需要对应



## jdbc案例
### 需求讨论与分层架构
使用jdbc技术访问到mysql数据库 对 student学生数据做增删改查操作

1.初始化数据库

```plsql
CREATE TABLE `mayikt_student` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `age` tinyint DEFAULT '0',
  `address` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb3;

INSERT INTO `mayikt`.`mayikt_student` (`id`, `name`, `age`, `address`) VALUES ('1', 'mayikt', '18', '武汉市');
INSERT INTO `mayikt`.`mayikt_student` (`id`, `name`, `age`, `address`) VALUES ('2', 'meite', '23', '上海市');
INSERT INTO `mayikt`.`mayikt_student` (`id`, `name`, `age`, `address`) VALUES ('3', '李思', '12', '孝感市');
INSERT INTO `mayikt`.`mayikt_student` (`id`, `name`, `age`, `address`) VALUES ('4', '刘流', '27', '武汉市');
INSERT INTO `mayikt`.`mayikt_student` (`id`, `name`, `age`, `address`) VALUES ('5', '王麻子', '12', '孝感市');
INSERT INTO `mayikt`.`mayikt_student` (`id`, `name`, `age`, `address`) VALUES ('6', 'KKK', '11', '成都');
INSERT INTO `mayikt`.`mayikt_student` (`id`, `name`, `age`, `address`) VALUES ('7', 'xiaokey', '22', '武汉市');
INSERT INTO `mayikt`.`mayikt_student` (`id`, `name`, `age`, `address`) VALUES ('8', '999', '33', '孝感市');
INSERT INTO `mayikt`.`mayikt_student` (`id`, `name`, `age`, `address`) VALUES ('9', 'wang', '55', '武汉市');
INSERT INTO `mayikt`.`mayikt_student` (`id`, `name`, `age`, `address`) VALUES ('10', 'lisi', '38', '孝感市');

```

2.定义实体类层

```plsql
/**
 * <AUTHOR>
 * @ClassName Student
 * @qq 644064779
 * @addres www.mayikt.com
 * 微信:yushengjun644
 */
public class Student {
    private Long id;
    private String name;
    private Integer age;
    private String address;
}
```

数据库中的数据类型需要与db对应  实体类中的 基本数据类型 建议用包装类 默认是为null





分层架构

com.mayikt.entity---实体类----创建实体类与数据库表结构字段一一对应的

com.mayikt.dao----数据库访问层----db打交道

com.mayikt.serivce---业务逻辑层

com.mayikt.controller---控制层

如果在db数据类型是为varchar 对应 string

如果在db数据类型是为int对应 <font style="color:#000000;">Integer</font>

<font style="color:#000000;"></font>

<font style="color:#000000;">需求1：查询所有学生信息</font>

<font style="color:#000000;">需求2：根据id查询学生信息</font>

<font style="color:#000000;">需求3：新增学生信息 ----insert into</font>

<font style="color:#000000;">需求4：根据主键id修改学生信息</font>

<font style="color:#000000;">需求5：根据主键id删除学生信息  企业实际开发中 不会真正物理删除数据 而是隐藏update的形式。---javaweb开发。</font>

<font style="color:#000000;"></font>

1.导入mysql驱动jar包;

2.注册驱动 javase 反射机制<font style="color:#000000;">Class</font>.forName()

3.获取数据库连接

4.获取执行者对象

5.执行sql语句并获取返回结果

6.对结果进行处理

7.释放jdbc资源



executeUpdate----执行insert 、update、delete sql语句

返回影响行数

### 相关代码
#### 实体类层
```java
public class StudentEntity {
    /**
    * 学生对象
    * 在java中定义数据库实体类层
    * 不建议使用基本数据类型 使用包装类
    */
    /**
    * 学生的id
    */
    private Long id; // 默认值null
    /**
    * 学生姓名
    */
    private String name;
    /**
    * 学生年龄
    */
    private Integer age;
    /**
    * 学生的地址
    */
    private String address;
    
    public StudentEntity(Long id, String name, Integer age, String address) {
        this.id = id;
        this.name = name;
        this.age = age;
        this.address = address;
    }
    public StudentEntity( String name, Integer age, String address) {
        this.name = name;
        this.age = age;
        this.address = address;
    }
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public Integer getAge() {
        return age;
    }
    
    public void setAge(Integer age) {
        this.age = age;
    }
    
    public String getAddress() {
        return address;
    }
    
    public void setAddress(String address) {
        this.address = address;
    }
    
    @Override
    public String toString() {
        return "StudentEntity{" +
            "id=" + id +
            ", name='" + name + '\'' +
            ", age=" + age +
            ", address='" + address + '\'' +
            '}';
    }
}
```

#### Dao层
```java
package com.mayikt.dao;

import com.mayikt.entity.StudentEntity;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @ClassName StudentDao
 * @qq 644064779
 * @addres www.mayikt.com
 * 微信:yushengjun644
 */
public class StudentDao {
    /**
     * 学生对象数据库访问层
     */

    /**
     * 查询所有的学生信息
     *
     * @return
     */
    public ArrayList<StudentEntity> allStudent() {
        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            //A.java连接mysql数据库查询所有数据
            //1.导入mysql驱动jar包;
            //2. 注册驱动 javase 反射机制Class.forName()
            Class.forName("com.mysql.cj.jdbc.Driver");
            //3. 获取数据库连接
            connection = DriverManager.getConnection("*****************************************************", "root", "root");
            //4. 获取执行者对象
            statement = connection.createStatement();
            //5. 执行sql语句并获取返回结果
            resultSet = statement.executeQuery("select  * from mayikt_student");
            ArrayList<StudentEntity> studentEntities = new ArrayList<>();
            //6. 对结果进行处理
            while (resultSet.next()) { // 如果false结束该循环
                // 获取该行数据的第一列 id
                Long id = resultSet.getLong("id");
                // 获取该行数据的第二列 name
                String name = resultSet.getString("name");
                // 获取该行数据的第三列 age
                Integer age = resultSet.getInt("age");
                // 获取该行数据的第四列 address
                String address = resultSet.getString("address");
                // 将db中查询到数据封装成java学生对象
                StudentEntity studentEntity = new StudentEntity(id, name, age, address);
                // 将该对象存入到集合中
                studentEntities.add(studentEntity);
            }
            return studentEntities;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            //  7. 释放jdbc资源
            try {
                if (resultSet != null)
                    resultSet.close();
                if (statement != null)
                    statement.close();
                if (connection != null)
                    connection.close();
            } catch (Exception e) {
                e.printStackTrace();
            }


        }

    }

    /**
     * 根据学生id 查询学生信息 学生的id
     *
     * @return
     */
    public StudentEntity getByIdStudent(Long stuId) {
        /**
         * 判断用户是否传递学生id的值
         */
        if (stuId == null) {
            return null;
        }
        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            //A.java连接mysql数据库查询所有数据
            //1.导入mysql驱动jar包;
            //2. 注册驱动 javase 反射机制Class.forName()
            Class.forName("com.mysql.cj.jdbc.Driver");
            //3. 获取数据库连接
            connection = DriverManager.getConnection("*****************************************************", "root", "root");
            //4. 获取执行者对象
            statement = connection.createStatement();
            //5. 执行sql语句并获取返回结果 自己拼接 查询sql语句
            resultSet = statement.executeQuery("select  * from mayikt_student where id=" + stuId);
            boolean result = resultSet.next(); // 查询不到数据 false
            // 判断如果查询不到数据 则不会取值
            if (!result) {
                return null;
            }
            //6. 对结果进行处理
            // 获取该行数据的第一列 id
            Long id = resultSet.getLong("id");
            // 获取该行数据的第二列 name
            String name = resultSet.getString("name");
            // 获取该行数据的第三列 age
            Integer age = resultSet.getInt("age");
            // 获取该行数据的第四列 address
            String address = resultSet.getString("address");
            // 将db中查询到数据封装成java学生对象
            StudentEntity studentEntity = new StudentEntity(id, name, age, address);
            return studentEntity;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            //  7. 释放jdbc资源
            try {
                if (resultSet != null)
                    resultSet.close();
                if (statement != null)
                    statement.close();
                if (connection != null)
                    connection.close();
            } catch (Exception e) {
                e.printStackTrace();
            }


        }

    }

    /**
     * 插入我们的学生
     *
     * @param stu
     * @return
     */
    public int insertStudent(StudentEntity stu) {
        Connection connection = null;
        Statement statement = null;
        try {
            //A.java连接mysql数据库查询所有数据
            //1.导入mysql驱动jar包;
            //2. 注册驱动 javase 反射机制Class.forName()
            Class.forName("com.mysql.cj.jdbc.Driver");
            //3. 获取数据库连接
            connection = DriverManager.getConnection("*****************************************************", "root", "root");
            //4. 获取执行者对象
            statement = connection.createStatement();
            //5. 执行sql语句并获取返回结果 executeUpdate执行 insert sql语句
            String insertStudentSql = "INSERT INTO mayikt_student values(null,'" + stu.getName() + "'," + stu.getAge() + ",'" + stu.getAddress() + "')";
            System.out.println("insertStudentSql:" + insertStudentSql);
            // log输出
            int result = statement.executeUpdate(insertStudentSql);
            // 执行该sql语句 影响行数
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        } finally {
            //  7. 释放jdbc资源
            try {
                if (statement != null)
                    statement.close();
                if (connection != null)
                    connection.close();
            } catch (Exception e) {
                e.printStackTrace();
            }


        }
    }

    /**
     * 修改学生的信息
     *
     * @param stu
     * @return
     */
    public int updateStudent(StudentEntity stu) {
        Connection connection = null;
        Statement statement = null;
        try {
            //A.java连接mysql数据库查询所有数据
            //1.导入mysql驱动jar包;
            //2. 注册驱动 javase 反射机制Class.forName()
            Class.forName("com.mysql.cj.jdbc.Driver");
            //3. 获取数据库连接
            connection = DriverManager.getConnection("*****************************************************", "root", "root");
            //4. 获取执行者对象
            statement = connection.createStatement();
            //5. 执行sql语句并获取返回结果 executeUpdate执行 update sql语句
            String updateStudentSql = "update mayikt_student  set name='" + stu.getName() + "' ,age=" + stu.getAge() + "," +
                    "address='" + stu.getAddress() + "' where id=" + stu.getId() + "";
            // log输出
            System.out.println("updateStudentSql:" + updateStudentSql);
            int result = statement.executeUpdate(updateStudentSql);
            // 执行该sql语句 影响行数
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        } finally {
            //  7. 释放jdbc资源
            try {
                if (statement != null)
                    statement.close();
                if (connection != null)
                    connection.close();
            } catch (Exception e) {
                e.printStackTrace();
            }


        }
    }

    /**
     * 根据主键id删除学生信息
     *
     * @param id
     * @return
     */
    public int delStudent(Long id) {
        // 判断id是否为null
        if (id == null) {
            return 0;
        }
        Connection connection = null;
        Statement statement = null;
        try {
            //A.java连接mysql数据库查询所有数据
            //1.导入mysql驱动jar包;
            //2. 注册驱动 javase 反射机制Class.forName()
            Class.forName("com.mysql.cj.jdbc.Driver");
            //3. 获取数据库连接
            connection = DriverManager.getConnection("*****************************************************", "root", "root");
            //4. 获取执行者对象
            statement = connection.createStatement();
            //5. 执行sql语句并获取返回结果 executeUpdate执行 delete sql语句
            String delSQL = "delete from  mayikt_student where id=" + id;
            System.out.println("delSql:" + delSQL);
            // log输出
            int result = statement.executeUpdate(delSQL);
            // 执行该sql语句 影响行数
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        } finally {
            //  7. 释放jdbc资源
            try {
                if (statement != null)
                    statement.close();
                if (connection != null)
                    connection.close();
            } catch (Exception e) {
                e.printStackTrace();
            }


        }
    }

}

```

#### Service
```java
package com.mayikt.serivce;

import com.mayikt.dao.StudentDao;
import com.mayikt.entity.StudentEntity;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @ClassName StudentService
 * @qq 644064779
 * @addres www.mayikt.com
 * 微信:yushengjun644
 */
public class StudentService {

    /**
     * new 学生对象dao层
     */
    private StudentDao studentDao = new StudentDao();

    /**
     * 查询所有的学生信息
     *
     * @return
     */
    public ArrayList<StudentEntity> allStudent() {
        // 在通过业务逻辑层调用dao层代码
        ArrayList<StudentEntity> studententities = studentDao.allStudent();
        return studententities;
    }

    public StudentEntity getByIdStudent(Long stuId) {
        return studentDao.getByIdStudent(stuId);
    }

    public int insertStudent(StudentEntity stu) {
        return studentDao.insertStudent(stu);
    }

    public int updateStudent(StudentEntity stu) {
        return studentDao.updateStudent(stu);
    }

    public int delStudent(Long id) {
        return studentDao.delStudent(id);
    }

}

```

### jdbc工具类封装
1.编写配置文件

在src目录下创建config.properties配置文件

```properties
driverClass=com.mysql.cj.jdbc.Driver
url=*****************************************************
user=root
password=root
```

2.编写jdbc工具 获取连接 释放连接



类名称.方法名称---



```plain
package com.mayikt.utils;

import java.io.InputStream;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Properties;

/**
 * <AUTHOR>
 * @ClassName MayiktJdbcUtils
 * @qq 644064779
 * @addres www.mayikt.com
 * 微信:yushengjun644
 */
public class MayiktJdbcUtils {
    /**
     * 1.需要将我们的构造方法私有化 ---工具类 不需要 new出来 是通过类名称.方法名称访问
     */
    private MayiktJdbcUtils() {

    }

    /**
     * 2.定义工具类 需要 声明 变量
     */
    private static String driverClass;
    private static String url;
    private static String user;
    private static String password;

    /**
     *3.使用静态代码快 来给我们声明好 jdbc变量赋值（读取config.properties）
     */
    static {
        try {
            // 1.读取config.properties  IO 路径 相对路径
            InputStream resourceAsStream = MayiktJdbcUtils.class.getClassLoader().
                    getResourceAsStream("config.properties");
            // 2.赋值给我们声明好的变量
            Properties properties = new Properties();
            properties.load(resourceAsStream);
            driverClass = properties.getProperty("driverClass");
            url = properties.getProperty("url");
            user = properties.getProperty("user");
            password = properties.getProperty("password");
            // 3.注册驱动类
            Class.forName(driverClass);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 4.封装连接方法
     */
    public static Connection getConnection() {
        try {
            Connection connection = DriverManager.getConnection(url, user, password);
            return connection;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 5.封装释放连接方法 (重载)
     */
    public static void closeConnection(ResultSet resultSet, Statement statement, Connection connection) {
        // 1.查询 释放连接 resultSet  statement connection
        try {
            if (resultSet != null)
                resultSet.close();
            if (statement != null)
                statement.close();
            if (connection != null)
                connection.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 2.增删改 释放连接 statement connection
    }

    /**
     * 增删改---释放jdbc资源
     *
     * @param statement
     * @param connection
     */
    public static void closeConnection(Statement statement, Connection connection) {
        // 1.查询 释放连接 resultSet  statement connection
        closeConnection(null, statement, connection);
    }


}

```



### 封装主菜单系统
![1647487313967-fc82d1c2-b71c-4ec1-8947-600260b64111.png](./img/8X9e0d9ODVPNIerj/1647487313967-fc82d1c2-b71c-4ec1-8947-600260b64111-479265.png)

```java
package com.mayikt.test;

import com.mayikt.entity.StudentEntity;
import com.mayikt.serivce.StudentService;

import java.util.ArrayList;
import java.util.Scanner;

/**
* <AUTHOR>
* @ClassName IndexTest
* @qq 644064779
* @addres www.mayikt.com
* 微信:yushengjun644
*/
public class IndexTest {
    private static StudentService studentService = new StudentService();
    
    /**
    * new对象存放 堆内存
    *
    * @param args
    */
    public static void main(String[] args) {
        mainMenu();
    }
    
    /**
    * 1.需要定义主菜单程序的入口
    */
    public static void mainMenu() {
        while (true) {
            // 1.提示语
            System.out.println("欢迎来到我们每特教育蚂蚁课堂学生管理系统");
            System.out.println("1.查询所有的学生信息");
            System.out.println("2.根据学生id查询学生信息");
            System.out.println("3.新增学生信息");
            System.out.println("4.根据学生id修改学生信息");
            System.out.println("5.根据学生id删除学生信息");
            System.out.println("请选择对应序号:");
            // 2.接受用户输入的序号 int
            Scanner scanner = new Scanner(System.in);
            int result = scanner.nextInt();
            // 3.在根据用户选择的序号 判断
            switch (result) {
                case 1:
                    showAllStudent();
                    break;//直接让我们while退出了
                case 2:
                    findByIdStudent();
                    break;
                case 3:
                    insertStudent();
                    break;
                case 4:
                    updateIdStudent();
                    break;
                case 5:
                    delIdStudent();
                    break;
            }
        }
    }
    
    /**
    * 查询所有学生信息
    */
    public static void showAllStudent() {
        System.out.println("查询到的所有学生信息");
        ArrayList<StudentEntity> studentEntities = studentService.allStudent();
        for (StudentEntity stu : studentEntities) {
            System.out.println(stu);
        }
    }
    
    /**
    * 根据学生id查询学生信息
    */
    public static void findByIdStudent() {
        System.out.println("请输入学生的id：");
        Scanner scanner = new Scanner(System.in);
        Long stuId = scanner.nextLong();
        // 根据用户输入的学生id查询学生信息
        // 根据用户输入的学生id查询学生信息 查询不到  查询得到
        StudentEntity student = studentService.getByIdStudent(stuId);
        if (student == null) {
            System.out.println("该学生id" + student + ",不存在的");
            return;// 后面代码不会继续执行
        }
        // 查询到了学生信息
        System.out.println("学生信息：" + student);
    }
    
    /**
    * 根据学生id删除学生信息
    */
    public static void delIdStudent() {
        System.out.println("请输入学生的id：");
        Scanner scanner = new Scanner(System.in);
        Long stuId = scanner.nextLong();
        int result = studentService.delStudent(stuId);
        if (result > 0) {
            System.out.println("删除成功");
        } else {
            System.out.println("删除失败");
        }
    }
    
    /**
    * 插入我们的学生信息
    */
    public static void insertStudent() {
        Scanner scanner = new Scanner(System.in);
        //        System.out.println("请输入学生的id：");
        //        Long stuId = scanner.nextLong();
        System.out.println("请输入学生的名称：");
        String name = scanner.nextLine();
        System.out.println("请输入学生的年龄：");
        int age = scanner.nextInt();
        scanner.nextLine(); // 跳过
        System.out.println("请输入学生的地址：");
        String address = scanner.nextLine();
        StudentEntity studentEntity = new StudentEntity(name, age, address);
        int result = studentService.insertStudent(studentEntity);
        if (result > 0) {
            System.out.println("插入学生信息成功");
        } else {
            System.out.println("插入学生信息失败");
        }
    }
    
    public static void updateIdStudent() {
        // 需要先根据学生的id查询该学生信息 如果查询得到的情况下 才会修改学生信息
        // 查询不到的情况下 就不会修改学生信息
        Scanner scanner = new Scanner(System.in);
        System.out.println("请输入学生的id：");
        Long stuId = scanner.nextLong();
        // 根据学生id查询学生信息
        StudentEntity student = studentService.getByIdStudent(stuId);
        if (student == null) {
            System.out.println("没有查询该学生的id对应学生信息");
            return;
        }
        scanner.nextLine();
        System.out.println("请输入学生的名称：");
        String name = scanner.nextLine();
        System.out.println("请输入学生的年龄：");
        int age = scanner.nextInt();
        scanner.nextLine(); // 跳过
        System.out.println("请输入学生的地址：");
        String address = scanner.nextLine();
        // 封装接受的参数 变成学生对象
        StudentEntity studentEntity = new StudentEntity(stuId, name, age, address);
        int result = studentService.updateStudent(studentEntity);
        if (result > 0) {
            System.out.println("修改成功");
        } else {
            System.out.println("修改失败");
        }
    }
}


```

Exception in thread "main" java.util.InputMismatchException

	at java.util.Scanner.throwFor(Scanner.java:864)

	at java.util.Scanner.next(Scanner.java:1485)

	at java.util.Scanner.nextInt(Scanner.java:2117)

	at java.util.Scanner.nextInt(Scanner.java:2076)

	at com.mayikt.test.IndexTest.mainMenu(IndexTest.java:43)

	at com.mayikt.test.IndexTest.main(IndexTest.java:25)





<font style="color:rgb(77, 77, 77);">in.nextLine();不能放在in.nextInt();代码段后面</font>

<font style="color:rgb(77, 77, 77);">他不是跳过你了，而是他已经有内容了，内容就是‘\n’。因为nextInt();接收一个整型字符,不会读取\n，nextline();读入一行文本，会读入"\n"字符</font>

<font style="color:rgb(77, 77, 77);">解决办法in.nextInt() 中间(in.nextLine();)  in.nextLine();</font>

<font style="color:rgb(77, 77, 77);"></font>

## jdbc实现用户登录与注册
```sql
CREATE TABLE `mayikt_users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `phone` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `pwd` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb3;

```

```java
package com.mayikt.entity;

/**
 * <AUTHOR>
 * @ClassName UserEntity
 * @qq 644064779
 * @addres www.mayikt.com
 * 微信:yushengjun644
 */
public class UserEntity {
    /**
     * 用户id
     */
    private Long id;
    /**
     * 用户手机号码
     */
    private String phone;
    /**
     * 用户的密码
     */
    private String pwd;

    public UserEntity(Long id, String phone, String pwd) {
        this.id = id;
        this.phone = phone;
        this.pwd = pwd;
    }

    public UserEntity(String phone, String pwd) {
        this.phone = phone;
        this.pwd = pwd;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }
}

```

```java
package com.mayikt.dao;

import com.mayikt.entity.StudentEntity;
import com.mayikt.entity.UserEntity;
import com.mayikt.utils.MayiktJdbcUtils;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;

/**
* <AUTHOR>
* @ClassName UserDao
* @qq 644064779
* @addres www.mayikt.com
* 微信:yushengjun644
*/
public class UserDao {
    /**
    * 用户的注册
    * 思考点？如果用户一直使用相同的手机号码注册？
    * 该手机号码存在 不需要重复注册？给手机号码----在数据库中不存在
    */
    public int registerUser(UserEntity userEntity) {
        Connection connection = null;
        Statement statement = null;
        try {
            //A.java连接mysql数据库查询所有数据
            //1.导入mysql驱动jar包;
            //2. 注册驱动 javase 反射机制Class.forName()
            connection = MayiktJdbcUtils.getConnection();
            //4. 获取执行者对象
            statement = connection.createStatement();
            //5. 执行sql语句并获取返回结果 executeUpdate执行 insert sql语句
            String insertUserSql = "INSERT INTO `mayikt`.`mayikt_users` (`id`, `phone`, `pwd`) VALUES (null, '" + userEntity.getPhone() + "', '" + userEntity.getPwd() + "');";
            System.out.println("insertStudentSql:" + insertUserSql);
            // log输出
            int result = statement.executeUpdate(insertUserSql);
            // 执行该sql语句 影响行数
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        } finally {
            //  7. 释放jdbc资源
            MayiktJdbcUtils.closeConnection(statement, connection);
        }
    }
    
    /**
    * 根据手机号码查询用户的信息
    *
    * @param phone
    * @return
    */
    public UserEntity getByPhoneUser(String phone) {
        // 判断用户输入的手机号码是否是为null(正则表达式)
        //phone="";
        if (phone == null || phone.length() == 0) {
            return null;
        }
        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            //A.java连接mysql数据库查询所有数据
            //1.导入mysql驱动jar包;
            //2. 注册驱动 javase 反射机制Class.forName()
            connection = MayiktJdbcUtils.getConnection();
            //4. 获取执行者对象
            statement = connection.createStatement();
            //5. 执行sql语句并获取返回结果 自己拼接 查询sql语句
            String getByPhoneUserSql = "select * from mayikt_users where phone='" + phone + "'";
            System.out.println(getByPhoneUserSql);
            resultSet = statement.executeQuery(getByPhoneUserSql);
            boolean result = resultSet.next(); // 查询不到数据 false
            // 判断如果查询不到数据 则不会取值
            if (!result) {
                return null;
            }
            //6. 对结果进行处理
            // 获取该行数据的第一列 id
            Long dbId = resultSet.getLong("id");
            // 获取该行数据的第二列 phone
            String dbPhone = resultSet.getString("phone");
            // 获取该行数据的第三列 pwd
            String dbPwd = resultSet.getString("pwd");
            // 将db中查询到数据封装成user对象
            return new UserEntity(dbId, dbPhone, dbPwd);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            //  7. 释放jdbc资源
            MayiktJdbcUtils.closeConnection(resultSet, statement, connection);
            
        }
    }
    
    /**
    * 用户登录的方法
    *
    * @return
    */
    public UserEntity login(UserEntity userEntity) {
        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            //A.java连接mysql数据库查询所有数据
            //1.导入mysql驱动jar包;
            //2. 注册驱动 javase 反射机制Class.forName()
            connection = MayiktJdbcUtils.getConnection();
            //4. 获取执行者对象
            statement = connection.createStatement();
            //5. 执行sql语句并获取返回结果 自己拼接 查询sql语句
            String loginSql = "select * from mayikt_users where phone='" + userEntity.getPhone() + "' and pwd='" + userEntity.getPwd() + "';";
            System.out.println(loginSql);
            resultSet = statement.executeQuery(loginSql);
            boolean result = resultSet.next(); // 查询不到数据 false
            // 判断如果查询不到数据 则不会取值
            if (!result) {
                return null;
            }
            //6. 对结果进行处理
            // 获取该行数据的第一列 id
            Long dbId = resultSet.getLong("id");
            // 获取该行数据的第二列 phone
            String dbPhone = resultSet.getString("phone");
            // 获取该行数据的第三列 pwd
            String dbPwd = resultSet.getString("pwd");
            // 将db中查询到数据封装成user对象
            return new UserEntity(dbId, dbPhone, dbPwd);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            //  7. 释放jdbc资源
            MayiktJdbcUtils.closeConnection(resultSet, statement, connection);
            
        }
    }
    
}

```

```java
package com.mayikt.test;

import com.mayikt.entity.UserEntity;
import com.mayikt.serivce.UserSerivce;

import java.util.Scanner;

/**
 * <AUTHOR>
 * @ClassName UserTest
 * @qq 644064779
 * @addres www.mayikt.com
 * 微信:yushengjun644
 */
public class UserTest {
    private UserSerivce userSerivce = new UserSerivce();

    /**
     * 该登录代码 bug ----sql注入破解登录  手机号码和密码输入错误的 但是可以登录
     *
     * @param args
     */
    public static void main(String[] args) {
        UserTest userTest = new UserTest();
        userTest.index();
    }

    public void index() {
        Scanner scanner = new Scanner(System.in);
        System.out.println("输入数字1：用户注册");
        System.out.println("输入数字2：用户登录");
        int number = scanner.nextInt();
        switch (number) {
            case 1:
                registerUser();
            case 2:
                login();
        }
    }

    public void registerUser() {
        Scanner scanner = new Scanner(System.in);
        System.out.println("请输入用户的手机号码");
        String phone = scanner.nextLine();
        System.out.println("请输入用户的密码");
        String pwd = scanner.nextLine();
        int result = userSerivce.registerUser(new UserEntity(phone, pwd));
        if (result > 0) {
            // 用户注册成功
            System.out.println("用户注册成功");
        } else {
            System.out.println("用户注册失败啦");
        }
    }

    public void login() {
        //给用户输入手机号码或者是密码错误机会 三次 直接退出程序
        for (int i = 1; i <= 3; i++) {
            Scanner scanner = new Scanner(System.in);
            System.out.println("请输入用户的手机号码");
            String phone = scanner.nextLine();
            System.out.println("请输入用户的密码");
            String pwd = scanner.nextLine();
            // 调用 登录业务逻辑方法
            UserEntity dbUserEntity = userSerivce.login(new UserEntity(phone, pwd));
            if (dbUserEntity != null) {
                System.out.println("登录成功");
                return;//退出循环
            } else {
                System.out.println("用户输入的手机号码或者密码不正确! 错误的次数:" + i);
            }
        }

    }
}

```



## 什么是JDBC SQL注入漏洞


什么是SQL注入攻击：

就是利用SQL语句的漏洞实现对系统攻击 

底层原理就是 通过传递参数(or 1=1 )拼接的SQL语句 导致其成立可以查询到数据。

登录SQL语句：

select * from mayikt_users where phone='18140668751' and pwd='12'; 

但是黑客传递参数：

黑客传递的参数 ' or 1='1

select * from mayikt_users where phone='' and pwd='' or 1='1'; 

![1648112165494-a173f5f9-5427-455b-b6b0-ce7bcf0d243e.png](./img/8X9e0d9ODVPNIerj/1648112165494-a173f5f9-5427-455b-b6b0-ce7bcf0d243e-814940.png)

如果我们使用sql语句拼接的情况下 很容易导致 被黑客sql注入

## 如何解决SQL注入漏洞


解决办法：

使用PreparedStatemnet（预编译执行者对象）

在sql语句执行之前，将sql语句进行提前编译，明确SQL语句的格式后，传递的参数 就是参数 不会拼接sql语句。

用？占位符赋值的方式 setxxx(参数1，参数2)

xxx: 数据类型

参数1：？的位置从编号1开始

参数2：？的实际参数

```java
String loginSql = "select * from mayikt_users where phone=? and pwd=?;";
statement = connection.prepareStatement(loginSql);
statement.setString(1, userEntity.getPhone());
statement.setString(2, userEntity.getPwd());
```

## jdbc事务管理器
### mysql中的事务
事务是必须满足4个条件（ACID）：原子性（Atomicity，或称不可分割性）、一致性（Consistency）、隔离性（Isolation，又称独立性）、持久性（Durability）。

1.原子性：一个事务（transaction）中的所有操作，要么全部完成，要么全部不完成，不会结束在中间某个环节。事务在执行过程中发生错误，会被回滚（Rollback）到事务开始前的状态，就像这个事务从来没有执行过一样。

2.一致性：在事务开始之前和事务结束以后，数据库的完整性没有被破坏。这表示写入的数据必须完全符合所有的预设规则，这包含数据的精确度、串联性以及后续数据库可以自发性地完成预定的工作。

3.隔离性：数据库允许多个并发事务同时对其数据进行读写和修改的能力，隔离性可以防止多个事务并发执行时由于交叉执行而导致数据的不一致。事务隔离分为不同级别，包括读未提交（Read uncommitted）、读提交（read committed）、可重复读（repeatable read）和串行化（Serializable）。

4.持久性：事务处理结束后，对数据的修改就是永久的，即便系统故障也不会丢失。





开启事务-----

{

sql写的操作

update、insert、delete

}

提交事务或者回滚事务

提交事务----事务里面做的写操作 可以查询到 写完之后的数据

回滚事务----事务里面做的写操作  直接回滚了 反悔了  查询不到



如果开启了事务，但是没有提交或者回滚事务  我们是查询不到未提交的数据。

回滚事务----查询不到数据



1.直接用 SET 来改变 MySQL 的自动提交模式:

SET AUTOCOMMIT=0 禁止自动提交

SET AUTOCOMMIT=1 开启自动提交

2.用 BEGIN, ROLLBACK, COMMIT来实现

BEGIN 开始一个事务

ROLLBACK 事务回滚

COMMIT 事务确认

演示：

```java
select * from mayikt_users;
SET AUTOCOMMIT=0;
begin;
insert into mayikt_users values(
null,'15527339852','124'
)
commit;

```

注意：我们在使用手动提交事务，如果长期不提交数据也不回滚数据 容易引发行锁问题 导致该行数据一直被锁住，无法被其他线程修改。

### 事务如果不提交、不回滚，会发生哪些问题？


我们在使用手动提交事务，如果不提交数据也不回滚数据 容易引发行锁问题 导致该行数据一直被锁住，无法被其他线程修改。

```plain

BEGIN;
UPDATE `mayikt`.`mayikt_users` SET `id`='1',
 `phone`='15523733967', `pwd`='2221' WHERE (`id`='1');
```

select * from information_schema.innodb_trx  

kill 17; 手动释放行锁

kill 19;手动释放行锁

![1648285462408-4a1173dd-22d2-4d71-827e-43a8d5b70b52.png](./img/8X9e0d9ODVPNIerj/1648285462408-4a1173dd-22d2-4d71-827e-43a8d5b70b52-368982.png)



在mysql InnoDB存储引擎中 多个线程如果同时修改同一行数据 最终只会有一个线程修改成功。

InnoDB存储引擎---行锁

### jdbc中的事务管理器
同一事务中所有的操作，都在使用同一个Connection对象。

①JDBC中的事务    

Connection的三个方法与事务有关：

setAutoCommit（boolean）:设置是否为自动提交事务，如果true（默认值为true）表示自动提交，也就是每条执行的SQL语句都是一个单独的事务，如果设置为false，那么相当于开启了事务了；con.setAutoCommit(false) 表示开启事务。

commit（）：提交结束事务。

rollback（）：回滚结束事务。



jdbc整合mysql中事务呢？手动事务呢？

事务呢？

增加、删除、修改数据---写操作 加上事务

查询操作---不需要加上事务的？

开启事务

提交事务

回滚事务



### MySQL多表联合查询操作
## jdbc数据库连接池
### 什么是数据库连接池
1.我们在JDBC编程中，每次创建和断开Connection对象都会消耗一定的时间和IO资源，如果需要频繁的与数据库打交道，该过程效率非常低。因为在Java程序与数据库之间建立连接时，数据库端要验证用户名和密码，并且要为这个连接分配资源,Java程序则要把代表连接的java.sql.Connection对象等加载到内存中,所以建立数据库连接的开销很大。

<font style="color:rgb(51, 51, 51);">2.为了避免频繁的创建数据库连接，与时我们可以通过数据库连接池</font>负责分配、管理和释放数据库连接,它允许应用程序重复使用现有的数据库连接，而不是重新建立。

3.数据库连接池大致实现原理：

数据库连接池在初始化时将创建一定数量的数据库连接放到连接池中，当应用程序访问数据库时并不是直接创建Connection,而是向连接池“申请”一个Connection。如果连接池中有空闲的Connection,则将其返回，否则创建新的Connection。使用完毕后,连接池会将该Connection回收，并交付其他的线程复用使用，以减少创建和断开数据库连接的次数，提高数据库的访问效率。

![1648522378427-01ec486c-46db-4740-85ad-58140896a429.png](./img/8X9e0d9ODVPNIerj/1648522378427-01ec486c-46db-4740-85ad-58140896a429-327201.png)

### 整合c3p0数据库连接池
#### 快速入门
<font style="color:rgb(51, 51, 51);">1.导入依赖Jar包 </font>

c3p0-0.9.5.2

mchange-commons-java-0.2.12.jar

mysql-connector-java-8.0.13.jar

jar包相关下载：

链接：[http://note.youdao.com/noteshare?id=61e2cc939390acc9c7e5017907e98044&sub=DAABBA2F350445D2AC6879CCC3F46EA7](http://note.youdao.com/noteshare?id=61e2cc939390acc9c7e5017907e98044&sub=DAABBA2F350445D2AC6879CCC3F46EA7)

![1648699070613-e92d3c36-93d2-43a7-b054-75627ce080f3.png](./img/8X9e0d9ODVPNIerj/1648699070613-e92d3c36-93d2-43a7-b054-75627ce080f3-839396.png)

2.使用c3p0连接池

```java
  ComboPooledDataSource pool = new ComboPooledDataSource();// 创建c3p0数据库连接池
  pool.setUser("root");// 用户名称
  pool.setPassword("root");// 用户密码
  pool.setJdbcUrl("**************************************************************************************************");// MySQL数据库连接url
  pool.setDriverClass("com.mysql.jdbc.Driver"); // 加载驱动
```

```java
   public UserEntity getUserEntity(Long id) {
        ResultSet resultSet = null;
        Connection connection = null;
        ComboPooledDataSource pool = new ComboPooledDataSource();// 创建c3p0数据库连接池
        try {
            pool.setUser("root");// 用户名称
            pool.setPassword("root");// 用户密码
            pool.setJdbcUrl("**************************************************************************************************");// MySQL数据库连接url
            pool.setDriverClass("com.mysql.jdbc.Driver"); // 加载驱动
            //2.获取连接对象
            connection = pool.getConnection();
            PreparedStatement preparedStatement = connection.prepareStatement("select * from  mayikt_users where id=?");
            preparedStatement.setLong(1, id);
            resultSet = preparedStatement.executeQuery();
            if (!resultSet.next()) {
                return null;
            }
            // 获取该行数据的第一列 id
            Long dbId = resultSet.getLong("id");
            // 获取该行数据的第二列 phone
            String dbPhone = resultSet.getString("phone");
            // 获取该行数据的第三列 pwd
            String dbPwd = resultSet.getString("pwd");
            // 将db中查询到数据封装成user对象
            return new UserEntity(dbId, dbPhone, dbPwd);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            if (resultSet != null) {
                try {
                    resultSet.close();
                } catch (SQLException throwables) {
                    throwables.printStackTrace();
                }
            }
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException throwables) {
                    throwables.printStackTrace();
                }
            }
        }
    }
```



#### 改成配置文件形式
1.在src目录创建c3p0.properties 或者c3p0-config.xml 

2.特别的名称要求，程序会自动去找寻这个配置文件

```xml
<c3p0-config>
    <!-- 使用默认的配置读取连接池对象 -->
    <default-config>
        <!--  连接参数 -->
        <!--需要修改自己数据库路径、用户账号、密码-->
        <property name="driverClass">com.mysql.jdbc.Driver</property>
        <property name="jdbcUrl">*****************************************************</property>
        <property name="user">root</property>
        <property name="password">root</property>
    </default-config>

</c3p0-config>
```

在jdbc自己代码配置jdbc连接信息 注释掉。

![1648705468906-f521804c-6607-4098-a851-e32d62b9dc77.png](./img/8X9e0d9ODVPNIerj/1648705468906-f521804c-6607-4098-a851-e32d62b9dc77-129399.png)

自动就可以找到c3p0-config.xml 

我们也可以在c3p0-config.xml  配置多个jdbc数据库连接信息

代码指定使用数据库连接配置信息

 ComboPooledDataSource pool = new ComboPooledDataSource("mayikt-otherc3p0");

不指定就使用默认的。

```xml
<c3p0-config>
    <!-- 使用默认的配置读取连接池对象 -->
    <default-config>
        <!--  连接参数 -->
        <!--需要修改自己数据库路径、用户账号、密码-->
        <property name="driverClass">com.mysql.jdbc.Driver</property>
        <property name="jdbcUrl">*****************************************************</property>
        <property name="user">root</property>
        <property name="password">root</property>
    </default-config>
    <named-config name="mayikt-otherc3p0">
        <!--  连接参数 -->
        <property name="driverClass">com.mysql.jdbc.Driver</property>
        <property name="jdbcUrl">****************************************************</property>
        <property name="user">root</property>
        <property name="password">root</property>
    </named-config>

</c3p0-config>
```



#### 核心配置文件说明 
1.连接池核心参数

```xml

    <!--初始化申请的连接数量-->
    <property name="initialPoolSize">5</property>
    <!--最大的连接数量-->
    <property name="maxPoolSize">10</property>
    <!--超时时间(单位毫秒)-->
    <property name="checkoutTimeout">3000</property>
```

2.基本原理是在内部对象池中维护一定数量的数据库连接，并对外暴露数据库连接获取和返回方法。用户可通过getConnection()方法获取连接，使用完毕后再通过Connection.close()方法将连接返回，注意此时连接并没有关闭，而是由连接池管理器回收，并为下一次使用做好准备。

数据库连接池的好处：

1. 节约资源

2. 用户访问高效

```xml
        ComboPooledDataSource pool = new ComboPooledDataSource();
        for (int i = 1; i <=20; i++) {
            Connection connection = pool.getConnection();
            System.out.println(i + "," + connection);
            if(i==10){
                // 归还
                connection.close();
            }

        }
```

线程池 底层原理与数据库连接池基本是相同的

### 整合druid数据库连接池
Druid（德鲁伊）：数据库连接池实现技术，由阿里巴巴提供的， 与C3P0数据库连接池 底层实现原理一样。

1.需要导入druid-1.2.8.jar 依赖jar包

依赖jar包下载：[http://note.youdao.com/noteshare?id=61e2cc939390acc9c7e5017907e98044&sub=DAABBA2F350445D2AC6879CCC3F46EA7](http://note.youdao.com/noteshare?id=61e2cc939390acc9c7e5017907e98044&sub=DAABBA2F350445D2AC6879CCC3F46EA7)

![1648877855554-79d2161f-11c4-4563-a088-123f04e4a39b.png](./img/8X9e0d9ODVPNIerj/1648877855554-79d2161f-11c4-4563-a088-123f04e4a39b-844735.png)

2.定义配置文件

是properties形式的

可以叫任意名称，可以放在任意目录下。程序不会自动寻找，因此在使用时需要手动加载相应的配置文件。

druid.properties

```properties
# 加载数据库驱动
driverClassName=com.mysql.jdbc.Driver
# 连接数据库的url,db1表示数据库名,useSSL=false表示不使用SSL规范
url=******************************************************************************************
# 用户登录数据库的账号和密码
username=root
password=root
# 初始化连接数量
initialSize=5
# 最大连接数量
maxActive=10
# 最大等待时间
maxWait=3000
```

3.加载配置文件。使用Properties集合

4.获取数据库连接池对象：通过工厂来获取 DruidDataSourceFactory

5.获取连接：getConnection()

```java
        //读取druid.properties
        Properties properties = new Properties();
        InputStream resourceAsStream =
                Test03.class.getClassLoader().getResourceAsStream("druid.properties");
        properties.load(resourceAsStream);
        // 创建druid.properties数据库连接池
        DataSource dataSource = DruidDataSourceFactory.createDataSource(properties);
        Connection connection = dataSource.getConnection();
        // 4.获取预编译执行者对象 防止 sql注入的问题
        PreparedStatement preparedStatement =
                connection.prepareStatement("select * from mayikt_users  where id=? ");
        // 5.设置参数
        preparedStatement.setLong(1, 2);
        // 6.执行sql语句
        ResultSet resultSet = preparedStatement.executeQuery();
        if (!resultSet.next()) {
            return;
        }
        //6. 对结果进行处理
        // 获取该行数据的第一列 id
        Long dbId = resultSet.getLong("id");
        // 获取该行数据的第二列 phone
        String dbPhone = resultSet.getString("phone");
        // 获取该行数据的第三列 pwd
        String dbPwd = resultSet.getString("pwd");
        // 将db中查询到数据封装成user对象
        UserEntity userEntity = new UserEntity(dbId, dbPhone, dbPwd);
        System.out.println(userEntity);
        resultSet.close();
        connection.close();
```

### druid数据库连接池工具类
```java
public class DataSourceUtils {
    /**
     * DataSource 数据库连接池对象
     */
    private static DataSource dataSource;

    /**
     * 私有化构造方法
     */
    private DataSourceUtils() {
    }

    /**
     * 使用静态代码块加载配置文件
     */
    static {
        try {
            // 读取配置文件
            Properties properties = new Properties();
            InputStream resourceAsStream = DataSourceUtils.class.getClassLoader().getResourceAsStream("druid.properties");
            properties.load(resourceAsStream);
            // 获取数据库连接池
            dataSource = DruidDataSourceFactory.createDataSource(properties);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 封装获取连接方法
     *
     * @return
     * @throws SQLException
     */
    public static Connection getConnection() {
        try {
            return dataSource.getConnection();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 封装关闭连接方法
     */
    public static void close(Connection connection, Statement statement, ResultSet resultSet) {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
        if (statement != null) {
            try {
                statement.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
        if (resultSet != null) {
            try {
                resultSet.close();
            } catch (SQLException throwables) {
                throwables.printStackTrace();
            }
        }
    }
}

```

## 常见问题
### 问题1 
```java
  connection = JdbcUtils.getConnection();//创建数据库连接
            String findUserByPhoneSQL = "SELECT * FROM mayikt_users where phone = ?";//明确SQL语句格式
            statement = connection.prepareStatement(findUserByPhoneSQL); //不要用拼接MySQL, 而是用调用预编译
            statement.setString(1, userPhone); //设置第一个参数
            System.out.println(findUserByPhoneSQL);
            resultSet = statement.executeQuery(findUserByPhoneSQL); //执行SQL语句,获得结果 --resultSet
```

报错：java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax;

 check the manual that corresponds to your MySQL server version for the right syntax to use near '?' at line 1

原因：将ppstm.executeQuery(sql);中的sql去掉，改成ppstm.executeQuery();

### 问题2
closing inbound before receiving peer s close_notify 

![1648811275588-dfc2f797-6315-45f8-8fd1-7dd0b98e4f77.png](./img/8X9e0d9ODVPNIerj/1648811275588-dfc2f797-6315-45f8-8fd1-7dd0b98e4f77-186122.png)

jdbc地址连接后面加上 &useSSL=false 就好了

### 问题3


在执行 预编译sql时，报错  

错误案例：

```json
 Connection connection = null;
        PreparedStatement statement = null;
        try {
            String SQL = "insert into mayikt_student values(null,?,?,?)";
            connection = JdbcUtils.getConnection();
            statement = connection.prepareStatement(SQL);
            statement.setString(1, name);
            statement.setInt(2, age);
            statement.setString(3, address);
            System.out.println(SQL);
            int i = statement.executeUpdate(sql);
            if (i == 1) {
                System.out.println("插入成功");
            } else if (i == 0) {
                System.out.println("插入失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            JdbcUtils.closeConnection(statement, connection);
        }
```

报错内容：You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '?,?,?,?)'

改成

  int i = statement.executeUpdate(); 去掉在executeUpdate执行 sql语句



> 更新: 2022-12-20 09:23:41  
> 原文: <https://www.yuque.com/yushengjun-vnnu4/trtvdh/pafarq>