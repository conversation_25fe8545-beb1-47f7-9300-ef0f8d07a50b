---
created: 2024-12-04
aliases: []
---

状态: [[进行中]]

标签: [[learnpython]] [[爬虫]] [[网络编程]] [[BeautifulSoup]] [[数据采集]] [[自动化]]

# Python网络爬虫实践指南
## 第一步：下载必要的包 
### pip install request 
### pip BeautifulSoup
## 第二步：构造一个获取图片的函数
### def get_image (url, path )    url是网页的链接 path 是图片存放的路径，之后的步骤放置在这个函数体内
## 第三步：伪装成浏览器进行发送请求
### headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'}  该元素在网页使用F12 ,然后点击网络，然后使用Ctrl+R ，拉到最上面，找到网页地址，点击进去之后，在最下面找到User-Agent
## 第四步：
### 使用page页面发送请求：page = requests.get(url, headers=headers,timeout=10)  设置了请求路径以及超时等待时间
### rint(page)
### print(page.text)
### page.raise_for_status()  # 如果请求失败，会抛出异常
### page.encoding = 'utf-8'  # 设置编码方式
### bs = BeautifulSoup(page.text, 'html.parser')  # 创建一个BeautifulSoup对象
### imgs = bs.find_all('img')  # 找到所有的img标签
### print(imgs,f"共有{len(imgs)}张图片")
### 然后使用for循环遍历得到的图片

# 参考资料
- Python爬虫教程
- BeautifulSoup文档
- HTTP协议规范
- Python网络编程指南

# 相关笔记
- [[learnpython]]
- [[BeautifulSoup]]
- [[网络编程]]
- [[HTTP]]
- [[数据采集]]
