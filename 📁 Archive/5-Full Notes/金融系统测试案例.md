---
created: 2024-12-16
aliases: []
---

状态: [[进行中]]

标签: [[金融]] [[软件测试]] [[项目部署]] [[接口测试]]

# 金融项目测试实战

# 实现步骤
- ![[Pasted image 20241216175035.png]]
- baw
	- member 用户管理模块的接口
		- 注册接口
			- def register(url ,br, data):
			- '''
			- 注册接口
			- :param url: 从env.ini读取的环境信息
			- :param br: baseRequests实例
			- :param data: 注册接口的数据
			- :return:
			- '''
			- url = url + '/futureloan/mvc/api/member/register'
			- r = br.post(url,data=data)
			- return r
		- 登录接口
			- def login(url,br,data):
			- url = url + '/futureloan/mvc/api/member/login'
			- r = br.post(url,data=data)
			- return r
		- 充值接口
			- def recharge(url,br,data):
			- url = url + '/futureloan/mvc/api/member/recharge'
			- r = br.post(url,data=data)
			- return r
		- 添加项目接口
			- def add_project(url, br, data):
			- '''
			- 添加项目接口
			- :param url: 从env.ini读取的环境信息
			- :param br: baseRequests实例
			- :param data: 添加项目的数据
			- :return:
			- '''
			- url = url + '/futureloan/mvc/api/loan/add'
			- r = br.post(url, data=data)
			- return r
- caw
	- baserequest.py
		-  class BaseRequest:
		- def __init__(self):
			- self.session = requests.session() # 创建一个session
		- def get(self,url,**kwargs):
			- try:
				- r = self.session.get(url,**kwargs)
				- print(f"发送get请求成功，URL为：{url},参数为：{kwargs}，响应信息为{r.text}")
				- return r
			- except Exception as e:
				- print(f"发送get请求失败，URL为：{url},参数为：{kwargs} ,异常信息为{e}")
		- def post(self,url,data=None,json=None,**kwargs):
			- try:
				- r = self.session.post(url,data=data,json=json,  **kwargs)
				- print(f"发送post请求成功，url为：{url},参数为：{data}{json}{kwargs}，响应信息为{r.text}")
				- return r
			- except Exception as e:
				- print(f"发送post请求失败, url为：{url},参数为：{data}{json}{kwargs}，异常信息为{e}")
	- readfile
		- 读取文件路径
		- def get_project_path():
		    - cf = os.path.abspath(__file__)
		    - cd = os.path.dirname(cf)
		    - cd = os.path.dirname(cd)
		    - return cd
		- 读取yaml测试数据
		- def read_yaml(file_path):
			- file_path =  get_project_path() + file_path
			- with open(file_path, 'r', encoding='utf-8') as f:
				- content = f.read()
				- 解析yaml文件
				- a = yaml.load(content, Loader=yaml.FullLoader)
				- return a
		- 读取配置文件
		- def read_ini(filepath,section,key):
			- filepath = get_project_path() + filepath
			- cp = configparser.ConfigParser()
			- cp.read(filepath)
			- value = cp.get(section, key)
			- return value
	- sql 
		- 清除用户在数据库中的数据，保证测试环境无干扰
		- def delete_all_customer(db_info):
		    - """清理用户数据"""
		    - conn = pymysql.connect(**db_info)
		    - cursor = conn.cursor()
		    - sql = "DELETE FROM member;"
		    - cursor.execute(sql)
		    - conn.commit()
		    - print(f"执行sql语句成功 {sql}")
		    - cursor.close()
		    - conn.close()
		- 使用sql语句获取用户的ID
		- def get_member_id(db_info, mobile):
		    - """获取用户ID"""
		    - conn = pymysql.connect(**db_info)
		    - cursor = conn.cursor()
		    - sql = f"SELECT id FROM member WHERE mobilephone='{mobile}';"
		    - cursor.execute(sql)
		    - member_id = cursor.fetchone()[0]
		    - cursor.close()
		    - conn.close()
		    - return member_id
- data
	- data_add.yaml
		- 测试用例
			- regdata: {"mobilephone":"18066668888","pwd":"abcd_1"}
			  - data: {"memberId": 1001, "title": "买车", "amount": 1000, "loanRate": 18.0, "loanTerm": 6, "loanDateType": 2, "repaymemtWay": 4, "biddingDays": 1}
			  - expect: {"status": 1, "code": "10001", "data": None, "msg": "加标成功"}
			  - 其中有详细的手机号，密码，以及多对应的项目数据，还有抛出的异常信息
	- env.ini
		- 全局配置文件
		- [testenc]
		- 以及url 和db.inf
		- 配置项目路径以及数据库相关配置
	- setup.yaml
	- 前置数据
		- data:
		  - mobilephone: "18066668888"
		  - pwd: "abcd_1"
		  - regname: "测试用户"
- conftest
	- #充值、取现、添加项目等的前置
	- @pytest.fixture()
	- def prepare(prepare_data, url, br, db_info):
	    - #准备环境
	    - delete_all_customer(db_info)
	    - register(url, br, prepare_data['data'])
	    - login(url, br,prepare_data['data'])
	    - yield
	    - #清理环境
	    - delete_all_customer(db_info)
	    - 
- test_add
	- @pytest.fixture(params=read_yaml("/data/data_add.yaml"))
	- def add_data(request):
	- return request.param
	
	- def test_add(add_data, url, br, prepare_add_user):
	- """测试添加项目接口"""

# 参考资料
- 接口测试指南
- Python测试框架文档
- 数据库操作手册
- YAML配置指南
- 项目测试规范

# 相关笔记
- [[金融]]
- [[软件测试]]
- [[项目部署]]
- [[接口测试]]
- [[learnpython]]
- [[数据库]]
- [[测试框架]]
- [[自动化测试]]


