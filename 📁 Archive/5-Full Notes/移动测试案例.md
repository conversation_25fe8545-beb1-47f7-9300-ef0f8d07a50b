---
created: 2024-12-05
aliases: []
---

状态: [[进行中]]

标签: [[APP]] [[软件测试]] [[手工测试]] [[自动化测试]] [[Android]] [[ADB]] [[性能测试]] [[兼容性测试]] [[Monkey测试]] [[Appium]] [[UIAutomator]]

# APP测试
- 搭建app测试环境
	-  安装Java运行环境，配置环境变量
	-  SDK解压缩后可使用，配置环境变量
- 安卓Andriod 架构分层
	- 应用程序层：比如微信，支付宝
	- 应用程序框架层：给上层的应用程序提供了所需的API（接口）和工具
	- 核心层：C/C++  库，图形化渲染、数据库支持、网络等等。
	- 运行时环境:早期的安卓、应用程序运行在虚拟机中。从安卓5.0开始，转向了ART安卓运行时环境。
	- 硬件抽象层：将不同的硬件设备统一的接口（摄像头，不同厂家，不同型号的），供上层使用。
	- Linux内核  ：给各种硬件设备提供底层驱动
- 安卓应用程序。四大组件
	- Activity （活动）:一个用户页面认为是一个活动。显示用户界面，响应用户操作。每个应用程序有一个主页面，主Activity。启动应用程序时，使用的是主Activity。
	- Service (后台服务)：看微信读书时，听音乐。音乐就是以后台的方式运行的。
	- Content Provide (内容供应组件)：应用程序会把用户数据存储在数据库中，联系人、备忘录、Sqllite数据库，a.db就是一个数据库。比如手机上的通讯录
	- Broadcast Receivers（广播接收组件）：安卓会发各种广播信息给应用，比如电池使用状态、来电话了、短信来了等产生广播，应用程序要监听并对广播事件进行处理。
- APP测试
	- 运行的设备：手机、手表（智能穿戴设备）、平板、车载等，本质上也是一台计算机。
	- APP测试与web测试、测试理论、测试方法 、技术都差不多。
	- APP测试会有一些专项测试：安装卸载升级、弱网/网络环境测试、兼容性、交叉事件等
- ADB
	- Android Debug Bridge 安卓调试桥。命令行工具可以通过这个二进制
		 - 客户端（adb.exe) :在电脑上运行，
		 - 守护进程  （adbd)：在移动设备上，以后台进程的方式运行
		 - 服务器（adbd)：在电脑上以后台进程的方式运行
	- adb devices 查看连接到电脑的设备  
	- adb install d:/apk/meitu8.6.2.0.apk 安装应用程序  
	- adb install -r d:/apk/meitu9.2.1.0.apk 应用已经存在了，对其进行升级  
	- adb shell pm list packages 查看设备上安装的应用 package manage 包管理  
	- adb shell pm list packages -3 列出所有用户安装的应用（非系统应用）  
	- adb shell pm list packages -s 列出所有系统的应用  
	- adb shell pm list packages -3 | findstr "mt" 过滤  
	- adb uninstall com.mt.mtxx.mtxx 卸载应用  
	- aapt dump badging d:/apk/meitu9.2.1.0.apk 查询包相关的信息  
	- aapt dump badging d:/apk/meitu9.2.1.0.apk | findstr "Activity" 查询包相关的信息，过滤  
	    - launchable-activity: name='com.mt.mtxx.mtxx.MainActivity'  
	- adb shell am start com.mt.mtxx.mtxx/.MainActivity 启动APP am Activity manage 活动管理  
	- adb shell am force-stop com.mt.mtxx.mtxx 停止APP  
	- adb push d:/1.jpeg /storage/emulated/11.jpeg 将本地文件复制到移动设备上  
	- adb pull /storage/emulated/11.jpeg d:/apk/12.jpeg 将移动设备上的文件复制到本地  
	    - 不能往根路径上存。c:/ d:/  
	- adb logcat 实时查看手机的日志  
	- adb logcat -d > d:/apk/abc.log 查看当前日志，并将其重定向到文件中  
	- adb reboot 重启设备
- APP专项测试
	- APP稳定性测试：在一定的压力下，长时间运行某个功能/系统，观察是否有Crash（崩溃）、无响应、内存泄漏等问题出现。
		- 稳定性测试工具：使用monkey工具来测试。monkey是安卓自带的一个工具，通过adb来启动。
		- adb shell monkey 
			-  [-p ALLOWED_PACKAGE [-p ALLOWED_PACKAGE] ...] 对哪些app进行测试
			-           [-c MAIN_CATEGORY [-c MAIN_CATEGORY] ...]
			-           [ [--ignore-crashes] [--ignore-timeouts] 忽略异常
			-            [--ignore-security-exceptions]
			-             [--monitor-native-crashes]    [--ignore-native-crashes]
			-             [--kill-process-after-error]    [--hprof]
			-              [--pct-touch PERCENT]触摸事件  [--pct-motion PERCENT]  滑动事件
			-              [ --pct-trackball PERCENT]轨迹球事件  [ --pct-syskeys PERCENT]  系统按键事件
			-             [--pct-nav PERCENT]  [--pct-majornav PERCENT] 导航、主导航栏事件
			-             - [--pct-nav PERCENT] [--pct-majornav PERCENT] 导航、主导航栏事件  
			    
			- [--pct-appswitch PERCENT] app切换 [--pct-flip PERCENT] 键盘事件  
			    
			- [--pct-anyevent PERCENT]任意事件 [--pct-pinchzoom PERCENT] 二指缩放事件  
			    
			- [--pct-permission PERCENT] 权限  
			    
			- [--pkg-blacklist-file PACKAGE_BLACKLIST_FILE] 黑名单，测试时不包含哪些app  
			    
			- [--pkg-whitelist-file PACKAGE_WHITELIST_FILE] 白名单，测试时包含哪些app  
			    
			- [--wait-dbg] [--dbg-no-events]  
			    
			- [--setup scriptfile] [-f scriptfile [-f scriptfile] ...]  
			    
			- [--port port]  
			    
			- [-s SEED] [-v [-v] ...] -v日志比较少 -v-v日志比较 多 -v-v-v 日志最多  
			    
			- [--throttle MILLISEC] [--randomize-throttle]  
			    
			- [--profile-wait MILLISEC]  
			    
			- [--device-sleep-time MILLISEC]  
			    
			- [--randomize-script]  
			    
			- [--script-log]  
			    
			- [--bugreport]  
			    
			- [--periodic-bugreport]  
			    
			- [--permission-target-system]  
			    
			- COUNT
		- adb shell monkey -p com.mt.mtxx.mtxx -p com.zzb.memoryleak --pct-touch 30 --pct-motion 20 -v-v-v --ignore-crashes --ignore-timeouts --ignore-security-exceptions --ignore-native-crashes 1000 1> d:/apk/monkey1.log 2> d:/apk/monkey_error.log
			- - 1> 把正常的信息重定向到文件1  
			- 2> 把错误的信息重定向到文件2  
		- 在日志文件中搜索  
		    - crash 异常，崩溃  
		    - ANR application not responding 应用程序无响应  
		    - Exception 异常（java异常）  
		- adb shell monkey -s 1733468123374 -p com.mt.mtxx.mtxx -p com.zzb.memoryleak --pct-touch 30 --pct-motion 20 -v-v-v --ignore-crashes --ignore-timeouts --ignore-security-exceptions --ignore-native-crashes 1000 1> d:/apk/monkey2.log 2> d:/apk/monkey_error2.log  
		    - -s 种子 -s的值一样时，模拟的事件顺序、事件所在的坐标点等都一样，用于复现问题，回归问题，可以通过这个方法重现当时的问题。
	- APP性能测试：
		- 服务器端的性能测试（接口测试后讲解）
		- 手机端的性能测试
			- APP启动时间
				- 冷启动：应用程序没有在后台运行，从点击图标到内容全部加载耗时。不超过2S
				- 热启动：在后台运行时，点击图标从后台切换到前台。不超过1S。
				- 测试方法：
					- adb shell am start -W  包名/.MainActivity
					- adb shell am start -W com.mt.mtxx.mtxx/.MainActivity
					- 1
						- Status: ok
						- Activity: com.mt.mtxx.mtxx/.MainActivity
						- ThisTime: 6202
						- TotalTime: 6202
						- WaitTime: 6232
						- Complete
					- 2
						- Warning: Activity not started, its current task has been brought to the front
						- Status: ok
						- Activity: com.mt.mtxx.mtxx/.MainActivity
						- ThisTime: 82
						- TotalTime: 82
						- WaitTime: 88
						- Complete
						- 
			- APP界面响应时间
				- 测试方法  
					- 手动测试，测试过程中观察响应时间
					- 界面自动化测试工具、Appium、UI Automator等，脚本执行时，测试每个操作的响应时间。
			- App内存使用率、cup使用率、单位时间耗电量
				- 空闲状态：切换到后台或者启动后，不做任何处理
				- 中强度状态：操作App一段时间之后
				- 高强度使用应用，比如
				- 在上述状态下：观察内存、CPU，是否有增长过快的情况，内存没有及时回落
				- adb shell dumpsys meminfo  包名
					- adb shell dumpsys meminfo  com.mt.mtxx.mtxx
					- ![[Pasted image 20241206092252.png]]
					- PSS 每个进程的内存实际使用量
				- adb shell dumpsys cpuinfo | findstr 包名
					-  adb shell dumpsys cpuinfo | findstr com.mt.mtxx.mtxx
					- ![[Pasted image 20241206092738.png]]
				- adb shell top -s 9  查看CPU
				- adb shell top -s 10  查看内存
				- 上方这两个是实时监控的
				- adb shell  dumpsys  battery 查看电池使用状态
					- ![[Pasted image 20241206093127.png]]
					- 可以在测试前查看一次，在测试后再查看一次
					- level 电量百分比
					- health 电池健康状态
					- status 充电状态
				- App流量消耗
					- adb shell  dumpsys netstats
				- FPS帧率
					- adb shell  dumpsys gfxinfo 包名
				- 测试方法  
					- 手动测试，输入命令，观察相应的指标。
					- 性能测试的工具/平台，把包提交到平台/工具上，输出测试报告。微信WeTest测试平台。
	- APP 弱网测试
		- 在APP使用过程中，会遇到不同的弱网环境，比如是山上、地下车库、电梯等。对APP 进行弱网测试。关注
			- 是否有崩溃，挂死等问题
			- 功能不可用
			- 是否有合理重连、重发机制
		- 测试方法  ：
			- 手机连接到电脑上，通过Fiddler、Charles、Net-Simulator（更专业）等工具，是指延时
			- ![[Pasted image 20241206095933.png]]
	- APP兼容性测试：
		- web兼容性：不同浏览器，不同版本的浏览器上，能否正常使用
		- app兼容型：
			- 不同设备：机型数量多、品牌多
			- 不同分辨率
		- 手工测试（正交实验法）
	- APP界面自动化测试
		- UIAutomator: 安卓的一个自动化测试框架，基于Java开发的，可以测试安卓上的应用程序，支持模拟器和真机。提供了一系列API，模拟用户操作。
		- UIAutomator2: UIAutomator 的升级版，将UIAutomator封装成了Python 库
		- Appium :可以用于IOS和安卓的测试，支持模拟器和真机。使用比较广泛。
		- 使用APPium
			- 命令行中输入appium打开服务器端。
			- 打开inspector
				- 创建一个session，测试安卓的设置功能
					- - { "platformName": "Android", // 测试的平台  
					- "automationName": "uiautomator2", // 使用的驱动。  
					- "deviceName": "127.0.0.1:62001", // 测试的哪台移动设备 adb devices中查到的  
					- "appPackage": "com.android.settings", // 测试的app包名 设置 
					- "appActivity": ".Settings"} // app的activity
				- 创建一个session，测试小米计算器
					- - aapt dumps badging d:/apk/xiaomicalc.apk 
					- com.miui.calculator  
					- com.miui.calculator.cal.CalculatorActivity

# 参考资料
- Android开发文档
- APP测试指南
- 性能测试手册
- 自动化测试框架文档
- ADB命令参考

# 相关笔记
- [[APP]]
- [[软件测试]]
- [[手工测试]]
- [[自动化测试]]
- [[性能测试]]
- [[Android]]
- [[ADB]]
- [[Appium]]
- [[UIAutomator]]