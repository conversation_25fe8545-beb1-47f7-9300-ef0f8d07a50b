---
created: 2024-10-30
aliases: []
---

状态: [[进行中]]

标签: [[MySQL]] [[数据库]] [[SQL]] [[后端开发]]

# MySQL 知识点总结

## 数据库操作

### 创建数据库
```sql
CREATE DATABASE 数据库名 CHARACTER SET utf8;
```

### 查看数据库
```sql
SHOW DATABASES;
```

### 使用数据库
```sql
USE 数据库名;
```

### 删除数据库
```sql
DROP DATABASE 数据库名;
```

### 修改数据库字符集
```sql
ALTER DATABASE 数据库名 CHARACTER SET gbk;
```

## 表操作

### 数据类型
- 字符串：`CHAR`（固定长度），`VARCHAR`（可变长度）
- 整数：`INT`，`INTEGER`
- 浮点数：`FLOAT`，`DOUBLE(m, d)`（m为整数和小数总长度，d为小数长度）
- 日期：`DATE`
- 日期和时间：`DATETIME`
- 时间：`TIME`
- 文本：`TEXT`

### 约束
- 主键：`PRIMARY KEY`
- 非空：`NOT NULL`
- 唯一：`UNIQUE`
- 默认值：`DEFAULT`
- 自增：`AUTO_INCREMENT`

### 创建表
```sql
CREATE TABLE 表名（列 类型 约束，列  类型  约束）;
```

### 重命名表
```sql
RENAME TABLE 旧表名 TO 新表名;
```

### 修改表
- 增加列
  ```sql
  ALTER TABLE 表名 ADD 列名 类型 约束;
  ```
- 修改列
  ```sql
  ALTER TABLE 表名 MODIFY 列名 类型 约束;
  ```
- 删除列
  ```sql
  ALTER TABLE 表名 DROP 列名;
  ```
- 增加主键
  ```sql
  ALTER TABLE 表名 ADD PRIMARY KEY (列名);
  ```

### 删除表
```sql
DROP TABLE 表名;
```

### 查看表
- 查看所有表
  ```sql
  SHOW TABLES;
  ```
- 查看建表语句
  ```sql
  SHOW CREATE TABLE 表名;
  ```
- 查看表结构
  ```sql
  DESC 表名;
  ```

## 数据操作

### 插入数据
```sql
INSERT INTO 表名（列名1，列名2，...） VALUES (值1，值2，...），（值1，值2，...）;
```

### 更新数据
```sql
UPDATE 表名 SET 列名='值' WHERE 条件;
```

### 删除数据
```sql
DELETE FROM 表名 WHERE 条件;
```

### 清空表
```sql
TRUNCATE TABLE 表名;
```

## 查询数据

### 查询所有行
```sql
SELECT * FROM 表名;
```

### 分页查询
```sql
SELECT * FROM 表名 LIMIT 起始位置, 数量;
```

### 排序查询
```sql
SELECT * FROM 表名 ORDER BY 列名 ASC/DESC;
```

### 条件查询
```sql
SELECT * FROM 表名 WHERE 条件;
```

### 聚合查询
```sql
SELECT DISTINCT 列名 FROM 表名;
```

### 函数
- 自定义函数
  ```sql
  DELIMITER **
  CREATE FUNCTION 函数名（参数列表） RETURNS 返回类型 BEGIN 返回值; END **
  ```
- 调用函数
  ```sql
  SELECT 函数名(参数列表);
  ```

## 字符串和数学函数

### CONCAT 连接
```sql
SELECT carnumber, CONCAT(shengfen, location) AS 位置 FROM cars;
```

### SUBSTRING 子字符串
```sql
SELECT carnumber, SUBSTRING(location, 1, 3) AS 城市 FROM cars;
```

### 数学函数
- 绝对值
  ```sql
  SELECT abs(-10);
  ```
- 四舍五入
  ```sql
  SELECT round(596.6);
  ```
- 随机数
  ```sql
  SELECT rand() AS 随机数;
  ```

### 聚合函数
- 计数
  ```sql
  SELECT count(*) AS 购买记录 FROM cart WHERE buyer = 'Tom';
  ```
- 最大值
  ```sql
  SELECT max(quantity) FROM cart WHERE buyer = 'Tom';
  ```
- 平均值
  ```sql
  SELECT avg(quantity) FROM cart;
  ```
- 最小值
  ```sql
  SELECT min(quantity) FROM cart;
  ```
- 总和
  ```sql
  SELECT sum(quantity) FROM cart;
  ```

### GROUP BY
```sql
SELECT color 颜色, count( *) 数量 FROM cars GROUP BY color;
```

### HAVING
```sql
SELECT color 颜色, count(*) 数量 FROM cars GROUP BY color HAVING 数量 > 1;
```
### 子查询
```sql
SELECT Product FROM cart WHERE quantity = (SELECT MAX(quantity) FROM cart);
```

### 多表联合查询
```sql
SELECT * FROM tb_student AS A, tb_score AS B WHERE A.sno = B.sno;
```

### JOIN
```sql
SELECT * FROM tb_student A INNER JOIN tb_score B ON A.sno = B.sno;
SELECT * FROM tb_student A LEFT OUTER JOIN tb_score B ON A.sno = B.sno;
SELECT * FROM tb_student A RIGHT OUTER JOIN tb_score B ON A.sno = B.sno;
```

### UNION
```sql
SELECT sname, sex FROM tb_student WHERE sex = 'female'
UNION
SELECT t_name, sex FROM tb_teacher WHERE sex = '女';
```

## 索引和视图

### 索引
- 显示索引
  ```sql
  SHOW INDEX FROM tb_student;
  ```
- 创建索引
  ```sql
  CREATE INDEX abc ON tb_student(sname);
  ```
- 删除索引
  ```sql
  DROP INDEX abc ON tb_student;
  ```

### 视图
- 创建视图
  ```sql
  CREATE VIEW student_view AS
  SELECT a.*, b.subject, b.score
  FROM tb_student a
  LEFT JOIN tb_score b ON a.sno = b.sno;
  ```

## 存储过程和事务

### 存储过程
- 创建存储过程
  ```sql
  DELIMITER **
  CREATE PROCEDURE myAvg(in id INT, out avg_score FLOAT)
  BEGIN
    SELECT AVG(b.score) INTO avg_score
    FROM tb_student a
    LEFT JOIN tb_score b ON a.sno = b.sno
    WHERE a.sno = id;
  END**
  ```
- 调用存储过程
  ```sql
  CALL myAvg(1, @avg1);
  SELECT @avg1;
  ```
- 删除存储过程
  ```sql
  DROP PROCEDURE myAvg;
  ```

### 事务操作
```sql
DELIMITER **
CREATE PROCEDURE update_accounts()
BEGIN
  START TRANSACTION;
  UPDATE accounts SET balance = balance - 100.00 WHERE account_id = 'a';
  DECLARE aaa DECIMAL(10,2);
  SELECT balance INTO aaa FROM accounts WHERE account_id = 'a';
  IF aaa <= 0 THEN
    ROLLBACK;
  ELSE
    UPDATE accounts SET balance = balance + 100.00 WHERE account_id = 'b';
    COMMIT;
  END IF;
END**
DELIMITER ;
```

### 用户权限管理
- 授权
  ```sql
  GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' IDENTIFIED BY '123456' WITH GRANT OPTION;
  FLUSH PRIVILEGES;
  ```

## 数据库引擎及SQL分类

### 数据库引擎
- **InnoDB**：支持事务，行级锁定，外键。
- **MyISAM**：不支持事务，支持全文搜索。
- **Memory**：数据存储在内存中，快速访问。
- **Archive**：数据压缩，适用于历史数据存储。
- **Federated**：访问远程数据库服务器上的表。

### SQL分类
- **DDL**：数据定义语言，如`CREATE TABLE`, `ALTER TABLE`。
- **DML**：数据操纵语言，如`SELECT`, `INSERT`, `UPDATE`, `DELETE`。
- **DCL**：数据控制语言，如`GRANT`, `REVOKE`。
- **TCL**：事务控制语言，如`BEGIN TRANSACTION`, `COMMIT`, `ROLLBACK`。
- **DQL**：数据查询语言，主要是`SELECT`语句。

# 参考资料
- MySQL官方文档
- SQL教程
- 数据库设计指南
- MySQL性能优化手册

# 相关笔记
- [[MySQL]]
- [[数据库]]
- [[SQL]]
- [[数据库设计]]
- [[数据库优化]]
- [[后端开发]]
