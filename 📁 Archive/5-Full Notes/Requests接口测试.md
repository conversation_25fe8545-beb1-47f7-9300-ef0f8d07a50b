---
created: 2024-12-12
aliases: []
---

状态: [[进行中]]

标签: [[learnpython]] [[HTTP]] [[接口测试]] [[Requests]] [[API]] [[网络编程]] [[Session]] [[<PERSON><PERSON>]] [[异常处理]] [[JSON]]

# Requests 库学习笔记

## 1. 简介
- Python 最流行的 HTTP 请求库
- 简单直观的 API 接口
- 支持多种 HTTP 请求方法
- 自动处理常见的 HTTP 任务

## 2. 环境准备
### 2.1 安装
pip install requests

### 2.2 导入
import requests

## 3. 基础用法
### 3.1 HTTP 请求方法
- GET: requests.get(url)
- POST: requests.post(url)
- PUT: requests.put(url)
- DELETE: requests.delete(url)
- HEAD: requests.head(url)
- OPTIONS: requests.options(url)

### 3.2 请求参数传递
#### 3.2.1 URL 参数 (params)
params = {'key1': 'value1', 'key2': 'value2'}
response = requests.get(url, params=params)

#### 3.2.2 表单数据 (data)
data = {'username': 'test', 'password': '123456'}
response = requests.post(url, data=data)

#### 3.2.3 JSON 数据 (json)
json_data = {'name': 'test', 'age': 25}
response = requests.post(url, json=json_data)

## 4. 高级特性
### 4.1 请求定制
#### 4.1.1 请求头
headers = {
    'User-Agent': 'Mozilla/5.0',
    'Content-Type': 'application/json',
    'Authorization': 'Bearer token123'
}
response = requests.get(url, headers=headers)

#### 4.1.2 Cookie
cookies = {'session_id': 'abc123'}
response = requests.get(url, cookies=cookies)

#### 4.1.3 超时设置
response = requests.get(url, timeout=5)

### 4.2 响应处理
#### 4.2.1 状态码
response.status_code  # 200, 404, 500 等

#### 4.2.2 响应内容
- 文本: response.text
- 二进制: response.content
- JSON: response.json()
- 响应头: response.headers
- Cookie: response.cookies

### 4.3 会话管理
session = requests.Session()
session.headers.update({'Authorization': 'Bearer token123'})
response = session.get(url)

## 5. 异常处理
### 5.1 常见异常
- ConnectionError: 连接错误
- Timeout: 超时错误
- RequestException: 请求异常基类
- HTTPError: HTTP 错误

### 5.2 异常处理示例
try:
    response = requests.get(url)
    response.raise_for_status()
except requests.exceptions.RequestException as e:
    print(f"请求发生错误: {e}")

## 6. 最佳实践
### 6.1 性能优化
- 使用会话对象处理多个请求
- 设置合理的超时时间
- 及时关闭响应和会话对象

### 6.2 安全建议
- 使用 HTTPS
- 不在代码中硬编码敏感信息
- 验证服务器证书
- 使用环境变量存储密钥

### 6.3 代码规范
- 使用 with 语句管理会话
- 总是处理异常
- 使用有意义的变量名
- 添加适当的注释和文档

# 参考资料
- Requests官方文档
- Python HTTP库指南
- HTTP协议规范
- Python编程手册

# 相关笔记
- [[learnpython]]
- [[HTTP]]
- [[接口测试]]
- [[API]]
- [[网络编程]]

