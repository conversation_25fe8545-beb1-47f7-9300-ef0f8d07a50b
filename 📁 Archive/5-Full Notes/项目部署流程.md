---
created: 2024-10-28
aliases: []
---

状态: [[进行中]]

标签: [[Linux]] [[项目部署]] [[服务配置]] [[Shell命令]] [[3-Tags/Java]] [[MySQL]] [[Redis]] [[RabbitMQ]] [[MinIO]] [[Nginx]]

# Linux项目部署流程与服务检查

## 1.启动各项服务
### 检查java服务是否正常
#### java -version
### 启动redis服务
#### 检查redis服务是否正常  systemctl  status rdis
### 检查MySQL服务
#### systemctl  status  mysqld
### 检查minio服务
#### systemctl status minio 
#### 若未启动，则使用/opt/minio/minio server /var /lib/minio
#### 将其运行到后台  nohup /opt/minio/minio server /var/lib/minio > /var/log/minio.1og &
### 检查rabbitmq服务
#### systemctl status rabbitmq-server
#### 运行之后在浏览器验证是否正常
### 查看ningx是否在运行 systemctl status nginx
## 2.设置配置文件
### 永久关闭SELinux
#### 修改/etc/selinux/config 文件
##### SELINUx=enforcing  改为SELINUX=disabled
选
## 3.将 SQL，JAR,配置文件上传
### 使用moba将文件传到/opt/animal
### 配置 application-test.properties文件，将密码都修改成之前配置的以及mysql文件的名字
### 在navicat中使用ip链接数据库，新建数据库database，运行sql文件
## 4.启动项目
使用 java -jar - Dspring.config.1ocation=/opt/animalExperiment/application-test.properties
opt/animalExperiment/animalExperiment.iar命令
## 5.过程中出现错误，正在解决
## 学到的新内容
## sed ：查找某个字符换成另外的字符
### 使用这个命令以及正则表达式进行匹配以及管道符 |    . 通过echo将文件打印，通过管道符 | 后sed 
### echo this is a test line | sed 's/\w/[$]/g'  作用是将每个字符都加上【】，将每个字符输出给$ ,规则是给$两边加【】
### **命令格式**
#### sed [options] 'command' file(s)
####  s 替换文本 中的字符串 sed 's/book/books/' file

# 参考资料
- Linux服务管理手册
- Shell命令参考
- 项目部署文档

# 相关笔记
- [[linux全部笔记]]
- [[Linux环境下的Java项目部署]]
- [[Linux中间件服务安装与配置]]
- [[MySQL学习笔记]]


