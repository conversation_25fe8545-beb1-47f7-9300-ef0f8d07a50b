---
created: {{date}} {{time}}
type: directory-guide
tags: [[标签]] [[分类]] [[索引]]
---

# 标签体系说明

## 技术领域分类

### 1. 测试技术
- 测试类型
  - [[功能测试]] - 功能验证和测试
  - [[自动化测试]] - 自动化测试实践
  - [[性能测试]] - 性能评估和优化
  - [[接口测试]] - API和接口测试
  - [[UI测试]] - 界面测试
  - [[移动测试]] - 移动应用测试

- 测试工具
  - [[Selenium]] - Web自动化测试
  - [[Pytest]] - Python测试框架
  - [[Postman]] - API测试工具
  - [[Appium]] - 移动端测试
  - [[JMeter]] - 性能测试工具

- 测试理论
  - [[测试理论]] - 基础概念和方法论
  - [[测试流程]] - 标准测试流程
  - [[测试用例]] - 用例设计方法
  - [[测试计划]] - 测试规划和管理

### 2. 开发技术
- 编程语言
  - [[Python]] - Python编程
  - [[3-Tags/Java]] - Java开发
  - [[Shell]] - Shell脚本
  - [[HTML]] - 网页开发

- 数据存储
  - [[MySQL]] - MySQL数据库
  - [[Redis]] - 缓存数据库
  - [[数据库]] - 数据库理论
  - [[SQL]] - SQL语言

- 中间件
  - [[RabbitMQ]] - 消息队列
  - [[MinIO]] - 对象存储
  - [[Nginx]] - Web服务器

### 3. 运维技术
- 系统管理
  - [[Linux]] - Linux系统
  - [[命令行]] - 命令行操作
  - [[Shell命令]] - Shell命令集

- 容器技术
  - [[Docker]] - 容器化
  - [[Kubernetes]] - 容器编排
  - [[容器化]] - 容器相关技术

## 使用规范

### 标签命名规则
1. 使用有意义的名称
2. 保持命名一致性
3. 避免重复创建

### 标签使用建议
1. 相关标签组合使用
2. 适当数量，避免过多
3. 定期检查和更新

### 维护原则
1. 定期清理无效标签
2. 合并相似标签
3. 更新标签关系

## 注意事项
1. 标签命名要简洁明确
2. 避免过度使用标签
3. 保持标签体系的一致性
4. 定期整理和优化标签