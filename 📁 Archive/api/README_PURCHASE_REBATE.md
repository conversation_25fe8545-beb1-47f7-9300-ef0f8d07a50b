# 🎯 固定金额三级返佣系统

## 📖 概述

本系统为加密货币交易平台新增了**固定金额三级返佣**功能，支持用户在进行有效购买行为时，自动为其推荐人分发固定金额的返佣奖励。

### 🌟 核心特性

- **🎯 固定金额返佣**: 三级返佣分别为 10 USDT、5 USDT、2 USDT
- **🚀 自动触发**: 支持充值、理财、挖矿等多种购买行为自动触发
- **⚙️ 灵活配置**: 支持返佣金额、购买类型、门槛等参数配置
- **📊 统计完善**: 提供详细的返佣统计和查询功能
- **🔐 安全可靠**: 事务保证，异常处理完善
- **🔄 向后兼容**: 不影响现有返佣功能

## 🏗️ 系统架构

### 返佣层级设计

```
购买用户 (User C)
    ↓ 产生有效购买
┌─────────────────┐
│  A级 (直接推荐)  │ ← 获得 10 USDT
├─────────────────┤
│  B级 (间接推荐)  │ ← 获得 5 USDT  
├─────────────────┤
│  C级 (三级推荐)  │ ← 获得 2 USDT
└─────────────────┘
```

### 有效购买类型

| 类型代码 | 购买类型 | 默认启用 | 触发时机 |
|---------|----------|----------|----------|
| 1 | 充值 | ✅ | 充值审核通过后 |
| 6 | 理财购买 | ✅ | 理财订单审核通过后 |
| 12 | 挖矿购买 | ✅ | 挖矿订单创建成功后 |
| 22 | 币币交易 | ❌ | 交易完成后 |
| 27 | 合约交易 | ❌ | 开仓成功后 |
| 52 | 新币申购 | ✅ | 申购成功后 |

## 🚀 快速开始

### 1. 数据库更新

执行数据库更新脚本：

```sql
-- 执行项目根目录下的SQL脚本
source sql/purchase_rebate_update.sql;
```

### 2. 启用返佣功能

通过管理员账号登录后台，进行如下配置：

```bash
# 1. 访问系统设置
GET /setting/get/PURCHASE_REBATE_SETTING

# 2. 修改配置启用功能
PUT /setting/put/PURCHASE_REBATE_SETTING
{
  "isOpen": true,
  "oneAmount": 10,
  "twoAmount": 5,
  "threeAmount": 2,
  "minPurchaseAmount": 10,
  "validPurchaseTypes": [1, 6, 12, 52],
  "rebateCoinType": "USDT",
  "remark": "固定金额三级返佣系统"
}
```

### 3. 验证功能

进行一笔有效购买，检查返佣是否正常发放：

```bash
# 查看返佣统计
GET /bussiness/purchaseRebate/statistics?userId=1001

# 查看返佣明细
GET /bussiness/purchaseRebate/details?type=3&userId=1001
```

## 📚 API 接口文档

### 配置管理接口

#### 1. 获取返佣配置

```http
GET /setting/get/PURCHASE_REBATE_SETTING
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "isOpen": true,
    "oneAmount": 10,
    "twoAmount": 5,
    "threeAmount": 2,
    "minPurchaseAmount": 10,
    "validPurchaseTypes": [1, 6, 12, 52],
    "rebateCoinType": "USDT",
    "remark": "固定金额三级返佣：一级10USDT，二级5USDT，三级2USDT"
  }
}
```

#### 2. 保存返佣配置

```http
PUT /setting/put/PURCHASE_REBATE_SETTING
Content-Type: application/json

{
  "isOpen": true,
  "oneAmount": 15,
  "twoAmount": 8,
  "threeAmount": 3,
  "minPurchaseAmount": 50,
  "validPurchaseTypes": [1, 6, 12],
  "rebateCoinType": "USDT",
  "remark": "自定义返佣配置"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功"
}
```

### 返佣管理接口

#### 1. 获取返佣配置

```http
GET /bussiness/purchaseRebate/config
```

**权限要求：** `bussiness:purchaseRebate:query`

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "isOpen": true,
    "oneAmount": 10,
    "twoAmount": 5,
    "threeAmount": 2,
    "minPurchaseAmount": 10,
    "validPurchaseTypes": [1, 6, 12, 52],
    "rebateCoinType": "USDT",
    "remark": "固定金额三级返佣"
  }
}
```

#### 2. 保存返佣配置

```http
PUT /bussiness/purchaseRebate/config
Content-Type: application/json

{
  "isOpen": true,
  "oneAmount": 10,
  "twoAmount": 5,
  "threeAmount": 2,
  "minPurchaseAmount": 10,
  "validPurchaseTypes": [1, 6, 12, 52],
  "rebateCoinType": "USDT",
  "remark": "固定金额三级返佣"
}
```

**权限要求：** `bussiness:purchaseRebate:edit`

#### 3. 获取购买类型枚举

```http
GET /bussiness/purchaseRebate/purchaseTypes
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "all": [1, 6, 12, 22, 27, 52],
    "defaultEnabled": [1, 6, 12, 52],
    "typeMap": {
      "1": "充值",
      "6": "理财购买", 
      "12": "挖矿购买",
      "22": "币币交易",
      "27": "合约交易",
      "52": "新币申购"
    }
  }
}
```

#### 4. 测试返佣计算

```http
POST /bussiness/purchaseRebate/test?userId=1001&purchaseAmount=100&purchaseType=1
```

**权限要求：** `bussiness:purchaseRebate:test`

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "userId": 1001,
    "purchaseAmount": "100",
    "purchaseType": 1,
    "purchaseTypeDesc": "充值",
    "isValidPurchase": true,
    "levelOneRebate": 10,
    "levelTwoRebate": 5,
    "levelThreeRebate": 2
  }
}
```

#### 5. 重置为默认配置

```http
POST /bussiness/purchaseRebate/resetDefault
```

**权限要求：** `bussiness:purchaseRebate:edit`

#### 6. 获取配置状态

```http
GET /bussiness/purchaseRebate/status
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "isEnabled": true,
    "configExists": true,
    "validPurchaseTypes": [1, 6, 12, 52],
    "minPurchaseAmount": 10
  }
}
```

### 统计查询接口

#### 1. 查询返佣统计信息

```http
GET /bussiness/purchaseRebate/statistics?userId=1001
```

**权限要求：** `bussiness:purchaseRebate:query`

**参数说明：**
- `userId` (可选): 指定用户ID，不传则查询全部

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "rechargeRebate": {
      "oneCount": 5,
      "twoCount": 3,
      "threeCount": 1,
      "sumCount": 9,
      "sumAmount": 45.50
    },
    "miningRebate": {
      "oneCount": 2,
      "twoCount": 1,
      "threeCount": 0,
      "sumCount": 3,
      "sumAmount": 15.00
    },
    "purchaseRebate": {
      "oneCount": 8,
      "twoCount": 5,
      "threeCount": 2,
      "totalCount": 15,
      "totalAmount": 104.00,
      "type": "购买返佣"
    },
    "summary": {
      "totalCount": 27,
      "totalAmount": 164.50
    }
  }
}
```

#### 2. 查询返佣明细列表

```http
GET /bussiness/purchaseRebate/details?pageNum=1&pageSize=10&userId=1001
```

**权限要求：** `bussiness:purchaseRebate:list`

**参数说明：**
- `pageNum`: 页码（默认1）
- `pageSize`: 每页数量（默认10）
- `userId` (可选): 用户ID
- `type`: 返佣类型（自动设置为3）
- `status` (可选): 返佣状态
- `beginTime` (可选): 开始时间
- `endTime` (可选): 结束时间

**响应示例：**
```json
{
  "total": 25,
  "rows": [
    {
      "id": "12345",
      "type": 3,
      "level": 1,
      "amount": 10.00,
      "coinType": "USDT",
      "fromId": 1002,
      "userId": 1001,
      "status": 2,
      "loginName": "user001",
      "serialId": "PURCHASE_20250104_001",
      "createTime": "2025-01-04 15:30:25",
      "fromUserName": "user002",
      "statusText": "已返",
      "levelText": "一级返佣"
    }
  ],
  "code": 200,
  "msg": "查询成功"
}
```

#### 3. 导出返佣明细

```http
POST /bussiness/purchaseRebate/export
Content-Type: application/json

{
  "userId": 1001,
  "beginTime": "2025-01-01",
  "endTime": "2025-01-31"
}
```

**权限要求：** `bussiness:purchaseRebate:export`

**响应：** Excel文件下载

## 🔧 配置参数说明

### PurchaseRebateSetting 配置类

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| isOpen | Boolean | ✅ | false | 返佣功能开关 |
| oneAmount | BigDecimal | ✅ | 10 | 一级返佣金额(USDT) |
| twoAmount | BigDecimal | ✅ | 5 | 二级返佣金额(USDT) |
| threeAmount | BigDecimal | ✅ | 2 | 三级返佣金额(USDT) |
| minPurchaseAmount | BigDecimal | ✅ | 10 | 最低购买门槛(USDT) |
| validPurchaseTypes | List<Integer> | ✅ | [1,6,12,52] | 有效购买类型列表 |
| rebateCoinType | String | ✅ | "USDT" | 返佣币种 |
| remark | String | ❌ | - | 配置备注说明 |

### 购买类型代码说明

```java
public enum PurchaseTypeEnum {
    RECHARGE(1, "充值", true),
    FINANCIAL_PURCHASE(6, "理财购买", true), 
    MINING_PURCHASE(12, "挖矿购买", true),
    CURRENCY_TRADING(22, "币币交易", false),
    CONTRACT_TRADING(27, "合约交易", false),
    NEW_COIN_PURCHASE(52, "新币申购", true);
}
```

### 返佣级别说明

```java
public enum PurchaseRebateLevelEnum {
    LEVEL_ONE(1, "一级返佣"),    // 直接推荐
    LEVEL_TWO(2, "二级返佣"),   // 间接推荐  
    LEVEL_THREE(3, "三级返佣"); // 三级推荐
}
```

## 💡 业务流程

### 返佣触发流程

```mermaid
graph TD
    A[用户完成购买] --> B{检查返佣开关}
    B -->|关闭| C[不进行返佣]
    B -->|开启| D{验证购买有效性}
    D -->|无效| C
    D -->|有效| E[解析推荐关系链]
    E --> F[计算三级返佣]
    F --> G[更新上级用户资产]
    G --> H[记录返佣明细]
    H --> I[返佣完成]
```

### 推荐关系解析

```
用户推荐关系链 (appParentIds): "1001,1000,999"
                                ↓
┌─────────────────────────────────────────┐
│  购买用户: 1002                          │
│  推荐链: "1001,1000,999"                │  
├─────────────────────────────────────────┤
│  一级推荐人: 1001 → 获得 10 USDT         │
│  二级推荐人: 1000 → 获得 5 USDT          │
│  三级推荐人: 999  → 获得 2 USDT          │
└─────────────────────────────────────────┘
```

## 🛠️ 开发者指南

### 自定义购买类型

如需添加新的购买类型，请按以下步骤操作：

1. **更新枚举类**

```java
// 在 PurchaseTypeEnum 中添加新类型
NEW_PRODUCT_PURCHASE(60, "新产品购买", true);
```

2. **添加业务触发点**

```java
// 在相应的业务Service中添加返佣调用
try {
    IPurchaseRebateService purchaseRebateService = SpringUtils.getBean(IPurchaseRebateService.class);
    if (purchaseRebateService != null) {
        purchaseRebateService.processPurchaseRebate(user, amount, 
                PurchaseTypeEnum.NEW_PRODUCT_PURCHASE.getCode(), serialId);
    }
} catch (Exception e) {
    log.error("购买返佣处理失败", e);
}
```

3. **更新配置**

将新类型代码添加到 `validPurchaseTypes` 配置中。

### 自定义返佣策略

如需实现不同的返佣策略，可以：

1. **继承 PurchaseRebateServiceImpl**

```java
@Service("customPurchaseRebateService")
public class CustomPurchaseRebateServiceImpl extends PurchaseRebateServiceImpl {
    
    @Override
    protected BigDecimal getRebateAmountByLevel(PurchaseRebateSetting config, 
                                               PurchaseRebateLevelEnum level, 
                                               BigDecimal purchaseAmount) {
        // 实现自定义返佣计算逻辑
        // 例如：基于购买金额的动态返佣
        return super.getRebateAmountByLevel(config, level);
    }
}
```

2. **配置使用自定义服务**

在需要使用自定义策略的地方注入自定义服务。

### 数据库扩展

如需添加更多返佣相关字段：

```sql
-- 添加返佣来源字段
ALTER TABLE t_agent_activity_info 
ADD COLUMN source_type VARCHAR(50) COMMENT '返佣来源类型';

-- 添加返佣批次字段  
ALTER TABLE t_agent_activity_info
ADD COLUMN batch_id VARCHAR(64) COMMENT '返佣批次ID';
```

## 📊 监控与运维

### 关键指标监控

1. **返佣成功率**
```sql
SELECT 
  DATE(create_time) as date,
  COUNT(*) as total_rebates,
  SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as success_rebates,
  ROUND(SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as success_rate
FROM t_agent_activity_info 
WHERE type = 3 
GROUP BY DATE(create_time)
ORDER BY date DESC;
```

2. **返佣金额统计**
```sql
SELECT 
  coin_type,
  level,
  COUNT(*) as rebate_count,
  SUM(amount) as total_amount,
  AVG(amount) as avg_amount
FROM t_agent_activity_info 
WHERE type = 3 AND status = 2
GROUP BY coin_type, level;
```

3. **异常返佣记录**
```sql
-- 查找状态异常的返佣记录
SELECT * FROM t_agent_activity_info 
WHERE type = 3 AND status = 1 
AND create_time < DATE_SUB(NOW(), INTERVAL 1 HOUR);
```

### 性能优化建议

1. **索引优化**
```sql
-- 为常用查询字段添加复合索引
CREATE INDEX idx_rebate_query ON t_agent_activity_info 
(type, user_id, status, create_time);

-- 为统计查询添加索引
CREATE INDEX idx_rebate_stats ON t_agent_activity_info 
(type, level, coin_type, create_time);
```

2. **批量处理**

对于大量返佣处理，建议使用批量插入：

```java
@Transactional
public void batchProcessRebates(List<RebateRequest> requests) {
    List<TAgentActivityInfo> rebateRecords = new ArrayList<>();
    
    for (RebateRequest request : requests) {
        // 批量构建返佣记录
        rebateRecords.addAll(buildRebateRecords(request));
    }
    
    // 批量插入
    agentActivityInfoService.saveBatch(rebateRecords);
}
```

## ⚠️ 注意事项

### 安全考虑

1. **防止重复返佣**
   - 使用 `serialId` 确保同一业务单据不会重复返佣
   - 建议在数据库层添加唯一约束

2. **资产安全**
   - 所有资产操作都在事务中进行
   - 建议添加资产变动日志记录

3. **权限控制**
   - 返佣配置修改需要高级管理员权限
   - 敏感操作需要操作日志记录

### 性能考虑

1. **大用户量优化**
   - 推荐关系链建议限制在10级以内
   - 考虑使用缓存存储热点用户的推荐关系

2. **高并发处理**
   - 返佣处理建议使用异步队列
   - 避免在关键业务流程中同步处理返佣

### 运维建议

1. **定期检查**
   - 定期检查返佣配置是否合理
   - 监控返佣金额是否异常

2. **备份策略**
   - 返佣配置变更前先备份
   - 重要返佣数据定期备份

## 🤝 技术支持

如果您在使用过程中遇到问题，请：

1. **查看日志**
   - 检查应用日志中的返佣相关错误信息
   - 关注 `PurchaseRebateServiceImpl` 的日志输出

2. **数据验证**
   - 验证推荐关系链数据是否正确
   - 检查用户资产变动记录

3. **联系开发团队**
   - 提供详细的错误信息和复现步骤
   - 附上相关的日志和数据

---

**最后更新时间**: 2025-01-04  
**版本**: v1.0.0  
**维护者**: 开发团队