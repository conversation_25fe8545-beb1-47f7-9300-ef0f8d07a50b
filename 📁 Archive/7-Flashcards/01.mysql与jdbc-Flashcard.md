# MySQL与JDBC闪卡

### SQL是什么？它有什么作用？
1. 基本概念：
   - SQL（Structured Query Language，结构化查询语言）
   - 用于访问和处理数据库的标准计算机语言
   - ANSI标准的计算机语言
   - 可以对数据库进行增删改查等操作

2. 主要作用：
   - 执行数据库查询
   - 获取数据
   - 插入记录
   - 更新记录
   - 删除记录
   - 创建新数据库
   - 创建新表
   - 创建存储过程
   - 设置表权限

### SQL语言有哪些主要特点？
1. 综合统一性：
   - 为不同数据库提供统一的命令
   - 支持多种数据库（MySQL、SQL Server、Oracle等）
   - 基本命令易学易用

2. 非过程化语言：
   - 只需描述要做什么，而不是怎么做
   - 关注结果而不是过程

3. 语言简捷：
   - 常用命令简单明了
   - 语法结构清晰，易于掌握

4. 集合操作性：
   - 可以处理数据集合
   - 支持批量数据处理

### MySQL有什么特点？
1. 技术特点：
   - 使用C/C++开发，保证可移植性
   - 支持多线程，充分利用CPU
   - 优化的SQL查询算法
   - 支持多种编程语言接口

2. 使用优势：
   - 开源免费（社区版）
   - 体积小，速度快
   - 跨平台（Windows、Linux、Mac OS）
   - 社区活跃，支持完善

### MySQL的数据类型有哪些？
1. 字符串类型：
   - CHAR(n)：固定长度字符串
   - VARCHAR(n)：可变长度字符串
   - TEXT：长文本数据

2. 数值类型：
   - INT/INTEGER：整数
   - FLOAT：单精度浮点数
   - DOUBLE：双精度浮点数
   - DECIMAL：精确小数

3. 日期时间类型：
   - DATE：日期
   - TIME：时间
   - DATETIME：日期和时间
   - TIMESTAMP：时间戳

### MySQL的约束有哪些？
1. 主键约束（PRIMARY KEY）：
   - 唯一标识表中的每条记录
   - 不能为空，必须唯一

2. 外键约束（FOREIGN KEY）：
   - 建立表之间的关系
   - 确保数据的一致性

3. 唯一约束（UNIQUE）：
   - 确保字段值唯一
   - 可以为空值

4. 非空约束（NOT NULL）：
   - 确保字段不能为空值

5. 默认值约束（DEFAULT）：
   - 设置字段的默认值

### JDBC是什么？它有什么作用？
1. 基本概念：
   - Java Database Connectivity
   - Java语言访问数据库的标准API
   - Sun公司提供的数据库访问规范

2. 主要作用：
   - 连接数据库
   - 发送SQL语句
   - 处理结果集
   - 提供跨数据库的统一接口

### JDBC连接数据库的步骤是什么？
1. 加载驱动：
   ```java
   Class.forName("com.mysql.cj.jdbc.Driver");
   ```

2. 建立连接：
   ```java
   String url = "***************************/数据库名";
   String username = "root";
   String password = "密码";
   Connection conn = DriverManager.getConnection(url, username, password);
   ```

3. 创建Statement：
   ```java
   Statement stmt = conn.createStatement();
   // 或者创建PreparedStatement
   PreparedStatement pstmt = conn.prepareStatement(sql);
   ```

4. 执行SQL：
   ```java
   // 执行查询
   ResultSet rs = stmt.executeQuery(sql);
   // 执行更新
   int rows = stmt.executeUpdate(sql);
   ```

5. 处理结果：
   ```java
   while(rs.next()) {
       // 获取数据
       rs.getString("列名");
       rs.getInt("列名");
   }
   ```

6. 释放资源：
   ```java
   rs.close();
   stmt.close();
   conn.close();
   ```

### PreparedStatement有什么优势？
1. 性能优势：
   - SQL预编译
   - 缓存执行计划
   - 批量处理效率高

2. 安全性：
   - 防止SQL注入
   - 自动转义特殊字符
   - 参数化查询

3. 使用方便：
   - 不需要拼接SQL
   - 自动处理类型转换
   - 代码可读性好

### JDBC中的事务处理如何实现？
1. 事���控制：
   ```java
   try {
       // 开启事务
       conn.setAutoCommit(false);
       
       // 执行SQL操作
       ...
       
       // 提交事务
       conn.commit();
   } catch (Exception e) {
       // 回滚事务
       conn.rollback();
   } finally {
       // 恢复自动提交
       conn.setAutoCommit(true);
   }
   ```

2. 事务特性（ACID）：
   - 原子性（Atomicity）
   - 一致性（Consistency）
   - 隔离性（Isolation）
   - 持久性（Durability）

3. 事务隔离级别：
   - READ_UNCOMMITTED：读未提交
   - READ_COMMITTED：读已提交
   - REPEATABLE_READ：可重复读
   - SERIALIZABLE：串行化
