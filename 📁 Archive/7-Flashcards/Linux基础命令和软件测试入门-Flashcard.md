# Linux基础命令和软件测试入门闪卡

### Linux的基本命令有哪些？
1. ls命令：
   - ls -lh：显示文件大小
   - ls -l：长格式显示
   - ls：显示当前目录内容

2. mkdir命令：
   - mkdir -p：创建目录并包括父目录
   - 用于创建新目录

3. 其他基本命令：
   - pwd：显示当前位置路径
   - cd：切换目录
   - rm：删除文件
   - echo：显示文本

### vi编辑器的基本使用方法是什么？
1. 基本操作：
   - vi +文件路径：创建或编辑文件
   - 包括创建文件
   - 编辑文件
   - 认识vi布局

2. 常用命令和按键：
   - 编辑模式
   - 命令模式
   - 保存退出

### 软件测试流程包括哪些步骤？
1. 前期准备：
   - 需求分析
   - 功能列表
   - 用户分类
   - 场景梳理

2. 测试执行：
   - 测试设计
   - 编码
   - 测试执行
   - 结果验证

3. 测试管理：
   - 部署
   - 运维
   - 制品仓库
   - 测试计划

### 如何编写测试用例？
1. 用例组成：
   - 用例标题描述
   - 模块
   - 测试数据
   - 前提条件
   - 测试���骤
   - 期望结果
   - 测试类型

2. 编写规则：
   - 步骤描述准确详细
   - 具有可操作性
   - 确保可以按步骤执行

### 什么是等价类和边界值测试？
1. 等价类：
   - 有效等价类
   - 无效等价类
   - 划分方法

2. 边界值：
   - 优先使用方法
   - 测试数据选择
   - 边界条件确定

### 敏捷开发流程是什么？
1. 适用范围：
   - 10人以内团队
   - 快速迭代开发

2. 流程步骤：
   - 需求分析
   - 设计
   - 编码
   - 单元测试
   - 打包
   - 迭代
   - 任务分配
   - 执行测试
   - 回归测试 