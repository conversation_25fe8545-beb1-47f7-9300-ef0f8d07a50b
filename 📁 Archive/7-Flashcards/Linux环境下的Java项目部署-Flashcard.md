# Linux环境下的Java项目部署闪卡

### Linux环境下如何安装JDK？
1. 上传JDK文件：
   - 使用mobaXterm工具
   - 使用scp命令
   - 将jdk.tar.gz上传到Linux系统

2. 安装步骤：
   - 进入/opt目录：cd /opt
   - 解压缩tar文件
   - 验证安装：java -version

### 如何修改Linux镜像源？
1. 修改步骤：
   - 进入目录：cd /etc/yum.repos.d
   - 使用cp命令备份仓库配置文件
   - 使用curl命令修改镜像源
   - 清理缓存：yum clean all
   - 重建缓存：yum makecache fast

### MySQL服务如何配置？
1. 服务管理：
   - 查看状态：systemctl status mysqld
   - 启动服务：systemctl start mysqld
   - 设置开机自启：systemctl enable mysqld

2. 密码设置：
   - 查看临时密码：grep "temporary password" /var/log/mysqld.log
   - 修改root密码：
     * mysql -u root -p
     * alter user root@localhost identified by "密码"
     * exit退出

3. 远程连接配置：
   - 授权命令：grant all privileges on *.* to root@'%' identified by "密码"
   - 刷新权限：flush privileges
   - 使用Navicat连接Linux主机

### 如何安装和配置Nginx？
1. 安装步骤：
   - 安装源：yum install epel-release
   - 安装nginx：yum install nginx
   - 启动服务：systemctl start nginx

2. 验证安装：
   - 在Windows浏览器中输入Linux主机IP地址
   - 查看Nginx欢迎页面

### HTML基础知识包括哪些？
1. 基本标签：
   - <!DOCTYPE html>：文档类型声明
   - 标签格式：<标签></标签>
   - /表示标签结束

### 项目部署中常见的错误有哪些？
1. 视频播放问题：
   - 刷新失败
   - 使用F12检查
   - 视频地址与存储路径不一致
   - 需要确保路径正确 