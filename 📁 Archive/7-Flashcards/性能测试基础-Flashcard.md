# 性能测试基础闪卡

### 什么是性能测试？它的目的是什么？
1. 性能测试的概念：
   - 通过特定方式对被测系统按照一定测试策略施加压力
   - 获取系统的响应时间、TPS、吞吐量、资源利用率等指标
   - 检测系统上线后能否满足用户需求的过程

2. 性能测试的目的：
   - 评估当前系统的性能
   - 预测系统以后的性能
   - 找到系统的瓶颈点
   - 进行调优优化

### 性能测试时如何确定并发量？
1. 有明确需求时：
   - 按照需求文档的要求测试
   - 例如：测试1000并发、1200并发、1500并发

2. 无明确需求时：
   - 评估当前系统性能状况
   - 类似系统体检，了解系统性能状况

### 什么是基准测试？如何进行？
1. 基准测试场景：
   - 产品之前没有做过性能测试
   - 在特定版本（如V1R1）上获取基线数据
   - 在新版本（如V1R2）上进行对比测试

2. 测试环境要求：
   - 使用相同的软件环境
   - 使用相同的硬件环境
   - 使用相同的网络环境

3. 资源使用监控：
   - CPU使用率
   - 内存使用率
   - 磁盘I/O
   - 网络I/O

4. 性能指标：
   - 不同系统有不同的性能指标
   - 例如：登录接口响应时间要求在3s以内 