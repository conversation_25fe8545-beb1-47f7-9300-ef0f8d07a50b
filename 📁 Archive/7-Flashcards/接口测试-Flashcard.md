# 接口测试闪卡

### 什么是接口测试？
1. 基本概念：
   - API (Application Programming Interface) 应用程序接口
   - 接口是连接前端和后端的纽带，用于传递数据
   - 从功能层面看，接口是一个黑盒子，接收参数并返回结果
   - 从代码层面看，接口就是一个函数/方法

2. 接口类型：
   - WebService接口（SOAP协议）
   - WebSocket接口（TCP/UDP）
   - HTTP接口（RESTful API）

### HTTP协议的基本特点是什么？
1. 协议特点：
   - 无连接：每次请求建立连接，请求结束断开连接
   - 无状态：没有记忆能力，不知道之前传过什么
   - 简单：只能客户端发起请求，服务器处理后响应

2. 请求报文结构：
   - 请求行：请求方法、URL、协议版本
   - 请求头：附加信息（key: value格式）
   - 空行
   - 请求体（可选）：POST等方法的数据

3. 响应报文结构：
   - 状态行：协议版本、状态码、状态描述
   - 响应头：附加信息
   - 空行
   - 响应体：返回的数据

### HTTP请求方法有哪些？GET和POST有什么区别？
1. 常用请求方法：
   - GET：获取资源
   - POST：提交数据
   - DELETE：删除资源
   - PUT：更新资源
   - PATCH：部分更新
   - HEAD：获取报文头

2. GET和POST的区别：
   - 数据长度：GET有限制，POST无限制
   - 数据类型：GET只支持ASCII，POST支持任意类型
   - 编码方式：GET只支持URL编码，POST支持多种编码
   - 缓存：GET会被缓存，POST不会
   - 安全性：GET参数在URL中可见，POST较安全
   - 书签：GET可以收藏为书签，POST不可以

### HTTP状态码有哪些分类？
1. 1xx（信息性状态码）：
   - 服务器收到请求，需要客户端继续执行操作

2. 2xx（成功状态码）：
   - 200 OK：请求成功

3. 3xx（重定向状态码）：
   - 301：永久重定向
   - 302：临时重定向

4. 4xx（客户端错误状态码）：
   - 400：请求语法错误
   - 404：资源不存在
   - 403：禁止访问

5. 5xx（服务器错误状态码）：
   - 500：服务器内部错误
   - 503：服务不可用
   - 504：网关超时

### Content-Type包含哪些常见类型？
1. 表单数据：
   - application/x-www-form-urlencoded
   - 格式：key=value&key=value

2. JSON数据：
   - application/json
   - 格式：{key:value,key:value}

3. 文件数据：
   - multipart/form-data
   - 用于上传文件

4. 其他类型：
   - text/plain：纯文本
   - text/html：HTML文本
   - image/png：PNG图片
   - image/jpeg：JPEG图片

### 如何识别和跟踪用户？
1. Cookie机制：
   - 定义：一段存储在客户端的文本信息
   - 属性：
     * 名称和值
     * 过期时间（expires/Max-Age）
     * 路径（path）
     * HttpOnly（防止XSS攻击）
     * secure（HTTPS传输）

2. Session机制：
   - 定义：服务器端的用户会话跟踪机制
   - 工作流程：
     * 服务器创建Session并生成ID
     * 通过Cookie返回SessionID
     * 客户端请求时携带SessionID

3. Token机制：
   - 组成：用户标识+时间戳+签名+盐值
   - 特点：
     * 服务器不保存状态
     * 通过算法验证有效性
     * 可以避免CSRF攻击

### HTTP和HTTPS有什么区别？
1. 安全性：
   - HTTP：明文传输，不安全
   - HTTPS：加密传输，更安全

2. 功能：
   - HTTPS提供：
     * 通信加密
     * 身份认证
     * 完整性保护

3. 其他区别：
   - 端口：HTTP用80，HTTPS用443
   - 性能：HTTPS需要加解密，消耗更多资源
   - 成本：HTTPS需要证书，成本更高

### Fiddler工具有什么用途？
1. 主要用途：
   - 抓取HTTP/HTTPS报文
   - 模拟弱网环境
   - ��取移动设备报文
   - 接口测试
   - 数据篡改

2. 使用场景：
   - 接口文档缺失时抓取接口
   - 定位前后端问题
   - 测试非法值
   - 性能测试

3. 断点操作：
   - 请求断点：bpu 接口地址
   - 响应断点：bpafter 接口地址
   - 取消断点：bpu 或 bpafter 