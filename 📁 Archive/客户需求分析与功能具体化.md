好的，没问题。我们已将“通过安全问题重置密码”这一机制纳入考量，并明确管理员不提供重置用户密码的功能。

以下是根据我们讨论的所有需求为您生成的正式版**产品需求文档（PRD）**。

---

## **音频授权播放平台 - 产品需求文档 (PRD)**

| **文档版本** | **修订日期**   | **修订人** | **修订说明**                                         |
| :----------- | :------------- | :--------- | :--------------------------------------------------- |
| V1.0         | 2025年07月29日 | Gemini     | 初始版本创建，包含用户端、管理端核心功能及技术方案。 |

### **1. 项目概述**

#### **1.1. 项目背景**
为了满足特定内容的版权保护和授权分发需求，客户需要开发一个封闭的音频播放平台。该平台允许管理员上传和管理音频及其字幕内容，并能够将这些内容在指定有效期内授权给特定用户进行播放。

#### **1.2. 项目目标**
*   **用户端（Android）：** 为终端用户提供一个稳定、流畅的音频播放体验，确保其只能访问被授权的内容。
*   **管理端（Web）：** 为管理员提供一个高效、直观的内容管理和用户授权后台。
*   **服务端：** 构建一个安全、可靠、易于部署和维护的后端服务，支撑前后端的功能需求。

#### **1.3. 目标用户**
*   **普通用户：** 需要通过授权来获取特定音频学习资料或内容的终端使用者。
*   **管理员：** 内容提供方或平台的管理者，负责上传内容并管理用户权限。

---

### **2. 功能需求详述 (Functional Requirements)**

#### **2.1. 用户端 (Android - Kotlin)**

**2.1.1. 用户模块**
*   **功能ID：** USR-001
*   **功能名称：** 用户注册
*   **功能描述：** 新用户可以通过设定用户名、密码和安全问题来进行注册。
*   **业务规则：**
    1.  用户需要提供一个唯一的**用户名**。
    2.  密码需要输入两次以确认，并满足一定的复杂度要求（例如：长度不少于6位）。
    3.  用户必须从预设的问题列表中选择并回答 **2-3个安全问题**，该答案将用于密码找回。
    4.  服务器需要对密码和安全问题答案进行加密存储。

*   **功能ID：** USR-002
*   **功能名称：** 用户登录
*   **功能描述：** 已注册用户通过输入用户名和密码登录应用。
*   **业务规则：**
    1.  登录成功后，系统需生成一个有时效性的令牌（Token）返回给客户端，用于后续接口的身份验证。
    2.  登录失败时，应给予明确的错误提示（如“用户名或密码错误”）。

*   **功能ID：** USR-003
*   **功能名称：** 忘记密码/重置密码
*   **功能描述：** 用户如果忘记密码，可以通过回答注册时设置的安全问题来重置密码。
*   **业务规则：**
    1.  用户在登录页面点击“忘记密码”。
    2.  输入需要重置密码的用户名。
    3.  系统从数据库中调取该用户设置的安全问题并展示给用户。
    4.  用户需要正确回答所有安全问题。
    5.  全部验证通过后，用户可以设置新的密码。
    6.  新密码设置成功后，跳转至登录页，用户可使用新密码登录。

**2.1.2. 音频播放模块**
*   **功能ID：** AUD-001
*   **功能名称：** 授权音频列表
*   **功能描述：** 用户登录后，主界面以列表形式展示所有已被管理员授权且在有效期内的音频。
*   **业务规则：**
    1.  每个列表项应显示音频标题和剩余有效时长（例如：“剩余3天”、“已过期”）。
    2.  已过期的音频应置灰或标记，并且不可点击播放。
    3.  支持下拉刷新，获取最新的授权列表。

*   **功能ID：** AUD-002
*   **功能名称：** 音频播放与字幕
*   **功能描述：** 用户点击音频列表中的项目，进入播放器页面进行播放。
*   **业务规则：**
    1.  播放器界面包含：音频标题、播放/暂停按钮、可拖动的播放进度条、音频总时长与当前播放时间、上一首/下一首切换按钮。
    2.  播放器下方需有专门区域用于显示字幕。
    3.  字幕应与音频播放进度保持同步高亮显示。

*   **功能ID：** AUD-003
*   **功能名称：** 音频搜索
*   **功能描述：** 提供搜索功能，允许用户在已授权的音频中进行快速查找。
*   **业务规则：**
    1.  用户可以根据**音频标题**或**字幕内容**中的关键词进行搜索。
    2.  搜索结果以列表形式展示匹配的音频。

**2.1.3. 个人中心模块**
*   **功能ID：** PRF-001
*   **功能名称：** 个人信息展示
*   **功能描述：** 展示用户的基本信息。
*   **业务规则：**
    1.  显示用户的头像（可使用默认头像）、昵称（初期可与用户名一致）。
    2.  提供“退出登录”功能。

#### **2.2. 管理员端 (Web - Vue.js)**

**2.2.1. 基础模块**
*   **功能ID：** ADM-001
*   **功能名称：** 管理员登录
*   **功能描述：** 管理员通过专用的账号和密码登录后台管理系统。
*   **业务规则：** 管理员账号由系统预置，不提供注册入口。

**2.2.2. 用户管理模块**
*   **功能ID：** MNG-USR-001
*   **功能名称：** 用户列表查看
*   **功能描述：** 管理员可以查看所有已注册的用户列表。
*   **业务规则：**
    1.  列表以分页形式展示，包含用户名、注册时间等信息。
    2.  支持按用户名进行搜索。

*   **功能ID：** MNG-USR-002
*   **功能名称：** 用户授权管理
*   **功能描述：** 管理员可以为指定用户授权一个或多个音频，并设定有效期。
*   **业务规则：**
    1.  在用户详情页，可以勾选需要授权给该用户的音频。
    2.  可以为每一次授权行为设置一个独立的**到期时间**。
    3.  可以查看用户当前已拥有的音频及其到期时间。
    4.  可以取消用户的音频授权或修改授权到期时间。
    5.  **明确：管理员不提供为用户重置密码的功能。**

**2.2.3. 音频管理模块**
*   **功能ID：** MNG-AUD-001
*   **功能名称：** 音频与字幕上传
*   **功能描述：** 管理员可以上传音频文件和与之配套的字幕文件。
*   **业务规则：**
    1.  支持上传 `.mp3`, `.aac` 格式的音频文件。
    2.  支持上传 `.srt`, `.lrc` 格式的字幕文件。
    3.  上传时需要填写**音频标题**。
    4.  音频和其字幕文件需要进行关联。

*   **功能ID：** MNG-AUD-002
*   **功能名称：** 音频信息管理
*   **功能描述：** 管理员可以查看、编辑和删除已上传的音频。
*   **业务规则：**
    1.  以列表形式展示所有已上传的音频，包含标题、上传时间等。
    2.  可以修改音频的标题。
    3.  可以替换或删除某个音频的字幕文件。
    4.  可以删除整个音频及其关联的字幕和用户授权记录。

**2.2.4. 仪表盘模块**
*   **功能ID：** DSH-001
*   **功能名称：** 数据统计
*   **功能描述：** 在后台首页提供关键业务数据的概览。
*   **业务规则：**
    1.  显示核心数据卡片：总用户数、总音频数、今日活跃用户数等。

---

### **3. 后端及技术规格**

#### **3.1. 技术栈**
*   **后端框架：** Go + Gin
*   **数据库：** MySQL
*   **用户端开发：** Android - Kotlin
*   **管理端开发：** Web - Vue.js (使用 Ant Design Vue 组件库)
*   **Android API请求库：** Ktor
*   **Web API请求库：** Axios

#### **3.2. API 接口清单 (摘要)**

| 方法   | 路径                          | 描述                                 |
| :----- | :---------------------------- | :----------------------------------- |
| POST   | `/api/register`               | 用户注册                             |
| POST   | `/api/login`                  | 用户登录                             |
| POST   | `/api/password/forgot`        | 忘记密码（提交用户名，获取安全问题） |
| POST   | `/api/password/reset`         | 重置密码（验证答案并设置新密码）     |
| GET    | `/api/user/profile`           | 获取用户个人信息                     |
| GET    | `/api/audios`                 | 获取用户已授权的音频列表             |
| GET    | `/api/audios/{id}`            | 获取单个音频信息（含播放和字幕地址） |
| POST   | `/api/search/audios`          | 搜索音频                             |
| POST   | `/admin/login`                | 管理员登录                           |
| GET    | `/admin/users`                | 获取用户列表                         |
| POST   | `/admin/users/{userId}/grant` | 为用户授权音频                       |
| POST   | `/admin/audios/upload`        | 上传音频和字幕                       |
| PUT    | `/admin/audios/{id}`          | 更新音频信息                         |
| DELETE | `/admin/audios/{id}`          | 删除音频                             |

#### **3.3. 数据库表结构设计 (核心)**

1.  **用户表 (users)**
    *   `id` (主键)
    *   `username` (唯一)
    *   `password_hash`
    *   `security_question_1`
    *   `security_answer_1_hash`
    *   `security_question_2`
    *   `security_answer_2_hash`
    *   `created_at`

2.  **音频表 (audios)**
    *   `id` (主键)
    *   `title` (标题)
    *   `audio_file_path` (音频文件存储路径)
    *   `subtitle_file_path` (字幕文件存储路径)
    *   `created_at`

3.  **用户音频授权表 (user_audio_permissions)**
    *   `id` (主键)
    *   `user_id` (外键, 关联 users.id)
    *   `audio_id` (外键, 关联 audios.id)
    *   `expires_at` (授权到期时间)
    *   `created_at`

#### **3.4. 文件存储**
*   **方案：** 音频和字幕文件存储在服务器的本地文件系统。
*   **路径：** 后端服务提供一个静态文件访问接口，用于客户端获取文件。例如：`/uploads/audios/` 和 `/uploads/subtitles/`。

---

### **4. 非功能性需求**

*   **性能：** API 接口响应时间应在200ms以内。音频加载应流畅，无明显卡顿。
*   **安全：**
    *   所有用户密码和安全问题答案必须经过哈希加盐处理后才能存入数据库。
    *   前后端通信需使用 HTTPS 协议。
    *   所有需要授权的 API 必须进行 Token 验证。
*   **部署：** 后端应用应编译为单个二进制文件，以实现快速、便捷的服务器部署。

---
这份文档已经包含了您新增的需求，并对所有功能和技术细节进行了正式的阐述。您可以将其作为项目开发的蓝图和与客户沟通的依据。