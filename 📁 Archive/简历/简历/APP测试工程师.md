# APP测试工程师简历

## 个人信息
- 姓名：路辉
- 年龄：23岁
- 电话：15393320330
- 邮箱：<EMAIL>
- 现居地：西安

## 求职意向
- 岗位：APP测试工程师
- 工作地点：西安
- 到岗时间：1~2周以内

## 教育背景
- 学历：本科
- 专业：计算机相关专业
- 毕业时间：2024年6月
- 主修课程：软件测试、移动应用开发、数据库系统、软件工程

## 专业技能
- 掌握移动应用测试理论，熟悉测试用例设计方法
- 掌握黑盒测试方法，包括等价类、边界值、场景法等测试方法
- 掌握Python + Appium自动化测试框架的使用
- 掌握adb工具，能够进行应用安装、调试、日志分析
- 掌握Monkey测试工具，能够进行稳定性测试
- 熟练使用Charles、Fiddler抓包工具，能够分析网络请求
- 熟练使用JMeter进行性能测试
- 熟悉Android/iOS应用测试流程
- 熟悉移动应用性能测试指标，如启动时间、内存占用等
- 了解Linux基本命令，能够进行基础的系统操作

## 项目经验
### 社交APP测试项目
- 参与社交APP的功能测试和性能测试
- 编写测试用例，覆盖用户注册、登录、社交功能等
- 使用Appium+Python编写自动化测试脚本
- 进行兼容性测试，测试主流机型适配情况
- 使用性能测试工具进行性能分析

### 移动端自动化测试框架开发
- 参与Python+Appium自动化测试框架的开发
- 实现多设备测试功能
- 开发性能数据采集功能
- 实现测试脚本录制功能
- 编写框架使用说明文档

## 自我评价
- 热爱移动测试领域，具备扎实的专业知识
- 具有较强的学习能力，善于解决问题
- 工作认真细致，有责任心
- 良好的团队协作能力，能够有效沟通
- 积极主动，能够快速适应新环境 

