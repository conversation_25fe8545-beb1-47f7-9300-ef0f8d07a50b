# Palette
## 银行测试工程师

![照片](照片.png)

---
**Phone**: ************  
**Email**: <EMAIL>  
**Address**: 北京市朝阳区xxx街道  
**Date of Birth**: 1995/01/01  
**Nationality**: 中国  

---
## OBJECTIVE
我是一名银行测试工程师，专注于金融系统测试领域。擅长业务功能测试、性能测试和安全测试，熟悉银行业务流程和金融系统架构，致力于保障金融系统的稳定性和安全性。

## EDUCATION
### 金融信息工程
**某某大学** | 2013.09 - 2017.06  
- 专业课程：金融学、信息系统、数据库、计算机网络
- 学习成绩：专业前15%
- GPA：3.7/4.0

## EXPERIENCE

### 银行测试工程师
**某某金融科技公司** | 2020.03 - 至今

负责应用监控管理平台（HertzBeat）的测试工作：
- 设计并执行金融系统测试用例，包括功能测试和性能测试
- 使用LoadRunner等工具进行压力测试和性能评估
- 进行系统监控和告警测试
- 编写测试文档，包括测试计划、测试报告等

### 金融系统测试工程师
**某某银行** | 2017.07 - 2020.02

负责分布式消息处理平台（RocketMQ）的测试工作：
- 制定金融交易系统测试策略和测试计划
- 执行功能测试、性能测试和安全测试
- 进行系统集成测试和验收测试
- 分析系统性能瓶颈，提供优化建议

## SKILLS

### 测试技能
- 熟练掌握金融系统测试方法和技术
- 精通性能测试和安全测试
- 熟练使用LoadRunner、JMeter等测试工具
- 掌握自动化测试框架和工具

### 开发技能
- 熟悉Java、Python编程语言
- 了解Spring Cloud微服务架构
- 熟悉Oracle、MySQL数据库
- 掌握Linux常用命令

### 其他技能
- 熟悉银行业务流程和规范
- 良好的文档编写能力
- 优秀的沟通协作能力
- 较强的问题分析和解决能力

## LANGUAGE
- 英语：CET-6，良好的读写能力
- 中文：母语 