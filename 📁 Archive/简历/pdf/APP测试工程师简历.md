# Palette
## APP测试工程师

![照片](照片.png)

---
**Phone**: ************  
**Email**: <EMAIL>  
**Address**: 北京市朝阳区xxx街道  
**Date of Birth**: 1995/01/01  
**Nationality**: 中国  

---
## OBJECTIVE
我是一名APP测试工程师，专注于移动应用测试领域。擅长功能测试、兼容性测试和性能测试，具有丰富的自动化测试经验，致力于提供优质的移动应用测试解决方案。

## EDUCATION
### 软件工程
**某某大学** | 2013.09 - 2017.06  
- 专业课程：移动应用开发、软件测试、软件工程、数据结构
- 学习成绩：专业前15%
- GPA：3.7/4.0

## EXPERIENCE

### APP测试工程师
**某某科技有限公司** | 2020.03 - 至今

负责Android视频播放器（GSYVideoPlayer）的测试工作：
- 设计并执行移动应用测试用例，包括功能测试和兼容性测试
- 使用Appium、UIAutomator等工具进行自动化测试
- 进行性能测试，包括内存、CPU、电量消耗等
- 编写测试报告，跟踪缺陷修复进度

### 移动测试工程师
**某某软件公司** | 2017.07 - 2020.02

负责移动文件管理系统（MaterialFiles）的测试工作：
- 制定移动应用测试策略和测试计划
- 执行功能测试、兼容性测试和稳定性测试
- 使用Charles进行移动应用抓包分析
- 搭建移动自动化测试框架，提高测试效率

## SKILLS

### 测试技能
- 熟练掌握移动应用测试方法和技术
- 精通Android/iOS平台应用测试
- 熟练使用Appium、UIAutomator等自动化测试工具
- 掌握Charles、Fiddler等抓包工具

### 开发技能
- 熟悉Java、Python编程语言
- 了解Android开发基础知识
- 熟悉SQLite数据库
- 掌握Linux常用命令

### 其他技能
- 熟悉敏捷开发流程
- 良好的文档编写能力
- 优秀的沟通协作能力
- 较强的问题分析和解决能力

## LANGUAGE
- 英语：CET-6，良好的读写能力
- 中文：母语 