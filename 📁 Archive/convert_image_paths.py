import os
import re

def extract_numbers(filename):
    # 提取文件名中的数字部分
    numbers = re.findall(r'\d+', filename)
    if numbers:
        return numbers[0]
    return filename

def convert_image_paths(md_file_path):
    print(f'开始处理文件: {md_file_path}')
    
    # 读取Markdown文件
    with open(md_file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # 定义所有可能的图片路径模式
    patterns = [
        # 1. 处理格式：![xxx](./img/xxx/filename-numbers.png)
        (r'!\[(.*?)\]\((\.\/img\/[^)]+\/(\d+[^)\/]+?)(?:-\d+)?\.(?:png|jpe?g|gif))\)', r'![[\3.png]]'),
        
        # 2. 处理格式：![xxx.png](./img/xxx/filename-numbers.png)
        (r'!\[.*?\.(?:png|jpe?g|gif)\]\((\.\/img\/[^)]+\/(\d+[^)\/]+?)(?:-\d+)?\.(?:png|jpe?g|gif))\)', r'![[\2.png]]'),
        
        # 3. 处理格式：![xxx](./img/8X9e0d9ODVPNIerj/1642108033903-5f252111-713b-40e7-a4cd-8becc8b33a08-839494.png)
        (r'!\[.*?\]\(\.\/img\/[^)]+\/(\d+[^)]+?)(?:-\d+)?\.(?:png|jpe?g|gif)\)', r'![[\1.png]]')
    ]
    
    # 依次应用每个模式进行替换
    for pattern, replacement in patterns:
        content = re.sub(pattern, replacement, content)
    
    # 写回文件
    with open(md_file_path, 'w', encoding='utf-8') as file:
        file.write(content)
    
    print(f'已完成文件转换')

if __name__ == '__main__':
    # 指定要处理的文件路径
    file_path = '6-Full Notes/01.mysql与jdbc.md'
    try:
        convert_image_paths(file_path)
    except Exception as e:
        print(f'处理文件时出错: {str(e)}') 