---
created: {{date}} {{time}}
type: directory-guide
---

# 思维导图 (Mind Maps)

这个文件夹用于存放知识体系的思维导图和相关图片资源。

## 文件夹结构
- `XMind/` - 存放XMind源文件
  - `软件测试/` - 测试相关思维导图
  - `开发技术/` - 开发相关思维导图
  - `运维技术/` - 运维相关思维导图
  
- `Images/` - 存放导出的图片文件
  - `PNG/` - PNG格式图片
  - `SVG/` - SVG格式图片
  - `JPG/` - JPG格式图片

## 命名规范
1. XMind文件命名
   - 使用英文或拼音命名
   - 文件名应与对应的markdown文件保持一致
   - 示例：`software_testing.xmind`、`linux_basic.xmind`

2. 图片文件命名
   - 使用与XMind文件相同的基础名称
   - 添加日期后缀以区分版本
   - 示例：`software_testing_20240101.png`

## 使用建议
1. 版本管理
   - 定期保存XMind文件的不同版本
   - 为重要的版本添加说明文件
   - 保留关键节点的图片导出

2. 图片导出
   - 选择合适的导出格式
   - 确保图片清晰度
   - 控制文件大小

3. 文件组织
   - 按主题分类存放
   - 保持目录结构清晰
   - 及时清理过时文件

## 与其他目录的关联
- 每个XMind文件应对应一个markdown文档
- 在markdown文档中引用相关的思维导图
- 使用相对路径引用图片资源

## 更新维护
1. 定期检查
   - 验证文件完整性
   - 更新过时内容
   - 优化目录结构

2. 备份建议
   - 定期备份XMind源文件
   - 保存重要的图片导出
   - 使用版本控制工具

## 注意事项
- 控制单个文件的大小
- 保持文件命名的一致性
- 及时同步更新相关文档
- 注意图片的版权问题 