---
id: 27ff68e6-0b80-4011-97e6-688327c0ff5f
---

# 那些让你起飞的计算机基础知识！
#Omnivore

## 程序员小富

小富哥是我的好朋友，目前就职某狗东，目前职位架构师，负责电商平台的架构设计，工作8年来从事了支付、医美、电商、跨境电商等诸多行业。

他做了一个公众号【程序员小富】，主要分享他在实际开发中遇到问题的解决方案，回答粉丝提的一些问题。

比如：面试经验、架构设计、各类中间件使用的坑。**一口气**系列文章，就是为解答粉丝问题而创作的，是个很**宠粉**的大佬。

* [一口气说出 6种 延时队列的实现方法，面试官也得服](https://mp.weixin.qq.com/s?%5F%5Fbiz=MzAxNTM4NzAyNg==&mid=2247484104&idx=1&sn=bff39d224a04979d684cb5ee1cb479e2&scene=21#wechat%5Fredirect)
* [一口气说出 4种 “附近的人” 实现方式，面试官笑了](https://mp.weixin.qq.com/s?%5F%5Fbiz=MzAxNTM4NzAyNg==&mid=2247484037&idx=1&sn=c2a013c2f8a39b6a46a5dbfaf00366ee&scene=21#wechat%5Fredirect)
* [一口气说出 9种 分布式ID生成方式，面试官有点懵了](https://mp.weixin.qq.com/s?%5F%5Fbiz=MzAxNTM4NzAyNg==&mid=2247483785&idx=1&sn=8b828a8ae1701b810fe3969be536cb14&scene=21#wechat%5Fredirect)
* [一口气说出 5个 redis分布式锁的坑，真是又大又深](https://mp.weixin.qq.com/s?%5F%5Fbiz=MzAxNTM4NzAyNg==&mid=2247484051&idx=1&sn=a4bda1d370a31e2f3f9139c416e9f6d5&scene=21#wechat%5Fredirect)
* [一口气说出 9种 分库分表的分布式主键ID 生成方案](https://mp.weixin.qq.com/s?%5F%5Fbiz=MzAxNTM4NzAyNg==&mid=2247488759&idx=1&sn=16b3a1982def7f227d5ed52189e16b30&scene=21#wechat%5Fredirect)
* [一口气说出 3种 Springboot全局时间格式化方式，别再写重复代码了](https://mp.weixin.qq.com/s?%5F%5Fbiz=MzAxNTM4NzAyNg==&mid=2247487775&idx=1&sn=8dafd92ec3f5e61cf531b6d26bd007d7&scene=21#wechat%5Fredirect)
* [一口气说出 4种 OAuth2.0的授权方式](https://mp.weixin.qq.com/s?%5F%5Fbiz=MzAxNTM4NzAyNg==&mid=2247487003&idx=1&sn=47cd6b064a7fc3b3df8f6f4c3f7669a7&scene=21#wechat%5Fredirect)
* [一口气说出 6个 滤器器和拦截器区别，别再傻傻分不清了](https://mp.weixin.qq.com/s?%5F%5Fbiz=MzAxNTM4NzAyNg==&mid=2247486003&idx=1&sn=d9b48611b8b742e8d7ce20ace41b6d35&scene=21#wechat%5Fredirect)
* [一口气说出 6种 @Transactional 注解失效场景](https://mp.weixin.qq.com/s?%5F%5Fbiz=MzAxNTM4NzAyNg==&mid=2247483977&idx=1&sn=7d8d3c89bfe2261f6422572dca405990&scene=21#wechat%5Fredirect)
* [大厂也在用的 6种 数据脱敏方案，别做泄密内鬼](https://mp.weixin.qq.com/s?%5F%5Fbiz=MzAxNTM4NzAyNg==&mid=2247489848&idx=1&sn=a4163a7c0914bd8e1ec1f29171ab7c92&scene=21#wechat%5Fredirect)
* [MySQL不会丢失数据的秘密，就藏在它的 7种日志里](https://mp.weixin.qq.com/s?%5F%5Fbiz=MzAxNTM4NzAyNg==&mid=2247491379&idx=1&sn=c25b935a7070bb3d887f1a39cc67c4ac&scene=21#wechat%5Fredirect)
* ......

这里也只是部分优质文章，小富一路成长基本是靠自学，多看书多总结，对于自学最好的验证方式就是总结，自己学会的能说出来还能写出来，这说明你真的掌握了，给大家分享一份计算机基础知识学习路线指南，基础知识是奠定上层建筑的基石，打好底子才能学好里子！！！

点下边名片关注公众号，回复【**7777**】

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,s6oNb6llcXfAVsPFZV1YokIWTJTmWnstwQZDnuO3EE1o/https://mmbiz.qpic.cn/mmbiz_png/A3ibcic1Xe0iaTo0PgEghzOx6tAM4k7X04GJdFkNaCY81iaHGk9XUBMIzNrkLXw09gZvqUznaicf4l7rWkDrBxEsfNw/640?wx_fmt=png)  

基本上涵盖了计算机所有基础知识，从 CPU 到内存、讲解什么是二进制、磁盘、压缩算法、操作系统、汇编等知识。

我们来看下内容是怎样的

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sb_IvxAUR9ihparwFT8RSdauXAyzuvK4Kc421g6zvS6I/https://mmbiz.qpic.cn/mmbiz_png/A3ibcic1Xe0iaTo0PgEghzOx6tAM4k7X04GofPXzlVHYojFSh0Nt0e4IhyCBPHkZicJ2Ey3WRgRSUjXlg4RxCvBPgg/640?wx_fmt=png)

这个图画的很漂亮啊，看起来就是作者在用心画的，而且排版非常精美。

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sPMOSY76PnUlnkZeof7dJVappNLwdQtKF80N9vK4mCB8/https://mmbiz.qpic.cn/mmbiz_png/A3ibcic1Xe0iaTo0PgEghzOx6tAM4k7X04GuKKZWrciaxAw1xwjIH8EkYt8pb5qfxJC0r2nG2sGDFtD2H5dliaMgxGw/640?wx_fmt=png)

看起来一点不枯燥

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sXHkMKq-5BtxR8NxbuGpmalst1uKm5hQzmIeMnmHWZXQ/https://mmbiz.qpic.cn/mmbiz_png/A3ibcic1Xe0iaTo0PgEghzOx6tAM4k7X04Gh1bsrtHetKzBRvoG2HtrM4P9dshrXuce4bn5Fz6xlJhN8kSkibEmfUA/640?wx_fmt=png)

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,s6W5kKlK-AiRVe4MYMAT7NbJwi5wcexe04gmPhMN9hE4/https://mmbiz.qpic.cn/mmbiz_png/A3ibcic1Xe0iaTo0PgEghzOx6tAM4k7X04GWxIzSee3v3on5lX8YYpppNTOl7Lx7QBXH6DRkxib6Dfc9X1ytElIZcw/640?wx_fmt=png)

大家在公众号“**程序员小富**”后台回复 **7777** 即可获取！

另外，再推荐一位谷歌大佬的刷题笔记,每一道题的题解都写得非常清楚。

作者在美国卡内基梅隆大学攻读硕士学位时，为了准备实习秋招，他从夏天开始整理 Leetcode 上的题目，几个月的时间，刷了几百道题目。

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,s1hEeM4JH-UQGEW-3bssuMuEiNXqvpez9UX7STfAVrhE/https://mmbiz.qpic.cn/mmbiz_png/BcyAypujBVbtoib7mwiavg80lfBV4W9XwUGuTssSTQtwgfjIUK94s48q58EHwjIibT7hhxSmibbvbR4Qn0iaXIN3oibw/640?wx_fmt=png)

这本 PDF 涵盖了几乎所有面试中常见的算法题型！

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sg_oNM7gCkb1widgwkPh2evkS4zZR0ldFqZgk_SMCCVc/https://mmbiz.qpic.cn/mmbiz_png/BcyAypujBVbtoib7mwiavg80lfBV4W9XwU89dZ1l1LgKzEXZ4BYlZjpTbibWTDFpGxHMuOAibpcl1g3jLO2UqLfIvA/640?wx_fmt=png)

大家在公众号**“程序员小富”**后台回复 **7777** 即可获取。

[Read on Omnivore](https://omnivore.app/me/https-mp-weixin-qq-com-s-biz-mzkz-mdi-1-njcy-oq-3-d-3-d-ascene-5-1943e3494b1)
[Read Original](https://mp.weixin.qq.com/s?__biz=MzkzMDI1NjcyOQ%3D%3D&ascene=56&chksm=c313bf6ca5d8c01299ab4bca2dc4b293c01fbd5b353c13c14314a399310f6b6a94d5002b615f&clicktime=1736210152&countrycode=HR&devicetype=android-35&enterid=1736210152&exportkey=n_ChQIAhIQYZyIS92qUuaJmxGT13stTRLxAQIE97dBBAEAAAAAAPqmJSLQHV4AAAAOpnltbLcz9gKNyK89dVj0he7%2FaGfWkXpaedn0GmCur7lAXMjAxrxqzpIcDtFqi2mBvG6xNZb32jxzgRQKCzMuqgoCjFT0YkVgZnnQGtWgmBu8aIZiuEo14s%2BOZ3j46XwQ%2FyiINx%2BjgbN3OWXxQF3xPb5lHSMluMon%2BI5NO6JRA39B8oubmR5j4gMIfl5VxQkKcsfrsjZOdRdH27VvfXWagbfU%2FEYPpfBDoTff2yIhBTujSIUH23eIpj6rZkvDKLJsvZdfa%2Fchxaei3lm4FGo65FWNLMiEbmeFlJ4%3D&fasttmpl_flag=0&fasttmpl_fullversion=7547503-zh_CN-zip&fasttmpl_type=0&finder_biz_enter_id=4&flutter_pos=3&idx=1&lang=zh_CN&mid=2247504944&nettype=gmtds&pass_ticket=VlJsEJ2mhPKBYDo%2BrIgbwzoJ0WquhwM7iNk%2BA6zjqjzJ7IjZL2TMMeUcb%2B44Mgu7&ranksessionid=1736210088&realreporttime=1736210152837&scene=90&session_us=gh_66e5d19675b3&sessionid=1736210095&sn=9d3509c698cafaab79eabeeb4f2b6b59&subscene=93&version=2800373b&wx_header=3&xtrack=1)

