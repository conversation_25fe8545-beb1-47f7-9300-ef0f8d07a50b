---
id: 8b6a435e-3aaf-4fb0-9f15-f324dfc32959
---

# 好用的自动同步软件：FreeFileSync - 知乎
#Omnivore

最近研究怎么能够将文件从电脑上自动备份到移动硬盘，发现了很多人都在安利的FreeFileSync（官网：[FreeFileSync](https://link.zhihu.com/?target=https%3A//freefilesync.org/) ）

这是一个免费软件，在各个系统中都有提供安装包。软件安装过程非常简单，目前我将其直接安装到了C盘，安装好后即可看到两个软件：绿色的FreeFileSync和红色的RealTimeSync。我们使用的方式是：先用绿色的设置好同步选项，再用红色的执行并且监控目录自动同步。

---

**注意：**

**1\. 请在同步之前确认路径是否正确！千万不要把两个路径写反了！！！**

**2\. 该软件相当于是自动进行复制粘贴，推荐在【同步设置】——【同步】——【删除文件】中选择【历史版本】，将被覆盖、删除的文件的历史版本保存下来，避免文件丢失！！！**

**3\. 目前有人用该软件导致文件丢失（我在使用中并未发现此情况，可能是使用云盘导致），请在正式使用前先进行测试，并从官网下载软件（** [FreeFileSync](https://link.zhihu.com/?target=https%3A//freefilesync.org/download.php) **）**

---

### 同步选项

打开[绿色软件](https://zhida.zhihu.com/search?content%5Fid=147438028&content%5Ftype=Article&match%5Forder=1&q=%E7%BB%BF%E8%89%B2%E8%BD%AF%E4%BB%B6&zhida%5Fsource=entity)，点击上方齿轮状的的设置按钮，弹出同步配置菜单：

![](https://proxy-prod.omnivore-image-cache.app/717x0,sd7i6-MHIoGz2EFDuJddk45tAy-2mzAOHi9KXiNTsCzA/https://picx.zhimg.com/v2-d7bb4c518630c9e10a995526cef3759b_1440w.jpg)

我的使用场景是：想要备份数据的多个版本，因此会将所有文件都放到移动硬盘里但不会将移动硬盘中的文件复制回来，也就是需要进行单向同步**（注意：在这种配置下，移动硬盘中多余的文件/文件历史版本会被删除，不是双向同步！）**；并且我想要把文件历史版本也保存下来到另一个文件夹中，所以我的设置如图所示：

![](https://proxy-prod.omnivore-image-cache.app/721x0,so-mWRM1wFntuIhfJ_4hPNg34vC7vKTYfyGZV1ZGwdMk/https://pica.zhimg.com/v2-14f88d1e5a4254b71ab9a588f122420e_1440w.jpg)

历史版本这里的文件夹类似于回收站，会记录每一个文件的历史版本。**注意：最好不要选择【永久】，否则被覆盖/删除的文件的历史版本无法恢复！**

![](https://proxy-prod.omnivore-image-cache.app/730x0,sq-8IDdcWERsBTFmeVcWM-uCmHUXShN8WxTEZz5EgeBk/https://pica.zhimg.com/v2-eb3ed58f79fcfa571765fd46ca6b68a8_1440w.png)

左侧是要复制的文件的源路径，右侧是目标路径（移动硬盘中的文件夹）。**注意：两个文件夹的路径不要写反了！**

配置好后，可以将这个文件保存起来。**文件——另存为** 可以将这个配置保存成一个普通的[同步文件](https://zhida.zhihu.com/search?content%5Fid=147438028&content%5Ftype=Article&match%5Forder=1&q=%E5%90%8C%E6%AD%A5%E6%96%87%E4%BB%B6&zhida%5Fsource=entity)，可以在绿色的软件中鼠标点击进行同步；而 **文件——另存为[批处理作业](https://zhida.zhihu.com/search?content%5Fid=147438028&content%5Ftype=Article&match%5Forder=1&q=%E6%89%B9%E5%A4%84%E7%90%86%E4%BD%9C%E4%B8%9A&zhida%5Fsource=entity)** 可以将其保存成一个ffs\_batch文件，可以用它进行自动同步。

### 自动同步设置

打开红色的软件，**文件——打开** 选择刚才创建的ffs\_batch文件，即可进行自动同步了。里面可以设置自动同步的时间间隔（默认是10秒）。

![](https://proxy-prod.omnivore-image-cache.app/448x0,s20pvfK97bBt4VgKwggG3itS_dDe0TG-fzMg-1HZQiCw/https://pica.zhimg.com/v2-900305071d15653d279589a8d946c8fe_1440w.jpg)

如果文件夹是移动硬盘上的，软件会检测是否有这个盘符，如果没有的话j就不会轻举妄动。因此每次插上硬盘就可以进行同步了。

### 盘符设置

由于Windows上面的盘符可能会变，所以我们可以不用盘符而换用磁盘的名称，如我的移动硬盘叫做HenryFox（右键重命名即可修改这个名字）：

![](https://proxy-prod.omnivore-image-cache.app/254x0,silpMsyANSH693jXuOo_lLycy2XujLureR-Hmy-mAJuA/https://pic4.zhimg.com/v2-540825b2c72296e8911be2f46363068d_1440w.jpg)

我们在设置盘符的时候就可以换成用这个盘的名字而非盘符，如：

![](https://proxy-prod.omnivore-image-cache.app/392x0,swP-DmS9h6j9ifnqSaFZjyyFR-VwsBVqMkbmOd7p9O10/https://pic2.zhimg.com/v2-6386397135b16223c858b2fc0444fc5d_1440w.jpg)

这样就不会因为移动硬盘插入的次序不同而导致盘符不同出错了。

### Windows上开机自动启动同步

我们首先需要找到我们安装的RealTimeSync的位置，直接在快捷方式上**右键——打开文件所在位置**即可。

![](https://proxy-prod.omnivore-image-cache.app/398x0,sbSzfykFtdcBvWu1l6TTwy_5DQJGQ21E8r1dy0V-W4gQ/https://pica.zhimg.com/v2-69b38c1a7076e8710cdfce73490d6a44_1440w.jpg)

打开后点击地址栏，就可以复制文件所在的目录了，比如我这里是**C:\\Program Files\\FreeFileSync**

![](https://proxy-prod.omnivore-image-cache.app/510x0,sHpeizMaTO1Kn2_y3i1ZspdnLThby8beik_Os43jpAuw/https://picx.zhimg.com/v2-6bc03ea4b42343321fa1c897b9a8fe37_1440w.jpg)

接下来写一个txt文本文件，内容如下：

```taggerscript
@echo off
start "realtimesync" "C:\Program Files\FreeFileSync\RealTimeSync.exe" C:\Users\<USER>\Desktop\备份与记录\DataFolderSyncSettings.ffs_batch
```

这里前半部分的**C:\\Program Files\\FreeFileSync**需要换成你的FreeFileSync目录，而空格后面是自己的ffs\_batch文件的路径。**注意：如果路径有空格，需要用英文双引号引起来**

接下来用**文件——另存为**进行保存，编码设置为**ANSI**

![](https://proxy-prod.omnivore-image-cache.app/560x0,su5EF46FtpkQ9KMIs7AVZJACP2EXluXUwXnWl5_WjSbw/https://pica.zhimg.com/v2-3151e177a261c719480ccf9b9f15abb0_1440w.jpg)

最后，打开[启动文件夹](https://zhida.zhihu.com/search?content%5Fid=147438028&content%5Ftype=Article&match%5Forder=1&q=%E5%90%AF%E5%8A%A8%E6%96%87%E4%BB%B6%E5%A4%B9&zhida%5Fsource=entity)，把刚才的bat文件复制进去即可！（打开启动文件夹的步骤是：按下键盘上的win+R，输入**shell:startup**回车）

[Read on Omnivore](https://omnivore.app/me/free-file-sync-1943b6b2d6f)
[Read Original](https://zhuanlan.zhihu.com/p/266883367)

