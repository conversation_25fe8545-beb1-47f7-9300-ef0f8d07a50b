---
id: db4177f6-68ee-4420-ba65-1c6ee7026c45
---

# 一键搞定！在普通电脑上轻松运行大模型，注意不是 llama.cpp！
#Omnivore

近年来，大型语言模型（LLMs）在各个领域的应用愈发广泛。然而，对于普通用户而言，将这些强大的模型运行在本地PC上一直是个难题。Llamafile 的出现，打破了这个障碍，使得用户可以轻松地在多平台上运行大模型。本文将详细介绍 Llamafile 的特点、运行方法、与 llama.cpp 的关系以及其优势。

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sZybrGJWkbyB_SkMllA-OfiSgkwndAceAga-X4Pz5EQQ/https://mmbiz.qpic.cn/mmbiz_jpg/qE5QC4wLYCQQfle6CRibSySpNvaQlW5lFF5FE8ycMFtxbgTqKBHNMRsSSbcibsanTrpBicMwiaK4x7FGOHUHt7ibcMw/640?wx_fmt=jpeg)

---

### 1\. Llamafile 的特点

Llamafile 是一个基于 llama.cpp 项目开发的工具，通过将所有运行依赖打包成单个文件，简化了大型语言模型在本地PC上的分发和执行。用户只需下载一个文件，即可运行 LLM，无需额外安装复杂的运行环境。Llamafile 的主要特点包括：

* 单文件执行：Llamafile 将模型、依赖项以及执行环境打包成单文件，使得运行过程更加简便。
* 跨平台支持：借助 Cosmopolitan Libc，Llamafile 可以在 Windows、macOS 和 Linux 系统上无缝运行。
* 隐私保护：所有数据和计算均在本地完成，数据不会传输到云端，为用户提供了更高的隐私保障。

这种设计理念让 Llamafile 成为普通用户体验大型语言模型的绝佳选择。

---

### 2\. 跨平台运行示例

Llamafile 提供了一个简便的运行方式，使得用户可以轻松在不同系统上体验 LLM。以下以 LLaVA 模型为例，展示如何在 Windows、macOS 和 Linux 系统上运行 Llamafile。

#### 下载 LLaVA 模型

首先，下载 LLaVA 模型的 Llamafile 文件 `llava-v1.5-7b-q4.llamafile`（大小约为 4.29 GB）。该模型支持对话以及图片上传并回答相关问题，且所有操作均在本地进行，确保隐私。

#### 设置执行权限（适用于 macOS 和 Linux）

1. 打开终端，授予该文件执行权限（此步骤仅限 macOS 和 Linux 用户）：  
```angelscript  
chmod +x llava-v1.5-7b-q4.llamafile  
```
2. 对于 Windows 用户，只需在文件名后添加“.exe”后缀。

#### 运行 Llamafile

1. 在终端中运行以下命令启动模型：  
```angelscript  
./llava-v1.5-7b-q4.llamafile  
```
2. 运行后，浏览器会自动打开一个聊天界面（如未自动打开，可手动访问 http://localhost:8080）。

#### 停止运行

完成会话后，返回终端并按 `Control-C` 终止运行。这样，用户就可以简单几步在本地体验到 LLM 的强大功能。

#### 实际运行截图

以下是我的笔记本的配置  

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,shv_dTE7Pe4mge01py2WihKD09wXD7v-DLWCDrmZ0Bmk/https://mmbiz.qpic.cn/mmbiz_png/qE5QC4wLYCQQfle6CRibSySpNvaQlW5lFTuPZAbquGWOS89S7KsCibvTibD4qvEL1EqUFMazibiaS6rkawCVW7FqCcg/640?wx_fmt=png&from=appmsg)

下面是 llava-v1.5-7b 的运行画面，可以看到在没有GPU加速的情况下，我的 Intel i5 也能达到 6 token/秒的输出速度。  

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sqv-tswrabYBLntqzS54pmfmKcCA8tTSfRvbjsVl7GNg/https://mmbiz.qpic.cn/mmbiz_gif/qE5QC4wLYCQQfle6CRibSySpNvaQlW5lFBaib6YMcZaazHThBGvnrC8jUtVjnfcrzd7QJF46iaEluDq1eKsVibxM1g/640?wx_fmt=gif&from=appmsg)

运行的命令：  

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sN9dckOHAhZ_gIwhZJFutsfB_QUapdLY5v-ZumjSyRO0/https://mmbiz.qpic.cn/mmbiz_png/qE5QC4wLYCQQfle6CRibSySpNvaQlW5lFIb0YuVPnO5tCSj6zz8f2XEG3PexbGsun5xNsICkP6ORARm8c03qbtw/640?wx_fmt=png&from=appmsg)

---

### 3\. Llamafile 与 llama.cpp 的关系

Llamafile 项目基于 llama.cpp 构建，旨在进一步简化大型语言模型的分发和执行流程。

* llama.cpp 是 Georgi Gerganov 开发的开源项目，采用 C/C++ 编写，设计目标是提供一种轻量化的方式在本地运行 LLM。它使用 GGUF 模型格式，实现了高效的数据处理，是 LLM 本地化的重要工具。
* Llamafile 在 llama.cpp 基础上结合 Cosmopolitan Libc，将模型、执行环境和依赖打包成单文件，从而实现下载即运行。Llamafile 保留了 llama.cpp 的高效推理能力，并大幅提升了用户体验。

通过整合 llama.cpp，Llamafile 为用户提供了一个更加便捷的本地 LLM 运行环境，进一步降低了 LLM 的使用门槛。

---

### 4\. Llamafile 的流程优势

相比 llama.cpp，Llamafile 在简化用户流程上具有显著优势：

1. 单文件可执行程序：Llamafile 将模型、依赖库和运行环境整合到一个文件中，无需用户安装多个库或配置复杂的环境。
2. 极简的设置流程：在 llama.cpp 中，用户通常需要编译代码、处理依赖项和配置环境。Llamafile 则省去了这些步骤，用户只需下载并运行。
3. 跨平台兼容性：Llamafile 使用 Cosmopolitan Libc 实现跨平台支持，能够在 Windows、macOS 和 Linux 上运行，无需针对特定平台的额外设置。
4. 分发的便捷性：Llamafile 统一了文件管理，不会产生版本不匹配或缺少依赖的问题，分发和使用都更为便捷。
5. 资源效率：Llamafile 保留了 llama.cpp 的高效性，使得模型在本地快速运行，尤其适合老旧或性能较低的电脑。

通过这些优势，Llamafile 将 LLM 的本地化使用流程简化为“即下载即运行”，显著提升了模型的可访问性和易用性。

---

### 总结

Llamafile 在 llama.cpp 的基础上实现了本地 LLM 运行的极简化，让用户可以在无需复杂配置的情况下，在多平台上轻松体验大型语言模型。这种工具不仅提升了 LLM 的可用性，还为希望本地运行 LLM 的用户提供了隐私友好的解决方案。对于普通用户来说，Llamafile 是一种即开即用的解决方案，使得本地化的大模型体验触手可及。

### 参考网址

* 文中用到的大模型链接  
https://huggingface.co/Mozilla/llava-v1.5-7b-llamafile/resolve/main/llava-v1.5-7b-q4.llamafile?download=true
* llamafile 项目网址  
https://github.com/Mozilla-Ocho/llamafile

推荐阅读

* [【大模型】深度解析：智能体如何突破 RAG 的三大技术瓶颈](http://mp.weixin.qq.com/s?%5F%5Fbiz=MzAwNDEyNTg0MA==&mid=2649952370&idx=1&sn=d7c7f233e60a4470505edec1ea1af06e&chksm=8337158db4409c9b508ca516f357445bb5140a55105b2e5d6b10ac7699baf2f514bfce8ff401&scene=21#wechat%5Fredirect)
* [【大模型】一个简单程序看透 RAG 的核心原理，理解优化 RAG 的关键要点](http://mp.weixin.qq.com/s?%5F%5Fbiz=MzAwNDEyNTg0MA==&mid=2649952322&idx=1&sn=751f9634ca02c3d4c1a22b8a44179f6c&chksm=833715bdb4409cabbfc0dc980536f9c8955a533e2550b06e45343c44ce3845059e3a25823aa9&scene=21#wechat%5Fredirect)
* [【大模型】让AI不仅能说还能做！详解AI Agent的核心架构](http://mp.weixin.qq.com/s?%5F%5Fbiz=MzAwNDEyNTg0MA==&mid=2649952328&idx=1&sn=ad3fe9677231c684dd70af6ed07c93f9&chksm=833715b7b4409ca12771cbe3a3da368c0f360003ecdaa698076bc27b0ad03586493965cfb589&scene=21#wechat%5Fredirect)
* [【大模型之Graph RAG系列之一】由谷歌搜索的演进看知识图谱如何改进RAG技术](http://mp.weixin.qq.com/s?%5F%5Fbiz=MzAwNDEyNTg0MA==&mid=2649952338&idx=1&sn=ea19968bef21b4fa0cbe443624971add&chksm=833715adb4409cbb5b950d06273d7e6e9874cfa76bc4629d1ce9b89c195a299f03fab34a6f08&scene=21#wechat%5Fredirect)
* [AIGC: 从两个维度快速选择大模型开发技术路线](http://mp.weixin.qq.com/s?%5F%5Fbiz=MzAwNDEyNTg0MA==&mid=2649952300&idx=1&sn=d5a2894171379e3b4f02cd7d857cca2c&chksm=833715d3b4409cc5789251bfd417d23b6dcd446362632c8966504b31f30e732335980b5bcfa4&scene=21#wechat%5Fredirect)

[Read on Omnivore](https://omnivore.app/me/llama-cpp-1943ace34af)
[Read Original](https://mp.weixin.qq.com/s/rl1uLQwJ5kpgVJK0b1E4bQ)

