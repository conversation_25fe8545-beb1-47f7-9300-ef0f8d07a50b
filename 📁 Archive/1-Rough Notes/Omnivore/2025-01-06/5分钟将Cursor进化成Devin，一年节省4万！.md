---
id: ce00799d-318f-4a94-beab-736166b3208f
---

# 5分钟将Cursor进化成Devin，一年节省4万！
#Omnivore

 昨天社群中一个朋友向我推荐了一个开源项目，能够通过简单的配置，让普通的 AI 编程助手 Cursor 获得接近 Devin（一个高级 AI 编程助手）的能力。

我赶紧体验了下，结论是它至少让我的开发效率提升了80%！

各位可以去原作者项目star支持！是一个非常不错的项目！具体链接在文末。

* • 开源项目地址\[1\]
* • 项目作者的博客\[2\]

Devin 作为一款全自动编程的 AI 助手，相比 Cursor 等工具具有以下优势：

1. 1\. 过程规划能力
2. 2\. 自我进化能力
3. 3\. 扩展工具使用
4. 4\. 全自动执行

但这些功能差异真的需要付出 500美金/月 vs 20美金/月 的价格差异吗？这个开源项目告诉我们：**不需要！**

此开源项目可以实现上面列出来Devin独有的1、2、3点能力。下面我会给大家解析下这个项目是如何实现这3个功能。

同时我会实际演示使用和不使用这个项目的区别在哪里

## 二、实现原理

### 1\. 过程规划与自我进化

核心实现：利用 `.cursorrules` 文件

* • 将计划内容放入该文件
* • 每次交互都能获取最新版本的计划
* • AI 可以自动更新进度和下一步计划
* • 记录经验教训，实现知识积累

例如：

当前的 LLMs，由于其知识截止日期相对较早，并不知道 GPT-4o 模型。如果你让他们调用 GPT-4o，他们会删除“o”，认为这是一个错别字。

但是如果你纠正他们：“这个模型确实存在，你只是不知道它”，他们会把**这个教训记录在 .cursorrules 的相关部分**，不会再犯同样的错误，从而达到学习和改进的目的。

### 2\. 扩展工具使用

通过预置工具程序 + `.cursorrules` 说明的方式，为 Cursor 增加：

* • 网页浏览能力（爬虫）（使用 playwright）
* • 搜索功能（基于 DuckDuckGo API）
* • LLM 分析能力（支持本地或云端 API）

---

#### 网页浏览能力（爬虫）

tool文件夹下有一个search\_engine.py的脚本，他会使用DuckDuckGo的免费API搜索互联网内容。

要注意DuckDuckGo需要有魔法的情况下使用，按作者描述，你可以替换成

#### 搜索功能（基于 DuckDuckGo API）

tool文件夹下有一个web\_scraper.py的脚本，爬取指定页面内容，并使用LLM进行分析

#### LLM 分析能力（支持本地或云端 API）

tool文件夹下有一个llm\_api.py的脚本，可以将爬取到的页面内容、搜索到的互联网内容，经过LLM分析。

这个LLM可以是你本地部署的，也可以是OpenAPI等等大模型

**值得注意的是，因为支持拓展工具，所以你完全可以开发自己的拓展脚本工具**

例如当你需要实现指纹浏览器的能力时

你可以开发一个IP更换的脚本，当然了让cursor帮你写就行，原作者也表达了他的这些工具都是cursor帮助写的。

### 3\. 全自动执行

* • Cursor：目前需要手动确认命令执行

虽然Cursor目前推出了YOLO模式，但是和Devin原生自动执行还是有差距，这是因为Devin运行在一个完全虚拟的环境中。

就算命令执行有问题也没关系，大不了重新开始。

* • Windsurf：支持在 Docker 容器中运行，提供更安全的自动执行环境。

这是windsurf比Cursor做的好的地方，甚至你可以认为Windsurf的发展方向就是Devin

## 三、功能对比

| 功能   | Devin ($500/月) | 改进后的 Cursor ($20/月) |
| ---- | -------------- | ------------------- |
| 过程规划 | 完整自动           | 接近 Devin            |
| 自我进化 | 支持             | 支持                  |
| 工具扩展 | 丰富             | 接近 Devin，可扩展        |
| 自动执行 | 完全支持           | 需确认或变通              |

## 四、项目作者实际应用案例

改进后的 Cursor 可以完成以下复杂任务：

1. 1\. 分析过去 5 年科技股票回报率，生成详细报告
2. 2\. 爬取博客 Top 100 文章发布时间，生成 GitHub 风格可视化
3. 3\. 1.5 小时内完成一个包含前后端的招聘网站

以上是原作者的实战。下面我给出我的体验case

## 五、我的实际应用案例

我将对比在相同任务下，不使用这个项目和使用这个项目的时候cursor的表现。虽然没有特别深入，但是获取可以管中窥豹看到这个项目的价值。

任务：抓取我免费cursor教程博客网站lookai.top\[3\]的所有文章，包括文章主题、标题

### 不使用本项目

cursor会告诉你他没有抓取页面的能力，需要先实现一个爬虫才能够实现。

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,s8cVA2XMlEy6oVf72uAU6QYGcL9TSkGFQR0eE4gMEJEU/https://mmbiz.qpic.cn/mmbiz_png/RBgzkvDqanKcribhJhFX05qYqa1NSibf3piaIrLlrYj8FsVVXA2PtfHKeLKiak0SDIHDTJ0llKPCZfk5LUlxWWm6Xg/640?wx_fmt=png&from=appmsg "null")

### 使用本项目

cursor会明白要去调用爬虫那个脚本，去获取页面信息，然后再进行进一步的处理。

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sBT7gHhUnFcymcSSB6B1M5WT1fOS3txxNkPNbw9Lc5e4/https://mmbiz.qpic.cn/mmbiz_png/RBgzkvDqanKcribhJhFX05qYqa1NSibf3pcaniccRZAonj9kuT9yxzGhJxgag7dLuhOfMI3RibCxCIy3G3J4lwkaJw/640?wx_fmt=png&from=appmsg "null")

cursor不仅可以知道我博客有哪些网站，而且可以一次性爬取多个页面。

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sDHmzyAklJ7P_JO3oyt-FYQJU4cUIUOwycE7Dh-WceIs/https://mmbiz.qpic.cn/mmbiz_png/RBgzkvDqanKcribhJhFX05qYqa1NSibf3pO2ia1feHRjOb9vTEicyeRAJ5vPHjWrK4vhHl83TXCGnSZ3zM5PygicGew/640?wx_fmt=png&from=appmsg "null")

## 五、项目使用方法

1. 1\. 从 GitHub 下载项目文件
2. 2\. 复制到你当前项目文件夹

例如我的项目是一个nexjs项目，我的项目根目录是绿色框标记内容。

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sptJWUZFeLvfmNqjRzH6399WO-13u7W-AtBw_vGa240w/https://mmbiz.qpic.cn/mmbiz_png/RBgzkvDqanKcribhJhFX05qYqa1NSibf3pTLnNcNhborzyibgQkkOMGibsSbdpEFok7QfkskqQf8kYlbAjDfXkkOSA/640?wx_fmt=png&from=appmsg "null")

红色框就是我复制过来的内容。

1. 3\. 执行命令：

说明一下，安装下面这些依赖，都是为了执行爬虫、llm、网页搜索这些拓展而需要的。最后你的项目应该就和上面我的项目长得一样。

```vala
# 创建一个python的环境，这需要你电脑已经安装好了python ./py310
python3 -m venv py310

# 启动python
# mac执行如下：
source py310/bin/activate
# Windows执行如下:
.\py310\Scripts\activate


# 安装依赖
pip install -r requirements.txt

# 安装爬虫依赖
python -m playwright install chromium
```

1. 4\. 查看你的cursor项目索引是否构建好了

上下文怎么构建你可以关注本公众号，后台输入「上下文构建」，会有张图告诉你如何构建

## 六、局限性

目前仍有两个无法实现的功能：

1. 1\. 图像理解能力（受限于 Cursor 实现）
2. 2\. 反爬虫绕过能力（仍在探索中）

## 结语

这个开源项目通过简单的改进，让更多开发者能够用更低的成本获得高级 AI 助手的能力，大家可以试一试！

#### 引用链接

`[1]` 开源项目地址: _https://github.com/grapeot/devin.cursorrules_  
`[2]` 项目作者的博客: _https://yage.ai/cursor-to-devin-en.html_  
`[3]` lookai.top: _https://www.lookai.top/en/cursor/instruction/instruction_  

[Read on Omnivore](https://omnivore.app/me/https-mp-weixin-qq-com-s-biz-mz-u-5-nj-mx-ote-5-oq-3-d-3-d-ascen-1943b24be70)
[Read Original](https://mp.weixin.qq.com/s?__biz=MzU5NjMxOTE5OQ%3D%3D&ascene=1&chksm=ff87e91f823ab5befa6a546995e47a1fff5b4f0d7a4c5f53ed98291c7e6820522c4cf2ea5624&clicktime=1736159032&countrycode=HR&devicetype=android-35&enterid=1736159032&exportkey=n_ChQIAhIQpDFIYbZ2VP4xeurIRw8RxBLiAQIE97dBBAEAAAAAALrGCMUyQpkAAAAOpnltbLcz9gKNyK89dVj0UJ3u%2BZm0rOTZOpI%2FPn75osftac4e74Nff7kUE54afwYA0nUmr1QGPwHYwyUSd442goVqyAd%2BqWJ2QYw1GhhdoZmUJRYvxlhG71zGSXub3aVthyTv9U4a%2BQvD8gXbI7DtMbmWvc9RZdjy9R%2FuWjoGCeGgQEDGrrEhkFvAsjQS8Rf3p%2FOcsfbVY%2FtGCqW07wd4Gfl3VJ7VktxohASM2OO9bNNpa08KyIX9gWw2DhW4RxfX8ZxwCUeI%2B6ewx%2Fo%3D&fasttmpl_flag=0&fasttmpl_fullversion=7543278-zh_CN-zip&fasttmpl_type=0&from=singlemessage&idx=1&lang=zh_CN&mid=2247487180&mpshare=1&nettype=gmtds&pass_ticket=1Q1DvHSHT%2BgXtiDbc1CNXS3WII1t1Fz621aUrljfpKIm3OXHpCkEpj37Q9X5xRZ7&realreporttime=1736159032067&scene=1&sessionid=1736157095&sharer_shareinfo=af96bec17dc223ff168367e038820617&sharer_shareinfo_first=af96bec17dc223ff168367e038820617&sn=66ad872e8ea2558b1040c196f7b26e31&srcid=0105sgKxo9YiYH8hIHt9u3eR&subscene=10000&version=2800373b&wx_header=3)

