购物车的订单在模拟支付之后，相对应的商品数量消失
我的购物历史中 对商品评价之后就直接提交 然后在我的评价列表中可以查看对应数据
目前 商品详情页点击转圈
这个是错误信息[自动热重载] 已开启代码文件保存后自动热重载  
index.js? [sm]:225 跳转到商品详情，ID: 2  
[自动热重载] 已开启代码文件保存后自动热重载  
product.js? [sm]:32 商品详情页参数: {id: "2", from: "index"}  
TypeError: this.loadProductDetail is not a function  
at bi.onLoad (product.js? [sm]:37)  
at bi.<anonymous> (WASubContext.js?t=wechat&s=1740672874842&v=3.7.8:1)  
at bi.c.__callPageLifeTime__ (WASubContext.js?t=wechat&s=1740672874842&v=3.7.8:1)  
at WASubContext.js?t=wechat&s=1740672874842&v=3.7.8:1  
at WASubContext.js?t=wechat&s=1740672874842&v=3.7.8:1  
at WASubContext.js?t=wechat&s=1740672874842&v=3.7.8:1  
at WASubContext.js?t=wechat&s=1740672874842&v=3.7.8:1  
at WASubContext.js?t=wechat&s=1740672874842&v=3.7.8:1  
at WASubContext.js?t=wechat&s=1740672874842&v=3.7.8:1  
at Function.kr (WASubContext.js?t=wechat&s=1740672874842&v=3.7.8:1)(env: Windows,mp,1.06.2412050; lib: 3.7.8)

还有就是每个应该显示商品图片信息的位置总是显示不出来