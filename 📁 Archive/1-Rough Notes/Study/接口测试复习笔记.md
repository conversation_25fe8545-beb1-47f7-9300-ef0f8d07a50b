---
created: 2024-01-02
aliases: [API Testing Review]
---

状态: [[复习]]
标签: [[接口测试]] [[软件测试]] [[HTTP协议]]

# 接口测试复习要点

## 一、基础概念
### 1.1 接口定义
- API：应用程序接口(Application Programming Interface)
- 作用：连接前端和后端的纽带，用于数据传递
- 本质：从功能看是黑盒，从代码看是函数/方法

### 1.2 接口类型
- WebService接口 (SOAP协议)
- WebSocket接口 (TCP/UDP)
- HTTP接口 (RESTful API)

## 二、HTTP协议重点
### 2.1 协议特点
- 无连接：请求完成即断开
- 无状态：不记忆之前的交互
- 简单：客户端发请求，服务器给响应

### 2.2 请求报文结构
1. 请求行
   - 请求方法(GET/POST等)
   - URL(接口地址)
   - 协议版本

2. 请求头
   - Content-Type常见类型：
     - application/x-www-form-urlencoded
     - application/json
     - multipart/form-data
   - 其他重要头：
     - Host
     - User-Agent
     - Cookie

3. 请求体(可选)

### 2.3 响应报文结构
1. 状态行
   - 状态码分类：
     - 1xx：信息
     - 2xx：成功
     - 3xx：重定向
     - 4xx：客户端错误
     - 5xx：服务器错误

2. 响应头
3. 响应体

### 2.4 GET和POST区别
- 数据长度：GET有限制，POST无限制
- 数据类型：GET限ASCII，POST无限制
- 安全性：POST较安全
- 缓存：GET会缓存，POST不缓存

## 三、用户识别机制
### 3.1 Cookie
- 特点：存储在客户端的文本信息
- 重要属性：
  - 过期时间(expires/Max-Age)
  - 路径(path)
  - HttpOnly
  - secure

### 3.2 Session
- 特点：服务器端存储
- 工作流程：
  - 服务器创建session并返回sessionID
  - 客户端请求时携带sessionID

### 3.3 Token
- 特点：不占服务器资源，安全性高
- 组成：uid + 时间戳 + 签名 + 盐值

## 四、测试要点
### 4.1 功能测试
- 参数验证
- 返回值验证
- 异常处理
- 业务流程验证

### 4.2 性能测试
- 响应时间
- 并发能力
- 稳定性

### 4.3 安全测试
- 参数校验
- 权限控制
- 加密传输

## 五、测试工具
### 5.1 Fiddler
- 用途：
  - 抓取HTTP报文
  - 模拟弱网环境
  - 篡改请求/响应
- 常用命令：
  - bpu：请求断点
  - bpafter：响应断点

### 5.2 Postman
- 接口调试
- 测试用例管理
- 自动化测试
- 环境配置

### 5.3 其他工具
- Python Requests
- JMeter
- Charles

## 六、考试重点提示
1. HTTP协议基础概念
2. 请求/响应报文结构
3. 状态码含义
4. Cookie/Session/Token区别
5. GET/POST方法区别
6. 测试方法和策略
7. 常见问题处理方法

## 七、重点简答题
### 7.1 HTTP协议相关
1. 简述HTTP协议的特点？
答：HTTP协议具有以下特点：
- 无连接：每次请求都要建立新的连接，请求完成后断开连接
- 无状态：协议对于事务处理没有记忆能力
- 简单快速：客户端向服务器请求服务时，只需传送请求方法和路径
- 灵活：允许传输任意类型的数据对象，由Content-Type标记

2. 请详细描述一次完整的HTTP请求过程？
答：完整过程如下：
1) 域名解析（DNS解析）
2) 建立TCP连接（三次握手）
3) 发送HTTP请求（请求行、请求头、请求体）
4) 服务器处理请求
5) 返回响应结果
6) 关闭TCP连接（四次挥手）
7) 浏览器解析HTML

3. Cookie、Session和Token的区别是什么？
答：三者区别如下：
- 存储位置：
  - Cookie：存储在客户端
  - Session：存储在服务器
  - Token：无需存储，每次请求时校验
- 安全性：
  - Cookie：较低，容易被截获
  - Session：较高，存在服务器
  - Token：最高，使用加密算法
- 性能：
  - Cookie：较好，存在客户端
  - Session：一般，占用服务器资源
  - Token：最好，不占用服务器资源

### 7.2 接口测试相关
1. 接口测试关注哪些方面？
答：主要关注以下方面：
- 功能性：接口是否按预期工作
- 正确性：返回值是否符合预期
- 异常处理：对异常情况的处理是否合理
- 安全性：权限控制、数据加密等
- 性能：响应时间、并发处理能力

2. 接口测试用例设计要点有哪些？
答：主要包括：
- 参数验证：必填、非必填、边界值、特殊字符
- 返回值验证：状态码、响应数据格式、业务数据正确性
- 异常场景：网络异常、超时、并发等
- 安全测试：SQL注入、XSS攻击等
- 业务流程：完整业务流程验证

### 7.3 Python相关知识点
#### 字符串操作
```python
# 1. 字符串基本操作
str1 = "Hello, API Testing"
print(len(str1))  # 长度
print(str1.upper())  # 转大写
print(str1.lower())  # 转小写
print(str1.split(','))  # 分割
print(str1.strip())  # 去除首尾空格

# 2. 字符串格式化
name = "API"
age = 20
print("Name: %s, Age: %d" % (name, age))  # 老式格式化
print("Name: {}, Age: {}".format(name, age))  # format格式化
print(f"Name: {name}, Age: {age}")  # f-string格式化

# 3. 字符串查找替换
text = "api testing is important"
print(text.find('test'))  # 查找子串位置
print(text.replace('api', 'API'))  # 替换
print('test' in text)  # 判断包含

# 4. 实际应用示例
def parse_response(response_text):
    # 去除空白字符
    response_text = response_text.strip()
    # 分割响应数据
    lines = response_text.split('\n')
    # 解析每行数据
    result = {}
    for line in lines:
        if ':' in line:
            key, value = line.split(':', 1)
            result[key.strip()] = value.strip()
    return result
```

#### JSON处理
```python
import json

# 1. 字典转JSON
data = {
    'name': 'API Test',
    'method': 'POST',
    'params': {'id': 1}
}
json_str = json.dumps(data)

# 2. JSON转字典
json_data = '{"status": 200, "message": "success"}'
dict_data = json.loads(json_data)

# 3. 实际应用
def handle_response(response):
    try:
        data = json.loads(response.text)
        return data.get('status') == 200
    except json.JSONDecodeError:
        return False
```

#### 常见考题
1. 如何实现字符串反转？
```python
# 方法1：切片
text = "hello"
reversed_text = text[::-1]

# 方法2：reversed函数
reversed_text = ''.join(reversed(text))
```

2. 如何统计字符串中每个字符的出现次数？
```python
from collections import Counter

text = "hello world"
# 方法1：Counter
char_count = Counter(text)

# 方法2：字典
count_dict = {}
for char in text:
    count_dict[char] = count_dict.get(char, 0) + 1
```

3. 如何判断字符串是否是回文？
```python
def is_palindrome(text):
    # 去除空格和转小写
    text = text.replace(" ", "").lower()
    return text == text[::-1]
```

### 7.4 接口测试实战题
1. 使用Python实现一个简单的接口测试框架
```python
import requests
import json

class APITest:
    def __init__(self, base_url):
        self.base_url = base_url
        self.session = requests.Session()
    
    def send_request(self, method, endpoint, data=None, headers=None):
        url = self.base_url + endpoint
        response = self.session.request(
            method=method,
            url=url,
            json=data,
            headers=headers
        )
        return response
    
    def assert_status_code(self, response, expected_code):
        assert response.status_code == expected_code, \
            f"Expected {expected_code}, but got {response.status_code}"
    
    def assert_response_data(self, response, key, expected_value):
        data = response.json()
        assert data.get(key) == expected_value, \
            f"Expected {expected_value}, but got {data.get(key)}"

# 使用示例
def test_login_api():
    api = APITest("http://api.example.com")
    data = {
        "username": "test",
        "password": "123456"
    }
    response = api.send_request("POST", "/login", data=data)
    api.assert_status_code(response, 200)
    api.assert_response_data(response, "message", "success")
```

# 考试重点提示
1. 掌握HTTP协议基础知识，特别是报文结构和状态码
2. 理解Cookie、Session、Token的区别和应用场景
3. 熟练掌握Python字符串操作和JSON处理
4. 能够编写基本的接口测试代码
5. 理解接口测试的重点和测试用例设计方法

# 复习建议
1. 多动手实践，特别是Python代码部分
2. 理解原理，不要死记硬背
3. 做好笔记，归纳总结
4. 多看实际案例，理解应用场景 