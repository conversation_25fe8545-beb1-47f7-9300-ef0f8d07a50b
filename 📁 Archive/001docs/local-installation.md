# DeepTask 本地安装指南

本文档详细说明如何在本地构建和全局安装 DeepTask，以便在任意项目目录中使用。

## 🎯 安装目标

- 全局安装 `deeptask` 命令，可在任意目录使用
- 支持 `npx deeptask-mcp` 启动 MCP 服务器
- 本地构建，无需依赖 npm 仓库
- 跨平台支持（Windows/macOS/Linux）

## 📋 前置要求

- Node.js 18.0.0 或更高版本
- npm（随 Node.js 安装）
- Git（用于克隆仓库）

## 🚀 安装步骤

### 1. 克隆仓库

```bash
# 克隆项目到本地
git clone https://github.com/luQingU/deeptask.git
cd deeptask
```

### 2. 安装依赖

```bash
# 安装项目依赖
npm install
```

### 3. 本地构建和全局安装

```bash
# 运行本地构建脚本
node scripts/build-local.js
```

构建脚本会自动：
- 设置可执行文件权限
- 检查是否已有全局安装
- 卸载旧版本（如果存在）
- 全局链接新版本
- 测试安装是否成功

### 4. 验证安装

```bash
# 检查版本
deeptask --version

# 查看帮助
deeptask --help

# 测试 MCP 服务器
npx deeptask-mcp --help
```

## 🔧 配置 API 密钥

### 方法一：环境变量文件（推荐）

在任意项目目录中创建 `.env` 文件：

```bash
# 复制示例配置
cp /path/to/deeptask/assets/env.example .env

# 编辑配置文件
# 添加您的 API 密钥
DEEPSEEK_API_KEY=your_deepseek_key_here
SILICONFLOW_API_KEY=your_siliconflow_key_here
GROQ_API_KEY=your_groq_key_here
```

### 方法二：全局环境变量

```bash
# Windows (PowerShell)
$env:DEEPSEEK_API_KEY="your_key_here"

# macOS/Linux (Bash)
export DEEPSEEK_API_KEY="your_key_here"
```

## 📱 使用方法

### 命令行使用

```bash
# 在任意目录初始化项目
deeptask init

# 配置 AI 模型
deeptask models --setup

# 解析 PRD 生成任务
deeptask parse-prd your-prd.txt

# 查看任务列表
deeptask list

# 获取下一个任务
deeptask next
```

### Cursor 集成

在项目的 `.cursor/mcp.json` 中配置：

```json
{
  "mcpServers": {
    "deeptask": {
      "command": "npx",
      "args": ["deeptask-mcp"],
      "env": {
        "DEEPSEEK_API_KEY": "your_key_here",
        "SILICONFLOW_API_KEY": "your_key_here",
        "GROQ_API_KEY": "your_key_here"
      }
    }
  }
}
```

## 🔄 更新和维护

### 更新到最新版本

```bash
# 进入 DeepTask 源码目录
cd /path/to/deeptask

# 拉取最新代码
git pull origin master

# 重新安装依赖
npm install

# 重新构建和安装
node scripts/build-local.js
```

### 卸载

```bash
# 卸载全局安装
npm unlink -g deeptask

# 或者使用构建脚本中的提示
# 构建脚本会在检测到已安装时提供卸载选项
```

## 🐛 故障排除

### 常见问题

1. **命令未找到**
   ```bash
   # 检查全局安装
   npm list -g deeptask
   
   # 重新链接
   cd /path/to/deeptask
   npm link
   ```

2. **权限错误（Linux/macOS）**
   ```bash
   # 确保文件可执行
   chmod +x bin/deeptask.js
   chmod +x mcp-server/server.js
   ```

3. **API 密钥问题**
   ```bash
   # 检查环境变量
   echo $DEEPSEEK_API_KEY
   
   # 或在 Windows
   echo $env:DEEPSEEK_API_KEY
   ```

4. **MCP 服务器启动失败**
   ```bash
   # 直接测试
   npx deeptask-mcp
   
   # 检查依赖
   npm install
   ```

### 调试模式

```bash
# 启用调试输出
export DEBUG=deeptask*

# 运行命令查看详细信息
deeptask list --debug
```

## 📚 相关文档

- [配置指南](configuration.md) - 详细的配置选项
- [教程](tutorial.md) - 完整的使用教程
- [MCP设置指南](../cursor/rules/mcp_setup_guide.mdc) - Cursor集成详细步骤

## 💡 最佳实践

1. **版本管理**：定期更新到最新版本以获得最新功能和修复
2. **API 密钥管理**：建议在每个项目目录创建独立的 `.env` 文件
3. **版本控制**：将 `.env` 文件添加到 `.gitignore` 中

---

如有问题，请在 [GitHub Issues](https://github.com/luQingU/deeptask/issues) 中报告。
