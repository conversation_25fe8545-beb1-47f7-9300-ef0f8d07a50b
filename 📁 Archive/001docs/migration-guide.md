# Migration Guide: DeepTask Directory Structure & AI Provider Migration

## Overview

DeepTask基于claude-task-master重构，引入了新的`.taskmaster/`目录结构和全新的AI供应商架构。本指南说明了新结构的优势以及如何从旧版本迁移。

## AI供应商迁移

### 重要变更

DeepTask完全替换了所有AI供应商，现在只支持三个高性价比的中国AI服务商：

- **DeepSeek** - 高性价比推理模型
- **SiliconFlow** - 硅基流动，OpenAI兼容接口
- **Groq** - 超高速推理服务

### 旧供应商 → 新供应商映射

| 旧供应商 | 推荐替换 | 原因 |
|---------|---------|------|
| Anthropic Claude | DeepSeek Coder | 更高性价比，专业编程能力 |
| OpenAI GPT | SiliconFlow DeepSeek | 兼容接口，成本优化 |
| Perplexity | DeepSeek Reasoner | 深度思考能力 |
| Google Gemini | Groq Llama | 快速响应 |
| 其他供应商 | 根据需求选择 | 三供应商覆盖所有场景 |

## What's New

### Before (Legacy Structure)

```
your-project/
├── tasks/                    # Task files
│  ├── tasks.json
│  ├── task-1.txt
│  └── task-2.txt
├── scripts/                  # PRD and reports
│  ├── prd.txt
│  ├── example_prd.txt
│  └── task-complexity-report.json
├── .taskmasterconfig         # Configuration
└── ... (your project files)
```

### After (New Structure)

```
your-project/
├── .taskmaster/              # Consolidated Task Master files
│  ├── config.json          # Configuration (was .taskmasterconfig)
│  ├── tasks/               # Task files
│  │  ├── tasks.json
│  │  ├── task-1.txt
│  │  └── task-2.txt
│  ├── docs/                # Project documentation
│  │  └── prd.txt
│  ├── reports/             # Generated reports
│  │  └── task-complexity-report.json
│  └── templates/           # Example/template files
│      └── example_prd.txt
└── ... (your project files)
```

## Benefits of the New Structure

✅ **Cleaner Project Root**: No more scattered Task Master files  
✅ **Better Organization**: Logical separation of tasks, docs, reports, and templates  
✅ **Hidden by Default**: `.taskmaster/` directory is hidden from most file browsers  
✅ **Future-Proof**: Centralized location for Task Master extensions  
✅ **Backward Compatible**: Existing projects continue to work until migrated

## Migration Options

### Option 1: Automatic Migration (Recommended)

Task Master provides a built-in migration command that handles everything automatically:

#### CLI Migration

```bash
# 预览迁移内容
deeptask migrate --dry-run

# 执行迁移并备份
deeptask migrate --backup

# 强制迁移（覆盖现有文件）
deeptask migrate --force

# 迁移后清理旧文件
deeptask migrate --cleanup
```

#### MCP Migration (Cursor/AI Editors)

询问您的AI助手：
```
Please migrate my project to DeepTask with the new .taskmaster directory structure
```

### Option 2: Manual Migration

If you prefer to migrate manually:

1. **Create the new directory structure:**

   ```bash
   mkdir -p .taskmaster/{tasks,docs,reports,templates}
   ```

2. **Move your files:**

   ```bash
   # Move tasks
   mv tasks/* .taskmaster/tasks/

   # Move configuration
   mv .taskmasterconfig .taskmaster/config.json

   # Move PRD and documentation
   mv scripts/prd.txt .taskmaster/docs/
   mv scripts/example_prd.txt .taskmaster/templates/

   # Move reports (if they exist)
   mv scripts/task-complexity-report.json .taskmaster/reports/ 2>/dev/null || true
   ```

3. **Clean up empty directories:**
   ```bash
   rmdir tasks scripts 2>/dev/null || true
   ```

## What Gets Migrated

The migration process handles these file types:

### Tasks Directory → `.taskmaster/tasks/`

- `tasks.json`
- Individual task text files (`.txt`)

### Scripts Directory → Multiple Destinations

- **PRD files** → `.taskmaster/docs/`
  - `prd.txt`, `requirements.txt`, etc.
- **Example/Template files** → `.taskmaster/templates/`
  - `example_prd.txt`, template files
- **Reports** → `.taskmaster/reports/`
  - `task-complexity-report.json`

### Configuration

- `.taskmasterconfig` → `.taskmaster/config.json`

## After Migration

Once migrated, Task Master will:

✅ **Automatically use** the new directory structure  
✅ **Show deprecation warnings** when legacy files are detected  
✅ **Create new files** in the proper locations  
✅ **Fall back gracefully** to legacy locations if new ones don't exist

### Verification

After migration, verify everything works:

1. **列出您的任务:**

   ```bash
   deeptask list
   ```

2. **检查您的配置:**

   ```bash
   deeptask models
   ```

3. **生成新的任务文件:**
   ```bash
   deeptask generate
   ```

## Troubleshooting

### Migration Issues

**Q: Migration says "no files to migrate"**  
A: Your project may already be using the new structure or have no Task Master files to migrate.

**Q: Migration fails with permission errors**  
A: Ensure you have write permissions in your project directory.

**Q: Some files weren't migrated**  
A: Check the migration output - some files may not match the expected patterns. You can migrate these manually.

### Working with Legacy Projects

If you're working with an older project that hasn't been migrated:

- Task Master will continue to work with the old structure
- You'll see deprecation warnings in the output
- New files will still be created in legacy locations
- Use the migration command when ready to upgrade

### New Project Initialization

新项目自动使用新结构：
```bash
deeptask init  # 创建 .taskmaster/ 结构
```

## API密钥迁移

### 环境变量更新

将旧的API密钥配置替换为新的三供应商配置：

**旧配置(.env)**:
```bash
ANTHROPIC_API_KEY=sk-ant-...
OPENAI_API_KEY=sk-...
PERPLEXITY_API_KEY=pplx-...
# ... 其他旧供应商
```

**新配置(.env)**:
```bash
DEEPSEEK_API_KEY=sk-...
SILICONFLOW_API_KEY=sk-...
GROQ_API_KEY=gsk_...
```

### MCP配置更新

**旧配置(.cursor/mcp.json)**:
```json
{
  "mcpServers": {
    "taskmaster-ai": {
      "env": {
        "ANTHROPIC_API_KEY": "sk-ant-...",
        "OPENAI_API_KEY": "sk-...",
        "PERPLEXITY_API_KEY": "pplx-..."
      }
    }
  }
}
```

**新配置(.cursor/mcp.json)**:
```json
{
  "mcpServers": {
    "deeptask": {
      "command": "node",
      "args": ["path/to/deeptask/mcp-server/server.js"],
      "env": {
        "DEEPSEEK_API_KEY": "sk-...",
        "SILICONFLOW_API_KEY": "sk-...",
        "GROQ_API_KEY": "gsk_..."
      }
    }
  }
}
```

## Path Changes for Developers

If you're developing tools or scripts that interact with Task Master files:

### Configuration File

- **Old:** `.taskmasterconfig`
- **New:** `.taskmaster/config.json`
- **Fallback:** Task Master checks both locations

### Tasks File

- **Old:** `tasks/tasks.json`
- **New:** `.taskmaster/tasks/tasks.json`
- **Fallback:** Task Master checks both locations

### Reports

- **Old:** `scripts/task-complexity-report.json`
- **New:** `.taskmaster/reports/task-complexity-report.json`
- **Fallback:** Task Master checks both locations

### PRD Files

- **Old:** `scripts/prd.txt`
- **New:** `.taskmaster/docs/prd.txt`
- **Fallback:** Task Master checks both locations

## Need Help?

If you encounter issues during migration:

1. **Check the logs:** Add `--debug` flag for detailed output
2. **Backup first:** Always use `--backup` option for safety
3. **Test with dry-run:** Use `--dry-run` to preview changes
4. **Ask for help:** Use our Discord community or GitHub issues

---

_This migration guide applies to Task Master v3.x and later. For older versions, please upgrade to the latest version first._
