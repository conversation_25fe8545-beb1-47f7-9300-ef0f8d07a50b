# Configuration

Taskmaster uses two primary methods for configuration:

1.  **`.taskmaster/config.json` File (Recommended - New Structure)**

    - This JSON file stores most configuration settings, including AI model selections, parameters, logging levels, and project defaults.
    - **Location:** This file is created in the `.taskmaster/` directory when you run the `task-master models --setup` interactive setup or initialize a new project with `task-master init`.
    - **Migration:** Existing projects with `.taskmasterconfig` in the root will continue to work, but should be migrated to the new structure using `task-master migrate`.
    - **Management:** Use the `task-master models --setup` command (or `models` MCP tool) to interactively create and manage this file. You can also set specific models directly using `task-master models --set-<role>=<model_id>`, adding `--ollama` or `--openrouter` flags for custom models. Manual editing is possible but not recommended unless you understand the structure.
    - **Example Structure:**
      ```json
      {
        "models": {
          "main": {
            "provider": "deepseek",
            "modelId": "deepseek-coder",
            "maxTokens": 64000,
            "temperature": 0.2
          },
          "research": {
            "provider": "deepseek",
            "modelId": "deepseek-reasoner",
            "maxTokens": 64000,
            "temperature": 0.1
          },
          "fallback": {
            "provider": "siliconflow",
            "modelId": "deepseek-chat",
            "maxTokens": 64000,
            "temperature": 0.2
          }
        },
        "global": {
          "logLevel": "info",
          "debug": false,
          "defaultSubtasks": 5,
          "defaultPriority": "medium",
          "projectName": "Your Project Name"
        }
      }
      ```

2.  **Legacy `.taskmasterconfig` File (Backward Compatibility)**

    - For projects that haven't migrated to the new structure yet.
    - **Location:** Project root directory.
    - **Migration:** Use `task-master migrate` to move this to `.taskmaster/config.json`.
    - **Deprecation:** While still supported, you'll see warnings encouraging migration to the new structure.

## Environment Variables (`.env` file or MCP `env` block - For API Keys Only)

- Used **exclusively** for sensitive API keys and specific endpoint URLs.
- **Location:**
  - For CLI usage: Create a `.env` file in your project root.
  - For MCP/Cursor usage: Configure keys in the `env` section of your `.cursor/mcp.json` file.
- **Required API Keys (Depending on configured providers):**
  - `DEEPSEEK_API_KEY`: Your DeepSeek API key (format: sk-...).
  - `SILICONFLOW_API_KEY`: Your SiliconFlow API key (format: sk-...).
  - `GROQ_API_KEY`: Your Groq API key (format: gsk_...).
- **Optional Endpoint Overrides:**
  - **Per-role `baseURL` in `.taskmaster/config.json`:** You can add a `baseURL` property to any model role (`main`, `research`, `fallback`) to override the default API endpoint for that provider. If omitted, the provider's standard endpoint is used.

**Important:** Settings like model ID selections (`main`, `research`, `fallback`), `maxTokens`, `temperature`, `logLevel`, `defaultSubtasks`, `defaultPriority`, and `projectName` are **managed in `.taskmaster/config.json`** (or `.taskmasterconfig` for unmigrated projects), not environment variables.

## Example `.env` File (for API Keys)

```
# Required API keys for DeepTask providers
DEEPSEEK_API_KEY=sk-your-deepseek-key-here
SILICONFLOW_API_KEY=sk-your-siliconflow-key-here
GROQ_API_KEY=gsk_your-groq-key-here

# Note: At least one API key is required for DeepTask to function
# You can comment out providers you don't plan to use
```

## Troubleshooting

### Configuration Errors

- If Task Master reports errors about missing configuration or cannot find the config file, run `task-master models --setup` in your project root to create or repair the file.
- For new projects, config will be created at `.taskmaster/config.json`. For legacy projects, you may want to use `task-master migrate` to move to the new structure.
- Ensure API keys are correctly placed in your `.env` file (for CLI) or `.cursor/mcp.json` (for MCP) and are valid for the providers selected in your config file.

### If `task-master init` doesn't respond:

Try running it with Node directly:

```bash
node node_modules/claude-task-master/scripts/init.js
```

Or clone the repository and run:

```bash
git clone https://github.com/eyaltoledano/claude-task-master.git
cd claude-task-master
node scripts/init.js
```

## Provider-Specific Configuration

### DeepSeek Configuration

DeepSeek是中国领先的AI公司，提供高性价比的推理模型：

1. **获取API密钥**:
   - 访问 [platform.deepseek.com](https://platform.deepseek.com/)
   - 注册账号并完成实名认证
   - 在API密钥页面创建新密钥
2. **配置示例**:
   ```bash
   # In .env file
   DEEPSEEK_API_KEY=sk-your-deepseek-key-here
   ```

### SiliconFlow Configuration

SiliconFlow（硅基流动）提供OpenAI兼容的接口，成本优化：

1. **获取API密钥**:
   - 访问 [cloud.siliconflow.cn](https://cloud.siliconflow.cn/)
   - 注册账号并完成认证
   - 在控制台创建API密钥
2. **配置示例**:
   ```bash
   # In .env file
   SILICONFLOW_API_KEY=sk-your-siliconflow-key-here
   ```

### Groq Configuration

Groq提供超高速推理服务，适合快速响应场景：

1. **获取API密钥**:
   - 访问 [console.groq.com](https://console.groq.com/)
   - 使用Google或GitHub账号登录
   - 在API Keys页面创建新密钥
2. **配置示例**:
   ```bash
   # In .env file
   GROQ_API_KEY=gsk_your-groq-key-here
   ```
