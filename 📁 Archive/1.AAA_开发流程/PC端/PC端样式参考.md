# 静态原型样式要求 (Art-Design-Pro风格参考)

创建基于 art-design-pro 设计风格的企业级中后台系统时，请参照以下样式规范和要求来实现视觉效果：

1.  **整体设计风格**:
    *   遵循 art-design-pro 简约高效、体现现代感和专业性的企业级设计语言。
    *   实现清晰的信息层级与视觉引导。
    *   精心调整留白设计和间距系统，遵循 art-design-pro 的间距规范。
    *   采用统一的卡片式模块化布局，卡片应带有精致的阴影或边框。
    *   复刻 art-design-pro 特有的表格与表单设计风格。
    *   展现专业的数据可视化展示风格（静态样式）。
    *   遵循 art-design-pro 的颜色系统和主题配色。
    *   模拟细腻的微交互与反馈机制（如悬停、点击状态的样式变化）。
    *   支持浅色主题和暗黑主题的样式切换能力（需在结构上预留支持）。

2.  **色彩系统**:
    *   **主色调**: 参照 art-design-pro 的蓝色主题（例如使用变量 `--art-primary`）。
    *   **辅助色**: 参照 art-design-pro 变量系统中的 `--art-secondary`, `--art-success`, `--art-warning`, `--art-error` 等颜色。
    *   **中性色**: 参照 art-design-pro 的灰度色阶（`--art-gray-100` 至 `--art-gray-900`）。
    *   **背景色**: 参照 art-design-pro 的背景色系统（`--art-bg-color`, `--art-main-bg-color`）。
    *   **实现时应使用 SCSS 变量来管理这些颜色。**

3.  **页面设计规格**:
    *   主要设计尺寸参考：1920×1080px。
    *   最小兼容尺寸参考：1366×768px。
    *   实现响应式布局，布局样式需考虑不同尺寸的适配。

4.  **组件风格**:
    *   **导航菜单**: 参照 art-design-pro 的菜单样式。
    *   **表格组件**: 参照 art-design-pro 的表格设计，包括表头、单元格、行高、斑马纹、hover 效果、阴影以及分页、筛选区域的样式。
    *   **表单组件**: 参照 art-design-pro 的输入框、选择框、按钮、标签等表单元素的样式、布局和对齐方式。
    *   **卡片组件**: 使用带有 art-design-pro 风格阴影 (`--art-box-shadow` 系列变量) 或精致边框的卡片样式。
    *   **按钮组件**: 遵循 art-design-pro 的按钮样式层级和不同状态的样式。
    *   **实现时应优先通过 Element UI 的样式覆盖机制和 SCSS 变量来达到这些组件的视觉效果。**

5.  **资源引用**:
    *   **图标**: 使用与 art-design-pro 风格高度一致的图标样式。
    *   **字体**: 使用 art-design-pro 推荐的 `DMSans` 和 `Montserrat` 字体（若项目已配置）。
    *   **阴影效果**: 参照 art-design-pro 的阴影变量（`--art-box-shadow` 系列）。
    *   **实现时应将这些资源引用或其对应的变量进行管理。**