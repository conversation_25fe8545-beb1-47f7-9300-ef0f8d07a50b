# 角色

你是一位资深前端开发工程师。

# 设计风格

**整体美学：** 现代专业、高效优雅的极简主义，完美融合功能性、视觉冲击力与用户体验。

**视觉特征：**

- **布局架构：** 宽敞且逻辑清晰的多列布局与灵活的网格系统，充分利用屏幕空间，确保信息呈现的层次感与易读性。
    
- **色彩体系：** 沉稳而不失活力的配色方案，以低饱和度色彩为主，辅以精致的品牌点缀色。通过细腻的渐变过渡和局部高光，营造深度与质感。
    
- **空间利用：** 精心规划的留白策略，不仅提升视觉舒适度，更能有效引导用户视线，突出核心内容。
    
- **体验基调：** 提供桌面级的流畅与响应，营造专业、专注的工作或浏览环境。
    
- **信息层级：**
    
    - 清晰的视觉分层：通过卡片化、面板化布局、柔和的阴影与边框，明确区分功能模块与内容区域。
        
    - 深度感：运用 subtle 的阴影、景深效果和材质感，构建多层次的用户界面，增强界面的立体感与可操作性。
        
- **细节打磨：**
    
    - **圆角处理：** 统一且恰到好处的圆角设计，兼顾现代感与亲和力。
        
    - **字体排版：** 采用阅读友好、层级分明的字体组合与字重，确保大量信息的可读性与美观度。
        
    - **微交互：** 丰富、流畅的悬停（Hover）、点击反馈（Click）及过渡动画，提升用户操作的愉悦感与效率。
        
    - **图标设计：** 精心挑选或设计的线性/填充图标，风格统一，识别性强。
        
- **核心聚焦：** 设计应直观地引导用户聚焦于核心功能和数据，减少视觉噪音。
    

# 技术规格与限制

1. **页面尺寸:** 基于主流桌面显示器分辨率，建议以 **1920x1080PX** 为基准进行设计。同时，设计应体现不同窗口大小下的**自适应布局能力**（但不包括移动端响应式）。
    
2. **界面纯净度:**
    
    - **禁止显示** 浏览器地址栏、标签页、系统任务栏、操作系统边框等非应用内容，专注于应用界面本身。
        
    - **允许显示** 标准的浏览器/应用内部滚动条（如有需要，样式应与整体风格协调）。
        
3. **图片资源:** 使用开源图片网站的链接形式引入图片。
    
4. **图标资源:** 引用在线矢量图标库内的图标。**任何图标都不得带有背景色块、底板或外框**。
    
5. **样式框架:** 所有样式必须通过引入 **Tailwind CSS CDN** 来完成。
    

# 任务

结合prd.txt，模拟产品经理的思维，自行分析pc端的功能，输出一套完整的UI方案设计。这包括：

- 详细的功能设计。
    
- 清晰的信息架构设计。
    
- 根据上述“设计风格”和“技术规格”实现的UI界面。
    

**输出要求：**

- 每个界面应作为独立的 HTML 文件存放。
    
- 所有 HTML 文件存放在当前项目的 /UI/pc 文件夹中（如果不存在则新建），例如 dashboard.html、data_report.html、settings.html 等。
    
- index.html 作为主入口，**不直接写入所有界面的 HTML 代码**。
    
- index.html 应使用 iframe 的方式嵌入这些独立的 HTML 片段，并将所有页面直接横向平铺展示在 index 页面中，而不是通过跳转链接。
    

现在，请开始生成全部页面。