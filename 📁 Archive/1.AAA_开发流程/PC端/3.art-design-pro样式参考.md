# Art-Design-Pro样式系统参考指南

本文档提取了art-design-pro框架的核心样式系统，作为静态原型开发的参考。使用这些样式变量和组件规范，可以确保你创建的静态原型与art-design-pro的设计风格保持高度一致。

## 颜色系统

### 主题色

```css
/* 主题色变量 */
--art-primary: 93, 135, 255;     /* 主色调 - 蓝色 */
--art-secondary: 73, 190, 255;   /* 次要色 - 浅蓝 */
--art-error: 250, 137, 107;      /* 错误色 */
--art-info: 83, 155, 255;        /* 信息色 */
--art-success: 19, 222, 185;     /* 成功色 */
--art-warning: 255, 174, 31;     /* 警告色 */
--art-danger: 255, 77, 79;       /* 危险色 */
```

### 背景色

```css
/* 主题背景色变量 */
--art-bg-primary: 236, 242, 255;   /* 主色调背景 */
--art-bg-secondary: 232, 247, 255; /* 次要色背景 */
--art-bg-success: 230, 255, 250;   /* 成功色背景 */
--art-bg-error: 253, 237, 232;     /* 错误色背景 */
--art-bg-info: 235, 243, 254;      /* 信息色背景 */
--art-bg-warning: 254, 245, 229;   /* 警告色背景 */
--art-bg-danger: 253, 237, 232;    /* 危险色背景 */

/* 全局背景色 */
--art-bg-color: #fafbfc;         /* 最底部背景颜色 */
--art-main-bg-color: #ffffff;    /* 主背景颜色 */
```

### 灰度色阶

```css
/* 灰度色阶 */
--art-gray-100: #f9f9f9;
--art-gray-200: #f1f1f4;
--art-gray-300: #dbdfe9;
--art-gray-400: #c4cada;
--art-gray-500: #99a1b7;
--art-gray-600: #78829d;
--art-gray-700: #4b5675;
--art-gray-800: #252f4a;
--art-gray-900: #071437;
```

### 文本颜色

```css
/* 文本颜色 */
--art-text-muted: #99a1b7;
--art-text-gray-100: #f9f9f9;
--art-text-gray-200: #f1f1f4;
--art-text-gray-300: #dbdfe9;
--art-text-gray-400: #c4cada;
--art-text-gray-500: #99a1b7;
--art-text-gray-600: #78829d;
--art-text-gray-700: #4b5675;
--art-text-gray-800: #252f4a;
--art-text-gray-900: #071437;
```

### 边框颜色

```css
/* 边框颜色 */
--art-border-color: #eaebf1;
--art-border-dashed-color: #dbdfe9;
--art-root-card-border-color: #f1f1f4;
```

### 阴影效果

```css
/* 阴影效果 */
--art-box-shadow-xs: 0 0.1rem 0.75rem 0.25rem rgba(0, 0, 0, 0.05);
--art-box-shadow-sm: 0 0.1rem 1rem 0.25rem rgba(0, 0, 0, 0.05);
--art-box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, 0.075);
--art-box-shadow-lg: 0 1rem 2rem 1rem rgba(0, 0, 0, 0.1);

/* 卡片阴影 */
--art-root-card-box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.03);
--art-card-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.04);
```

## 暗黑主题

art-design-pro支持暗黑主题，以下是暗黑主题的核心变量：

```css
html.dark {
  /* 背景颜色 */
  --art-bg-color: #070707;
  --art-main-bg-color: #161618;
  
  /* 卡片背景与边框 */
  --art-border-color: #26272f;
  --art-root-card-border-color: #1e2027;
  
  /* 灰度色阶 */
  --art-gray-100: #1b1c22;
  --art-gray-900: #f5f5f5;
}
```

## 字体系统

art-design-pro使用以下字体：

```css
/* 主要字体 */
font-family: 'DMSans', sans-serif;

/* 辅助字体 */
font-family: 'Montserrat', sans-serif;
```

## 布局系统

### 间距规范

```css
/* 通用间距变量 */
--console-margin: 20px; /* 工作台盒子间距 */
```

### 响应式断点

```css
/* 响应式断点 */
$device-notebook: 1600px;  /* 笔记本电脑 */
$device-ipad-pro: 1180px;  /* iPad Pro */
$device-ipad: 800px;       /* iPad */
$device-phone: 500px;      /* 手机 */
```

## 组件样式指南

### 卡片组件

卡片是art-design-pro中最常用的容器组件，有以下几种样式：

1. **标准卡片**
   - 白色背景
   - 轻微阴影或边框
   - 圆角：8px
   - 内边距：20px

2. **边框模式卡片**
   ```css
   .art-custom-card {
     border: 1px solid var(--art-card-border);
   }
   ```

3. **阴影模式卡片**
   ```css
   .art-custom-card {
     box-shadow: var(--art-card-shadow);
     border: 1px solid rgba(var(--art-gray-300-rgb), 0.3);
   }
   ```

### 按钮系统

按钮有多种样式和状态：

1. **主要按钮**
   ```css
   .art-button-primary {
     background-color: rgb(var(--art-primary));
     color: white;
   }
   ```

2. **次要按钮**
   ```css
   .art-button-secondary {
     background-color: rgb(var(--art-secondary));
     color: white;
   }
   ```

3. **文本按钮**
   ```css
   .art-button-text {
     color: rgb(var(--art-primary));
     background: transparent;
   }
   ```

### 表格样式

表格组件特点：

1. 表头背景色：轻微区分于内容区
2. 边框：通常很淡或不可见
3. 条纹样式：奇偶行区分
4. 悬停效果：背景色变化

### 表单样式

表单组件特点：

1. 输入框高度：40px左右
2. 标签位置：默认在上方
3. 聚焦状态：边框颜色变为主题色
4. 验证状态：不同状态有不同的边框颜色

## 常用工具类

```css
/* 背景色工具类 */
.bg-primary { background-color: rgb(var(--art-bg-primary)) !important; color: rgb(var(--art-primary)) !important; }
.bg-secondary { background-color: rgb(var(--art-bg-secondary)) !important; color: rgb(var(--art-secondary)) !important; }
.bg-success { background-color: rgb(var(--art-bg-success)) !important; color: rgb(var(--art-success)) !important; }
.bg-error { background-color: rgb(var(--art-bg-error)) !important; color: rgb(var(--art-error)) !important; }
.bg-warning { background-color: rgb(var(--art-bg-warning)) !important; color: rgb(var(--art-warning)) !important; }
.bg-danger { background-color: rgb(var(--art-bg-danger)) !important; color: rgb(var(--art-danger)) !important; }

/* 文本颜色工具类 */
.text-primary { color: rgb(var(--art-primary)) !important; }
.text-secondary { color: rgb(var(--art-secondary)) !important; }
.text-success { color: rgb(var(--art-success)) !important; }
.text-error { color: rgb(var(--art-error)) !important; }
.text-warning { color: rgb(var(--art-warning)) !important; }
.text-danger { color: rgb(var(--art-danger)) !important; }
```

## 静态原型实现步骤

1. 创建基础HTML结构，包含art-design-pro的颜色变量系统
2. 实现基础布局结构（顶部导航、侧边菜单、内容区）
3. 添加卡片、表格、表单等核心组件
4. 实现基本交互（如菜单切换、表单验证等）
5. 添加响应式支持

## 完整CSS变量参考文件

为方便使用，建议创建一个`art-variables.css`文件，包含以上所有变量，并在HTML文件中引用该文件。这样可以确保所有页面使用统一的样式变量。 