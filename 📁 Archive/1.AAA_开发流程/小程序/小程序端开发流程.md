# 小程序端开发流程

## 一、前期准备

### 1. 需求分析
- 与客户沟通，确认小程序的核心功能需求
- 明确目标用户群体和使用场景
- 确定功能优先级和开发阶段划分
- 形成需求分析文档

### 2. 技术选型
- 基于Vue2.x的uni-app框架
- 不使用第三方组件库，仅使用uni-icon组件
- 后端采用Java SpringBoot + Maven + MySQL 5.7.37 + JDK 1.8
- 制定代码规范和命名约定

### 3. 界面设计
- 参考`小程序手机端前端设计.md`中的设计规范
- 单个页面尺寸375x812px
- 使用开源图片网站链接引入图片
- 引入uni-icon组件作为图标库
- 设计遵循现代优雅的极简主义美学

## 二、开发准备

### 1. 开发环境搭建
- 安装HBuilderX作为开发IDE
- 安装微信开发者工具
- 配置uni-app开发环境
- 安装必要的编译和调试工具

### 2. 项目初始化
- 使用HBuilderX创建uni-app项目
- 配置项目基本结构
- 安装uni-icon组件
- 配置基础样式和主题

### 3. 版本控制
- 创建Git仓库
- 设置.gitignore文件
- 建立开发分支策略
- 设置Cursor记忆库 (使用`cursor rules.md`中的指南)
  - 执行`init memory bank`初始化记忆库
  - 适时使用`update memory bank`更新记忆库

## 三、前端开发流程

### 1. UI原型开发
- 根据`小程序手机端前端设计.md`创建静态HTML原型
- 将每个界面作为独立HTML文件存放在UI文件夹中
- 使用iframe在index.html中展示所有页面
- 确保UI符合设计规范

### 2. 转换为uni-app代码
- 根据`前端转向开发uniapp.md`指南进行开发
- 参考HTML原型，使用uni-app组件1:1复刻界面
- 使用Vue2.x语法进行开发
- 使用uni-icon替换原型中的图标

### 3. 功能开发
- 按模块进行开发
- 封装公共组件和样式
- 实现页面路由和导航
- 处理页面交互和状态管理
- 兼容刘海屏和普通屏幕
- 配置小程序特有功能(如分享、订阅消息等)

### 4. 前端数据管理
- 创建数据请求接口
- 设计数据模型
- 实现数据状态管理
- 处理本地存储
- 创建数据请求和响应拦截器

## 四、与后端对接

### 1. 接口定义
- 与后端开发人员共同制定API接口规范
- 定义请求和响应格式
- 明确错误码和处理机制
- 编写接口文档

### 2. 接口联调
- 实现API请求模块
- 配置开发环境代理
- 逐个接口进行联调
- 处理各种异常情况
- 调整前端逻辑以适配实际接口

### 3. 数据模拟
- 在后端接口未完成前，创建Mock数据
- 使用本地JSON模拟后端响应
- 实现离线开发功能

## 五、测试与优化

### 1. 功能测试
- 单元测试关键组件和函数
- 页面功能自测
- 表单验证和异常处理测试
- 跨端兼容性测试(小程序和H5)

### 2. 性能优化
- 图片资源优化
- 减少不必要的渲染
- 组件懒加载
- 优化网络请求
- 小程序分包加载

### 3. 用户体验优化
- 加载状态提示
- 操作反馈优化
- 错误提示优化
- 页面过渡效果
- 表单填写体验优化

## 六、部署与上线

### 1. 小程序审核准备
- 注册并完善微信小程序账号
  - 申请主体类型(个人/企业/政府/媒体等)
  - 提交必要的认证材料(营业执照、组织机构代码等)
  - 缴纳300元认证费用(企业类型)
- 准备小程序介绍材料
  - 撰写简洁明了的小程序名称(应与功能相符)
  - 设计符合规范的小程序图标(尺寸要求为750x750px)
  - 编写小程序功能介绍(200字以内)
  - 准备3-5张代表性截图(建议尺寸750x1334px)
- 设置隐私政策
  - 参考微信官方隐私协议模板
  - 明确说明收集用户信息的种类和用途
  - 说明数据存储的位置和安全措施
  - 提供用户数据删除和导出的途径
- 配置服务类目
  - 根据小程序功能选择正确的类目(最多可选3个)
  - 确保类目与实际功能匹配(避免审核被拒)
  - 准备类目所需的资质文件(如有)
- 完善基本信息
  - 设置客服联系方式(电话/邮箱)
  - 添加开发者信息
  - 配置业务域名和服务器域名
  - 设置默认分享文案和图片

### 2. 提交审核
- 打包小程序代码
  - 使用HBuilderX的"发行"功能生成小程序代码包
  - 确保代码已经过充分测试，无明显bug
  - 检查小程序体积是否超过限制(主包不超过2MB，分包不超过2MB)
  - 移除所有测试代码和调试信息
- 上传到微信开发者后台
  - 登录微信公众平台(mp.weixin.qq.com)
  - 进入"开发管理" > "开发版本"
  - 点击"上传"按钮提交代码包
  - 填写版本号和更新说明
- 提交审核申请
  - 在开发者后台选择需要提交的版本
  - 点击"提交审核"按钮
  - 填写审核相关信息(测试账号、测试方法等)
  - 选择功能页面标签(帮助审核人员理解功能)
  - 提供必要的测试素材(如需支付功能提供测试账号)
- 对接口进行安全域名配置
  - 在"开发管理" > "开发设置"中配置服务器域名
  - 添加request合法域名(API接口域名)
  - 添加uploadFile合法域名(文件上传域名)
  - 添加downloadFile合法域名(文件下载域名)
  - 配置WebSocket合法域名(如需)
  - 配置业务域名(用于网页跳转)
  - 注意：域名必须有备案且配置SSL证书(https)

### 3. 审核与发布
- 跟进审核进度
  - 通常审核时间为1-7个工作日
  - 在开发者后台实时查看审核状态
  - 开通微信公众平台安全助手接收审核通知
  - 查看审核状态的变更通知(微信通知/邮件)
- 处理审核反馈问题
  - 仔细阅读审核拒绝原因(截图和文字说明)
  - 根据反馈修改小程序代码或资料
  - 常见拒绝原因包括：UI不符合规范、功能缺失、类目不符、提示文案有误等
  - 修改后重新提交审核(注意版本号递增)
- 通过审核后发布
  - 审核通过后收到通知
  - 进入"版本管理"页面
  - 点击"提交发布"按钮
  - 选择发布时间(立即发布或定时发布)
  - 确认发布后小程序正式上线
- 设置发布版本和更新日志
  - 在开发者后台设置版本说明
  - 编写更新内容摘要(用户可见)
  - 配置版本更新提示(可选)
  - 建立版本发布记录档案

### 4. H5版本部署(如需)
- 打包H5静态文件
  - 使用HBuilderX的"发行" > "网站-H5手机版"
  - 检查生成的HTML、CSS和JavaScript文件
  - 确保资源引用路径正确(推荐使用相对路径)
  - 优化静态资源大小(压缩图片、合并CSS/JS)
- 上传到Web服务器
  - 准备Web服务器(如阿里云、腾讯云等)
  - 使用FTP工具上传文件到服务器
  - 配置正确的目录权限
  - 设置正确的MIME类型
- 配置服务器环境
  - 安装配置Nginx/Apache服务器
  - 启用HTTP/2以提高性能
  - 配置正确的字符集和压缩
  - 设置缓存策略(推荐使用ETag和Cache-Control)
  - 配置错误页面和重定向
- 设置域名和SSL证书
  - 购买并解析域名到服务器IP
  - 完成域名备案(中国大陆服务器必须)
  - 申请并安装SSL证书(推荐Let's Encrypt免费证书)
  - 配置HTTPS并设置HTTP自动跳转HTTPS
  - 配置HSTS增强安全性

### 5. 小程序多渠道发布(扩展)
- 微信小程序
  - 按上述流程完成微信小程序发布
- QQ小程序(如需)
  - 在QQ小程序平台注册开发者账号
  - 使用HBuilderX的条件编译适配QQ小程序
  - 按照QQ小程序流程提交审核发布
- 支付宝小程序(如需)
  - 在支付宝开放平台注册账号
  - 使用条件编译适配支付宝小程序特性
  - 提交审核并发布
- 百度智能小程序(如需)
  - 在百度智能小程序平台注册
  - 适配并提交审核发布
- 抖音小程序(如需)
  - 在抖音小程序平台注册
  - 适配并提交审核发布

## 七、运营与迭代

### 1. 数据监控
- 接入小程序数据分析
- 监控用户行为和转化
- 收集用户反馈
- 分析性能数据

### 2. 版本迭代
- 根据用户反馈规划新功能
- 修复已知问题
- 优化现有功能
- 定期更新版本

### 3. 持续优化
- 优化用户体验
- 提升性能指标
- 增强安全性
- 适配新版本小程序特性

## 八、开发提示与最佳实践

### 1. Cursor辅助开发
- 使用Cursor的Memory Bank功能跟踪项目进度
- 在开发过程中使用Plan Mode进行规划
- 使用Act Mode生成和修改代码
- 定期使用`update memory bank`更新记忆库

### 2. uni-app最佳实践
- 使用条件编译处理平台差异
- 合理使用组件生命周期
- 避免频繁操作DOM
- 注意小程序的特殊限制
- 优化首次加载速度

### 3. 与客户沟通
- 定期演示开发进度
- 及时反馈技术难点
- 灵活调整开发计划
- 记录需求变更 