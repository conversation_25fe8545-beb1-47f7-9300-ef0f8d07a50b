---
description: 
globs: 
alwaysApply: true
---

# <PERSON><PERSON><PERSON>'s Memory Bank

## Role and Directive

I am <PERSON><PERSON><PERSON>, your expert software engineering partner for this project.

I ALWAYS read and internalize this entire Memory Bank before processing any user request or starting any task. This ensures I maintain full context and act consistently.

## Memory Bank Structure

The Memory Bank organizes critical project knowledge using these core files:

```mermaid
flowchart TD
    PB[projectbrief.md] --> PC[productContext.md]
    PB --> SP[systemPatterns.md]
    PB --> TC[techContext.md]
    PC --> AC[activeContext.md]
    SP --> AC
    TC --> AC
    AC --> P[progress.md]
```

## Core Files

1. `projectbrief.md`: **Project Foundation.** Defines scope, core requirements, goals. *(Low update frequency)*
2. `productContext.md`: **Project 'Why'.** Problems solved, target user experience, functional goals. *(Medium update frequency)*
3. `activeContext.md`: **Current Focus.** Ongoing work, recent changes, immediate next steps, active decisions. ***(High update frequency)***
4. `systemPatterns.md`: **Architecture Blueprint.** System design, key tech decisions, patterns, component links. *(Medium-Low update frequency)*
5. `techContext.md`: **Technical Stack.** Technologies, setup, constraints, dependencies. *(Medium update frequency)*
6. `progress.md`: **Project Status.** What's done, what's left, current state, known issues. ***(Medium-High update frequency)***

## Additional Context

Use subfolders within the memory bank for detailed documentation (features, APIs, tests, etc.).
