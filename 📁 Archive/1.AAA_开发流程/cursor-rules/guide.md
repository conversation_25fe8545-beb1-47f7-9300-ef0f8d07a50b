# Cursor Rules 开发指南：UniApp (Vue3) + Spring Boot 项目实战

本指南详细说明如何利用Cursor Rules中的各种规则文件（.mdc）开发一个小程序前端（UniApp/Vue3）和Java Spring Boot后端的完整项目。

## 目录

1. [项目初始化](#项目初始化)
2. [项目规划](#项目规划)
3. [前端开发阶段](#前端开发阶段)
4. [API设计阶段](#api设计阶段)
5. [后端开发阶段](#后端开发阶段)
6. [集成与测试](#集成与测试)
7. [版本控制与协作](#版本控制与协作)
8. [常见问题](#常见问题)

## 项目初始化

首先，我们需要初始化Memory Bank来建立项目上下文和组织结构。

### 步骤1：初始化Memory Bank

```
initialize memory bank
```

这将创建`.memorybank`目录和核心文件，为项目建立基础结构。

### 步骤2：选择技术栈规则文件

根据我们的项目技术栈，需要从`cursor-rules`目录中选择以下规则文件：

- `vue3.mdc`：前端Vue3开发
- `java.mdc`：Java后端开发
- `springboot.mdc`：Spring Boot框架
- `maven.mdc`：构建工具
- `api.mdc`：API设计
- `git.mdc`：版本控制
- `memory-bank*.mdc`：记忆系统（已自动加载）

在与Cursor的对话中提及这些技术栈，例如：

```
我正在开发一个项目，前端使用UniApp(Vue3)，后端使用Java Spring Boot。
请在处理我的请求时考虑这些技术栈。
```

## 项目规划

### 步骤1：激活Plan Mode

在规划阶段，使用Plan Mode来制定项目结构和架构：

```
我需要规划一个小程序项目的基本架构，前端使用UniApp(Vue3)，后端使用Spring Boot。
请帮我设计一个合理的项目结构和技术选型 use Plan mode
```

### 步骤2：更新项目简介

完成规划后，更新Memory Bank中的项目信息：

```
请将我们讨论的项目结构和技术选型更新到memory bank的projectbrief.md和systemPatterns.md中
```

## 前端开发阶段

### 步骤1：创建UniApp项目结构

```
请为我创建一个基于Vue3的UniApp项目结构，包含常见的页面和组件目录 use Act mode
```

### 步骤2：开发页面和组件

在这个阶段，充分利用`vue3.mdc`的规则和模式：

```
我需要开发一个商品列表页面，包含下拉刷新和上拉加载更多功能。请使用UniApp/Vue3实现
```

实际示例对话：

```
用户: 请为我创建一个商品详情页面，需要包含轮播图、商品信息、规格选择和加入购物车功能

Cursor: 我将为您创建UniApp/Vue3的商品详情页面。基于Vue3的组合式API，这是实现方式...
[生成代码和解释]
```

### 步骤3：状态管理和请求处理

```
我需要实现商品数据的Pinia状态管理和网络请求封装，请给出最佳实践代码
```

## API设计阶段

在这个阶段，我们使用`api.mdc`来设计前后端交互的API：

### 步骤1：设计API规范

```
请帮我设计商城系统的RESTful API规范，包括用户、商品、订单等模块 use Plan mode
```

### 步骤2：生成API文档

```
基于我们讨论的API设计，请生成一份OpenAPI/Swagger文档 use Act mode
```

### 步骤3：前端API调用封装

```
请为UniApp封装一个API请求模块，包含拦截器、统一错误处理和请求取消功能
```

## 后端开发阶段

这个阶段结合使用`java.mdc`、`springboot.mdc`和`maven.mdc`规则：

### 步骤1：创建Spring Boot项目

```
请创建一个Spring Boot项目结构，使用Maven管理依赖，实现商城后端功能 use Act mode
```

### 步骤2：实现领域模型和数据访问

```
请设计并实现商品和订单的领域模型、Repository和Service层代码
```

实际示例对话：

```
用户: 请实现商品模块的数据模型和JPA Repository

Cursor: 基于Spring Data JPA，我将创建商品领域模型和相关Repository：
[生成代码和解释]
```

### 步骤3：开发控制器和业务逻辑

```
请实现商品管理的Controller，包含列表、详情、添加、修改和删除API
```

## 集成与测试

### 步骤1：前后端接口联调

```
我需要测试前端调用后端API的流程，请提供Mock服务和实际调用示例
```

### 步骤2：单元测试

```
请为商品服务层代码编写JUnit测试用例，包括正常和异常情况
```

## 版本控制与协作

这个阶段使用`git.mdc`和`git-auto-commit.mdc`规则：

### 步骤1：设置Git工作流

```
请帮我设置Git分支策略和工作流，包括feature、develop和main分支 use Plan mode
```

### 步骤2：代码提交

在完成功能开发后，Cursor会自动提示Git操作：

```
完成功能开发后，Cursor会提示：

任务完成，Memory Bank自动更新。

建议的Git工作流步骤（在终端手动执行）：

1. 查看更改：
   git status

2. 暂存所有更改（代码+Memory Bank更新）：
   git add .

3. 提交更改：
   git commit -m "feat: 实现商品详情页面和数据加载"

4. 更新本地分支：
   git pull origin feature/product-detail --rebase

5. 推送到远程仓库：
   git push origin feature/product-detail
```

## 常见问题

### 如何切换开发模式？

在提问末尾添加`use Plan mode`或`use Act mode`可以明确指定模式：
- Plan mode：适合架构设计、规划和分析
- Act mode：适合代码生成和实现

### 如何更新Memory Bank？

项目发生重大变化时，使用：
```
update memory bank
```

### 如何选择最合适的规则文件？

根据当前任务自动或明确指定：
- 前端开发：vue3.mdc
- 后端开发：java.mdc, springboot.mdc, maven.mdc
- API设计：api.mdc
- 版本控制：git.mdc

### 完整工作流示例

**前端页面开发**：
1. 激活vue3.mdc规则
2. 使用Plan mode设计页面结构
3. 使用Act mode实现具体代码
4. 更新Memory Bank
5. 提交代码到Git

**后端API开发**：
1. 激活java.mdc和springboot.mdc规则
2. 使用Plan mode设计API和数据模型
3. 使用Act mode实现Controller和Service
4. 更新Memory Bank
5. 提交代码到Git 