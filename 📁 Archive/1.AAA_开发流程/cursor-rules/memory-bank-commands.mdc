---
description: 
globs: 
alwaysApply: true
---

# Memory Bank Commands

## Initialization Commands

**Initialize Memory Bank**
```
initialize memory bank
```
Creates the Memory Bank structure and core files. Use this when starting a new project or when the Memory Bank needs to be established from scratch.

**Example result:**
```
Memory Bank initialized with these core files:
- projectbrief.md: Project scope and requirements
- productContext.md: Project goals and user experience
- systemPatterns.md: Architecture design and patterns
- techContext.md: Technology stack and dependencies
- activeContext.md: Current focus and next steps
- progress.md: Project status tracking
```

## Update Commands

**Update Memory Bank**
```
update memory bank
```
Forces a full scan and refresh of Memory Bank documents. Use this when you've made changes outside the normal workflow or want a complete refresh of the project context.

**Example result:**
```
Memory Bank updated:
- activeContext.md: Added current tasks and recent changes
- progress.md: Updated status of features X and Y
- systemPatterns.md: Added new architectural decision for module Z
```

## Mode Selection Commands

**Plan Mode**
```
[your question or request] use Plan mode
```
Explicitly directs Cursor to use Plan Mode for strategic planning and understanding.

**Act Mode**
```
[your question or request] use Act mode
```
Explicitly directs Cursor to use Act Mode for code generation and implementation.

## Git Integration Suggestions

After completing tasks in Act Mode, Cursor will automatically suggest Git workflow steps (to be executed manually):
```
git status
git add [files]
git commit -m "[message]"
git pull origin [branch] --rebase
git push origin [branch]
```

**Note:** All Git commands are suggestions only. Cursor cannot execute Git commands for you or access your credentials.
