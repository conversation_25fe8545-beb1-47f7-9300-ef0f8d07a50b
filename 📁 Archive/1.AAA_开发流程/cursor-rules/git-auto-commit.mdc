---
description: 
globs: 
alwaysApply: true
---
---
description: Git Auto Commit Rule
globs: 
alwaysApply: false
---
Description
This rule ensures that after Cursor IDE automatically performs changes from Git commits, the modified files are automatically committed back to Git using the conventional commit format. This maintains a clear and consistent commit history that explains what changes were made and why.

Rule
After Cursor IDE performs automatic changes:
1. All modified files MUST be automatically committed
2. Commit messages MUST follow the conventional commit format
3. Commit messages MUST include:
  - Type: The type of change (feat, fix, docs, style, refactor, test, chore)
  - Scope: The affected component or area (optional)
  - Description: A clear explanation of what changed
  - Body: Detailed explanation of why the changes were made
4. The commit message MUST reference the original prompts used to generate the changes

Implementation
- The Cursor IDE will:
  - Track all files modified by automatic changes
  - Generate a conventional commit message based on the changes
  - Include the original prompts in the commit body
  - Automatically execute the git commit command
  - Handle any potential merge conflicts or errors

Benefits
- Maintains clear and consistent commit history
- Provides traceability between prompts and code changes
- Follows industry-standard commit conventions
- Automates the commit process for better workflow efficiency

Examples
✅ Correct Commit Message:

❌ Incorrect Commit Message:
Conventional Commit Types
- feat: New feature
- fix: Bug fix
- docs: Documentation changes
- style: Code style changes (formatting, etc.)
- refactor: Code refactoring
- test: Adding or modifying tests
- chore: Maintenance tasks 
