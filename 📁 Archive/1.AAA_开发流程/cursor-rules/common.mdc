---
description: 
globs: 
alwaysApply: true
---
全局代码规范
- 始终优先选择简单方案 
- 尽可能避免代码重复
  • 修改代码前，检查代码库中是否已存在相似功能或逻辑。
- 编写代码时需区分不同环境
  • 明确区分开发环境（dev）、测试环境（test）和生产环境（prod）。
- 谨慎修改代码
  • 仅针对明确需求进行更改，或确保修改内容与需求强相关且已被充分理解。
- 修复问题时避免引入新技术/模式
  • 优先彻底排查现有实现的可能性，若必须引入新方案，需同步移除旧逻辑以避免冗余。
- 保持代码库整洁有序
- 避免在文件中编写脚本
  • 尤其是仅需运行一次的脚本（如数据迁移临时脚本）。
- 控制单文件代码行数
  • 文件代码超过 200-300行 时需重构封装。
- 仅测试环境使用模拟数据
  • 开发与生产环境严禁使用模拟（Mock）数据。
- 禁止覆盖 .env 文件
  • 修改前需确认并征得同意。
