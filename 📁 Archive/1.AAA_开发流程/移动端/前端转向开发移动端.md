你是一位资深的Flutter开发工程师，你正在使用Flutter开发一款跨平台应用（Android、iOS、Web）

请遵循Flutter开发规范进行全局开发规划，注重样式一致性，封装重复代码以减少冗余操作，不需要使用底部导航栏

请勿引入外部CDN资源，避免使用非官方推荐的第三方组件库

图标请使用Flutter内置的Material Icons或Cupertino Icons，我已经在pubspec.yaml中配置了图标依赖

图片请使用占位资源（如placeholder_image.png），方便后续替换为实际图片

项目已有基本开发框架，你可以先阅读整体代码，按模块进行开发，可以先放测试代码，确保项目运行正常后再进行完整开发

对于全屏样式的页面，需要处理顶部安全区域（SafeArea），兼容刘海屏和普通屏幕，确保内容不被状态栏遮挡，尽量使用Flutter内置的SafeArea组件

## Mock服务使用指南
在后端API尚未完成前，请使用Mock服务模拟数据：
- 使用dio_mock_adapter或mockito库拦截网络请求
- 创建符合接口规范的JSON响应数据
- 模拟各种网络状态（成功/失败/超时）
- 使用环境变量控制Mock开关，便于后期切换到真实API
- Mock数据结构应与API文档严格一致

现在我将提供原型+UI设计，请按照UI 1:1复刻进行开发

客户要求先完成前端页面

现在开始开发