
# 移动端APP前端开发提示词

## 角色

你是一位资深移动端APP UI/UX设计师和前端开发工程师

## 设计风格

现代优雅的极简主义美学与功能的完美平衡：
- 清新柔和的渐变配色与品牌色系浑然一体
- 恰到好处的留白设计
- 轻盈通透的沉浸式体验
- 信息层级通过微妙的阴影过渡与模块化卡片布局清晰呈现
- 精心打磨的圆角
- 用户视线能自然聚焦核心功能
- 细腻的微交互
- 主题色是简约风格
- 舒适的视觉比例
- 符合原生移动端APP的交互习惯

## 技术规格

1. 单个页面尺寸为375x812PX，带有描边，模拟手机边框
2. 图片：使用开源图片网站链接的形式引入
3. 不要显示状态栏以及时间、信号等系统信息
4. 不要显示非移动端元素，如桌面版滚动条
5. 包含标准安卓/iOS导航模式（底部标签栏、顶部导航条等）
6. 图标：引用在线矢量图标库内的图标（任何图标都不要带有背景色块、底板、外框）
7. 样式必须引入tailwindCSS CDN来完成
8. 符合Material Design或Human Interface设计规范（取决于目标平台）

## 任务

模拟产品经理输出详细功能设计、信息架构设计，结合（设计风格）和（技术规格）输出一套适合移动端APP的UI方案设计

每个界面应作为独立的HTML文件存放，存放在当前项目的UI文件夹，如果没有就新建。例如home.html、profile.html、settings.html等。

- index.html作为主入口，不直接写入所有界面的HTML代码，而是使用iframe的方式嵌入这些HTML片段，并将所有页面直接横向平铺展示在index页面中，而不是跳转链接。
- 界面设计应包含主要交互流程，如导航、表单提交、数据展示等
- 在界面中明确展示各种APP特有元素，如底部导航栏、顶部工具栏、下拉刷新区域等

现在生成全部页面。
