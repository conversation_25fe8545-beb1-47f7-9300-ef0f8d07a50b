---
created: {{date}} {{time}}
type: directory-guide
tags: [复习, 日记, 计划]
---

# 定期复习目录说明

这个目录用于存放通过Calendar插件创建的各类定期复习和记录文件。

## 目录结构
- `Daily/` - 存放每日记录和复习内容
  - 使用模板：`5-Templates/Daily.md`
  - 命名格式：`YYYY-MM-DD-主要内容.md`
  - 示例：`2024-01-01-Docker容器学习.md`

- `Weekly/` - 存放每周总结和复习内容
  - 使用模板：`5-Templates/Weekly Review.md`
  - 命名格式：`YYYY-WW-本周主题.md`
  - 示例：`2024-W01-自动化测试框架学习.md`

- `Monthly/` - 存放每月总结和复习内容
  - 使用模板：`5-Templates/Monthly Review.md`
  - 命名格式：`YYYY-MM-月度主题.md`
  - 示例：`2024-01-Linux系统管理学习总结.md`

- `Yearly/` - 存放年度总结和复习内容
  - 使用模板：`5-Templates/Yearly Review.md`
  - 命名格式：`YYYY-年度关键词.md`
  - 示例：`2024-全栈测试工程师进阶.md`

## Calendar插件设置
1. 新建笔记的默认位置：`7-Reviews/定期复习/Daily`
2. 使用的默认模板：`5-Templates/Daily.md`
3. 文件命名建议：
   - 保留日期前缀以便于时间索引
   - 添加具体内容描述以便于主题索引
   - 使用统一的分隔符（建议使用短横线）

## 使用建议
1. 每日记录
   - 在文件名中标注当天的主要学习内容
   - 使用标签标记技术领域
   - 关联相关的详细笔记
   - 记录重要的问题和解决方案

2. 定期复习
   - 每周回顾：按照技术主题组织内容
   - 每月复习：总结技术领域的进展
   - 年度总结：按照职业发展路线梳理

3. 标签使用
   - 主题标签：如 [[Docker]] [[Linux]] [[测试]]
   - 类型标签：如 [[学习笔记]] [[问题解决]] [[最佳实践]]
   - 进度标签：如 [[进行中]] [[已完成]] [[待复习]]

## 文件组织技巧
1. 主题聚合
   - 相关内容使用统一前缀
   - 便于按项目或技术领域归类
   - 方便批量查找和复习

2. 内容关联
   - 使用双向链接关联相关笔记
   - 在月度总结中链接相关的每日记录
   - 在年度总结中链接重要的月度总结

3. 复习提醒
   - 为重要内容设置复习时间
   - 使用标签标记复习优先级
   - 在定期总结中回顾学习效果

## 注意事项
1. 文件名要具有描述性和可读性
2. 保持命名格式的一致性
3. 及时更新和完善内容
4. 定期整理和归档
5. 建立清晰的标签体系
6. 保持复习计划的持续性