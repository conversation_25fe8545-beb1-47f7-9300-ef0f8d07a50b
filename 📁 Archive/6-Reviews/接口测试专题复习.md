---
created: 2024-01-03
aliases: [API Testing Review]
---

状态: [[复习]]
标签: [[接口测试]] [[专题复习]] [[费曼学习法]]

# 接口测试专题复习（半天计划）

## 一、概念简化（1小时）

### 1.1 HTTP协议的生活类比
就像在餐厅点餐：
- 客人（客户端）向服务员（服务器）点餐（发送请求）
- 服务员给客人送餐（返回响应）
- 菜单就是API文档
- 点餐方式就是请求方法（GET/POST）

### 1.2 接口测试的本质
类比餐厅服务质量检查：
- 能否正确下单（功能测试）
- 上菜速度（性能测试）
- 是否只有付款客人才能取餐（安全测试）

### 1.3 核心概念图
```mermaid
graph TD
    A[接口测试] --> B[基础知识]
    A --> C[测试内容]
    A --> D[实现方法]
    
    B --> B1[HTTP协议]
    B --> B2[请求/响应结构]
    
    C --> C1[功能测试]
    C --> C2[性能测试]
    C --> C3[安全测试]
    
    D --> D1[手工测试/Postman]
    D --> D2[自动化测试/Python]
```

## 二、实践练习（2小时）

### 2.1 基础HTTP请求练习
```python
# 最简单的GET请求示例
import requests

def test_simple_get():
    # 1. 准备
    url = "https://api.example.com/users"
    
    # 2. 发送请求
    response = requests.get(url)
    
    # 3. 验证响应
    if response.status_code == 200:
        print("请求成功！")
        print("返回数据:", response.json())
    else:
        print("请求失败！")

# POST请求示例
def test_simple_post():
    url = "https://api.example.com/login"
    data = {
        "username": "test",
        "password": "123456"
    }
    response = requests.post(url, json=data)
    print("状态码:", response.status_code)
    print("响应数据:", response.json())
```

### 2.2 接口自动化框架
```python
class APITest:
    def __init__(self, base_url):
        self.base_url = base_url
        self.session = requests.Session()
    
    def send_request(self, method, endpoint, data=None):
        url = self.base_url + endpoint
        response = self.session.request(method, url, json=data)
        return response
    
    def verify_response(self, response, expected_code=200):
        assert response.status_code == expected_code
```

## 三、重点难点解析（1小时）

### 3.1 HTTP状态码记忆法
- 2开头：成功（像餐厅说"好的，没问题"）
- 3开头：重定向（像说"请去其他桌位"）
- 4开头：客户端错误（像说"您点的菜品不存在"）
- 5开头：服务器错误（像说"厨房设备故障"）

### 3.2 GET和POST区别
| 特点 | GET | POST |
|------|-----|------|
| 数据位置 | URL中 | 请求体中 |
| 安全性 | 较低 | 较高 |
| 缓存 | 可以 | 不可以 |
| 使用场景 | 查询数据 | 提交数据 |

### 3.3 接口安全重点
1. 身份认证（像检查就餐凭证）
2. 数据加密（像密封打包）
3. 权限控制（像VIP专属菜品）

## 四、性能测试实践（1.5小时）

### 4.1 简单压力测试
```python
import time
from concurrent.futures import ThreadPoolExecutor

def pressure_test(url, num_requests=100, num_threads=10):
    def single_request():
        start_time = time.time()
        response = requests.get(url)
        end_time = time.time()
        return {
            "success": response.status_code == 200,
            "time": end_time - start_time
        }
    
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        results = list(executor.map(lambda _: single_request(), range(num_requests)))
    
    success_rate = sum(1 for r in results if r["success"]) / len(results)
    avg_time = sum(r["time"] for r in results) / len(results)
    print(f"成功率: {success_rate*100}%")
    print(f"平均响应时间: {avg_time}秒")
```

## 五、复习检查清单
- [ ] 能用自己的话解释HTTP协议
- [ ] 能写出基本的GET和POST请求
- [ ] 理解常见的状态码含义
- [ ] 能进行简单的接口测试
- [ ] 了解基本的安全测试点
- [ ] 能执行简单的性能测试

## 六、实际应用思考
1. 在实际项目中如何组织测试用例？
2. 如何处理接口之间的依赖关系？
3. 如何保证测试数据的有效性？
4. 如何处理测试环境的差异？

## 七、进阶学习方向
1. 接口自动化测试框架设计
2. 性能测试深入研究
3. 安全测试专项学习
4. 持续集成与持续部署 