# Web测试工程师简历

## 个人信息
- 姓名：路辉
- 年龄：23岁
- 电话：15393320330
- 邮箱：<EMAIL>
- 现居地：西安

## 求职意向
- 岗位：Web测试工程师
- 工作地点：西安
- 到岗时间：1~2周以内

## 教育背景
- 学历：本科
- 专业：计算机相关专业
- 毕业时间：2024年6月
- 主修课程：软件测试、计算机网络、数据库原理、软件工程

## 专业技能
- 掌握软件测试基础理论，熟悉测试用例设计方法
- 掌握黑盒测试方法，包括等价类、边界值、场景法等测试方法
- 掌握Python + Selenium + Unittest进行Web UI自动化测试
- 掌握接口测试工具Postman，能够进行接口测试和自动化测试
- 掌握性能测试工具JMeter，能进行基础的性能测试
- 熟练使用MySQL数据库，能够编写SQL语句
- 熟悉Linux常用命令，能够进行基本的系统操作
- 熟悉Git版本控制工具的基本使用
- 了解持续集成工具Jenkins的基本概念
- 熟悉抓包工具Charles、Fiddler的使用方法

## 项目经验
### 电商网站测试项目
- 参与网站功能测试和接口测试工作
- 编写测试用例，覆盖用户注册、登录、商品展示、购物车、订单等功能
- 使用Selenium+Python编写自动化测试脚本
- 使用JMeter进行性能测试，评估系统性能
- 编写测试报告，记录测试结果

### Web自动化测试框架开发
- 参与Python+Selenium+Unittest自动化测试框架的开发
- 实现测试用例的管理和执行
- 开发测试报告生成功能
- 实现失败用例截图功能
- 编写框架使用说明文档

## 自我评价
- 热爱软件测试行业，具备扎实的专业知识
- 善于思考和总结，具有较强的问题分析能力
- 工作态度认真，注重细节
- 良好的沟通能力，能够与团队成员良好协作
- 具有较强的学习能力，能够快速掌握新知识 

