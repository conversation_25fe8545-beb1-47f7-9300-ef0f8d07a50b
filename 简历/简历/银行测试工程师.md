# 银行测试工程师简历

## 个人信息
- 姓名：路辉
- 年龄：23岁
- 电话：15393320330
- 邮箱：<EMAIL>
- 现居地：西安

## 求职意向
- 岗位：银行测试工程师
- 工作地点：西安
- 到岗时间：1~2周以内

## 教育背景
- 学历：本科
- 专业：计算机相关专业
- 毕业时间：2024年6月
- 主修课程：软件测试、数据库系统、计算机网络、信息安全

## 专业技能
- 掌握软件测试理论，熟悉测试用例设计方法
- 掌握黑盒测试方法，包括等价类、边界值、场景法等测试方法
- 掌握Python + Selenium自动化测试框架的使用
- 掌握接口测试工具Postman，能够进行接口测试
- 掌握性能测试工具JMeter的基本使用
- 熟练使用MySQL数据库，能够编写SQL语句
- 熟悉银行业务基本流程和系统架构
- 熟悉金融系统安全测试基本要求
- 了解Linux基本命令，能够进行系统操作
- 了解持续集成工具Jenkins的基本使用

## 项目经验
### 银行核心业务系统测试项目
- 参与核心业务系统的功能测试
- 编写测试用例，覆盖账户管理、转账交易等功能
- 使用Python编写自动化测试脚本
- 进行系统性能测试，评估系统性能
- 参与安全测试工作

### 网上银行系统测试项目
- 参与网上银行系统的功能测试
- 编写测试用例，覆盖用户认证、账户管理等功能
- 使用Selenium+Python编写自动化测试脚本
- 进行接口测试和性能测试
- 编写测试报告，记录测试结果

## 自我评价
- 热爱金融IT领域，具备良好的专业知识
- 具有较强的学习能力，善于分析问题
- 工作认真负责，有责任心
- 良好的沟通能力，能够与团队协作
- 积极主动，能够快速适应新环境 

