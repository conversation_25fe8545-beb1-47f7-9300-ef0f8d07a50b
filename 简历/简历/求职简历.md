---

cssclass: resume

---
  

<div class="resume-header">

<div class="info-section">

  

# 求职简历
## 基本信息
- 姓名：路辉
- 性别：男
- 年龄：23
- 电话：15393320330
- 微信：betray_others
- 邮箱：<EMAIL>
- 现居地：西安市
## 求职意向
- 岗位：终端测试工程师
- 工作地点：西安
- 期望薪资：面议
- 到岗时间：1周内
## IT技能
1. 熟练掌握软件测试理论知识和黑盒测试方法
2. 熟练使用Python + Appium进行Android端自动化测试
3. 熟练使用Fiddler等抓包工具进行移动端测试
4. 熟练使用Android Studio进行应用调试
5. 熟练使用性能测试工具Jmeter进行终端性能测试
6. 熟悉Android/iOS平台的测试特点和测试方法，包括主流及小众机型适配测试
7. 熟悉蓝牙设备测试，包括功能测试、稳定性测试和兼容性测试
8. 熟悉Git版本控制和Jenkins持续集成工具
9. 熟悉Monkey、MonkeyRunner等稳定性测试工具
10. 熟悉测试用例设计方法，包括等价类、边界值分析等
## 工作经历
### 西安天迈科技股份有限公司（2023.09-至今）
- 职位：终端测试工程师
- 负责工业级Android终端设备的测试工作
- 参与需求分析，制定测试方案，编写测试用例
- 完成功能测试、兼容性测试和性能测试工作
- 建立完整的终端适配测试体系，覆盖各类工业级设备
- 使用Python+Appium实现自动化测试，提升测试效率
## 项目经验
### 工业级终端及蓝牙设备测试（2023.09-2024.02）
**项目描述：**
面向物流、仓储、零售等行业的工业级终端设备测试项目，包含PDA终端功能测试与蓝牙设备测试。设备支持一维码/二维码扫描、NFC读写、蓝牙通信等多种功能。
**项目职责：**
1. 测试管理
   - 参与需求评审，制定测试计划，设计测试用例
   - 搭建完整的终端测试体系，覆盖功能、性能、兼容性等方面
   - 编写测试文档，输出测试报告，跟踪问题修复进度
2. 终端功能测试
   - 完成Android系统功能测试和硬件兼容性测试
   - 进行扫描模块、NFC模块等核心功能测试
   - 执行防水防尘、跌落等可靠性测试
   - 使用Fiddler进行移动端接口测试和问题分析
**项目成果：**
- 测试覆盖率达到95%以上，发现并解决重要问题30+个
- 自动化测试框架提升测试效率60%，节省人力成本
- 建立了完整的终端和蓝牙测试规范，被公司采纳为标准流程
## 教育背景
- 学校：兰州工业学院
- 专业：软件工程
- 学历：本科
- 在读时间：2020.9-2024.6
## 自我评价
- 热爱软件测试，具备扎实的专业知识和实践经验
- 学习能力强，善于总结和分享，保持技术热情
- 性格开朗，善于沟通，具有良好的团队协作精神
</div>

</div>

</div>