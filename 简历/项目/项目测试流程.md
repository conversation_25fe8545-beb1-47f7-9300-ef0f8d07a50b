## 从1.13 ~ 1.17
1. 熟悉项目  
2. 编写测试用例 功能测试  使用Python脚本进行测试
3. 接口测试 postman fiddler 
4. 界面自动化测试
5. 找两个印象深刻的bug

## 1.18
1. 每个人整理两个技术难题。在界面、接口时，遇到的技术难题，怎么解决的。
2. 下周给大家做模拟面试，班上组织，每人30分钟左右。尽量覆盖常见的面试题。
3. 将整个学习过程，梳理成ppt，分模块讲解（测试基础、Linux、mysql、Python/Java语言、界面自动化、接口自动化、性能、项目实战），自己学到了哪些知识，学习过程中遇到的困难，自己怎么解决的。最后项目实战模块，自己的项目描述，做了哪些测试？怎么做的。等等
   年后来了前两天答辩分享，每人30分钟。
4. web和app 最终的简历发我，我打包后发给班主任再过一遍。
5. 只写了一份简历的，争取下周放假前把第二份简历定稿后发我。


