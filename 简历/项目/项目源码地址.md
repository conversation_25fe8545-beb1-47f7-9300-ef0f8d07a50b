---

created: 2024-01-09

aliases: [测试项目源码]

tags: [项目, 源码, 测试]

---
---

# 测试项目源码库

  

## Web测试项目

1. 企业级微服务平台

- [JeecgBoot](https://github.com/jeecgboot/jeecg-boot)

- 技术栈：Spring Boot + Vue + MySQL + Redis

  

2. 供应链平台

- [oms-erp](https://github.com/FJ-OMS/oms-erp)

- 技术栈：Spring Cloud + Vue + MySQL + Redis

  

## APP测试项目

1. 开源音乐播放器

- [CloudMusic](https://github.com/Binaryify/NeteaseCloudMusicApi)

- [MusicPlayer](https://github.com/DroidNinja/Android-FilePicker)

- 技术栈：Android原生 + Retrofit + Room

  

2. 开源视频播放器

- [GSYVideoPlayer](https://github.com/CarGuo/GSYVideoPlayer)

- 技术栈：Android原生 + IJKPlayer + Kotlin

  

3. 开源文件管理器

- [MaterialFiles](https://github.com/zhanghai/MaterialFiles)

- 技术栈：Android原生 + Jetpack + Kotlin

  

## 车载测试项目

1. 车载诊断系统

- [AndrOBD](https://github.com/fr3ts0n/AndrOBD)

- 技术栈：Android + OBD协议 + 蓝牙通信

  

2. ADAS系统

- [apollo](https://github.com/ApolloAuto/apollo)

- 技术栈：C++, Python, ROS

  

## 银行测试项目

1. 信贷风控系统

- [hertzbeat](https://github.com/dromara/hertzbeat)

- 技术栈：Spring Boot + Vue + MySQL + Redis

  

2. 支付清算系统

- [rocketmq](https://github.com/apache/rocketmq)

- 技术栈：Spring Boot + MySQL + RocketMQ