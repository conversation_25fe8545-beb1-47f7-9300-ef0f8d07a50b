# Palette
## WEB测试工程师

![照片](照片.png)

---
**Phone**: ************  
**Email**: <EMAIL>  
**Address**: 北京市朝阳区xxx街道  
**Date of Birth**: 1995/01/01  
**Nationality**: 中国  

---
## OBJECTIVE
我是一名Web测试工程师，在Web应用测试领域有丰富经验。擅长功能测试、接口测试和性能测试，熟悉敏捷开发流程，追求高质量的软件交付。

## EDUCATION
### 计算机科学与技术
**某某大学** | 2013.09 - 2017.06  
- 专业课程：软件工程、数据结构、计算机网络、数据库原理
- 学习成绩：专业前10%
- GPA：3.8/4.0

## EXPERIENCE

### Web测试工程师
**某某科技有限公司** | 2020.03 - 至今

负责企业级微服务开发平台（JeecgBoot）的测试工作：
- 设计并执行测试用例，覆盖功能测试、接口测试和性能测试
- 使用Postman、JMeter等工具进行接口自动化测试和性能测试
- 参与需求评审，提供测试建议，确保产品质量
- 编写测试文档，包括测试计划、测试报告等

### 测试工程师
**某某软件公司** | 2017.07 - 2020.02

负责供应链协同管理系统（OMS-ERP）的测试工作：
- 制定测试策略和测试计划，设计测试用例
- 执行功能测试、集成测试和系统测试
- 使用Selenium进行Web自动化测试
- 进行性能测试和压力测试，分析并解决性能问题

## SKILLS

### 测试技能
- 熟练掌握功能测试、接口测试、性能测试等测试方法
- 熟悉测试用例设计方法，包括等价类、边界值等
- 熟练使用Postman、JMeter等测试工具
- 掌握Selenium、Python等自动化测试技术

### 开发技能
- 熟悉Java、Python编程语言
- 了解Spring Boot、MyBatis等框架
- 熟悉MySQL、Redis等数据库
- 掌握Linux常用命令

### 其他技能
- 熟悉敏捷开发流程
- 良好的文档编写能力
- 优秀的沟通协作能力
- 较强的问题分析和解决能力

## LANGUAGE
- 英语：CET-6，良好的读写能力
- 中文：母语 