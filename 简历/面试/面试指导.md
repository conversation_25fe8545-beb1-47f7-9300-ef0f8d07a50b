# 面试准备

# 职业形象要求
# 面试中的关注点
智联 前程 boss 拉勾 猎聘

智联 前程 boss

有官网的大公司
无官网 小公司

优先与老师

优先复试
优先于成功概率大的

了解

大公司：我目前找工作是公司的平台和项目稳定性
小公司：目前能否和技术大牛面对面交流，对项目的参与性

![[Pasted image 20250121114349.png]]


![[Pasted image 20250121115933.png]]

人事：人员配比
技术 工具 技术  怎么测试的

![[Pasted image 20250121120104.png]]


![[Pasted image 20250121120134.png]]


软件测试的基本流程一般包括以下几个主要阶段：
 
测试计划与设计
 
- 需求分析：测试人员需与相关人员沟通，全面了解软件的功能、性能等需求，梳理出清晰的测试要点，为后续测试工作奠定基础。
 
- 制定测试计划：依据需求分析结果，确定测试的目标、范围、策略、资源、进度等，形成详细的测试计划文档，指导整个测试过程。
 
- 设计测试用例：根据需求规格说明书等，运用等价类划分、边界值分析等方法，设计覆盖各种情况的测试用例，确保软件功能的完整性和准确性。
 
测试环境搭建
 
- 搭建环境：根据软件的运行要求，配置包括硬件设备、操作系统、数据库等在内的测试环境，确保环境与软件实际运行环境相似或一致。
 
- 环境测试：对搭建好的环境进行测试，检查环境是否稳定、各项配置是否正确，确保测试工作能在稳定的环境中进行。
 
测试执行
 
- 单元测试：开发人员对软件的最小可测试单元进行测试，检查代码是否符合设计要求，是否存在逻辑错误等，提高代码的可靠性。
 
- 集成测试：将已测试的单元模块逐步集成，测试模块之间的接口和通信是否正确，确保集成后的系统能正常运行。
 
- 系统测试：将软件作为一个整体，在模拟的实际运行环境下进行全面测试，检查软件是否满足需求规格说明书中的所有要求。
 
- 验收测试：由用户或客户参与，在实际使用环境下对软件进行测试，验证软件是否满足业务需求和用户期望。
 
缺陷管理与跟踪
 
- 缺陷发现与记录：测试人员在测试过程中发现缺陷后，需详细记录缺陷的症状、出现的环境等信息，以便开发人员定位和修复。
 
- 缺陷提交与分配：将记录的缺陷提交到缺陷管理工具中，由项目负责人或相关人员分配给对应的开发人员进行修复。
 
- 缺陷修复与验证：开发人员根据缺陷描述进行修复，修复完成后，测试人员对缺陷进行验证，确保问题已解决。若未解决，需再次提交给开发人员。
 
测试报告与总结
 
- 测试报告编写：测试完成后，测试人员需编写测试报告，包括测试执行情况、缺陷统计与分析等内容，为项目决策提供依据。
 
- 测试总结与复盘：对整个测试过程进行总结，分析测试工作的优缺点，为后续项目积累经验，提出改进建议，以便在未来的项目中提高测试效率和质量。