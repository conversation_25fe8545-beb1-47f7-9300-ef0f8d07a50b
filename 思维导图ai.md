# 前后端分离项目开发主要角色与职责 (Markdown 思维导图风格)

- 项目开发主要角色
    - 产品经理 (Product Manager - PM)
        - 作用: 项目的灵魂人物，定义产品做什么，满足哪些需求，为什么做。客户需求的代言人。
        - 主要职责:
            - 挖掘、收集和理解客户/业务方需求。
            - 分析需求，定义产品功能、特性和业务规则。
            - 编写需求文档 (PRD)，定义用户故事和验收标准。
            - 制定产品路线图和功能优先级。
            - 协调团队理解需求，解答疑问。
            - 参与用户验收测试 (UAT)。
        - 活跃阶段:
            - 需求提出与初步沟通
            - 需求定义与规划 (核心)
            - 设计评审
            - 开发过程中的需求澄清
            - 验收测试
    - 项目经理 (Project Manager - Proj M)
        - 作用: 管理项目流程，确保按时、按预算、高质量完成。关注进度、资源、风险、沟通。
        - 主要职责:
            - 制定项目计划、排期和里程碑。
            - 分配任务和资源。
            - 监控项目进度，识别解决障碍和风险。
            - 协调团队内部及外部沟通。
            - 报告项目状态。
            - 管理项目范围和变更。
        - 活跃阶段:
            - 贯穿项目所有阶段 (核心)
    - UI/UX 设计师 (User Interface / User Experience Designer)
        - 作用: 设计用户体验和用户界面。关注易用性、高效性、美观性。
        - 主要职责:
            - 理解用户流程和交互需求。
            - 绘制线框图和高保真原型图。
            - 设计交互原型和视觉风格。
            - 输出设计规范文档和素材。
            - 与开发协作确保设计还原。
        - 活跃阶段:
            - 需求定义与规划 (理解需求)
            - 设计 (核心)
            - 开发 (提供支持/评审)
            - 测试 (参与可用性测试评审)
    - 技术架构师 / 解决方案架构师 (Technical Architect / Solution Architect)
        - 作用: 制定整体技术方案和架构，选择技术栈，确保系统扩展性、性能、安全、可维护性。
        - 主要职责:
            - 评估技术可行性，提供方案。
            - 设计高层级系统架构。
            - 确定关键技术选型。
            - 定义技术标准规范。
            - 指导详细设计，解决复杂技术问题。
            - 评估降低技术风险。
        - 活跃阶段:
            - 需求提出与初步沟通 (评估)
            - 需求定义与规划 (架构设计)
            - 设计 (详细技术方案指导)
            - 开发 (解决难题/评审)
            - 部署
    - 技术负责人 / 团队负责人 (Technical Lead / Team Lead)
        - 作用: 带领开发小组，解决技术问题，保障代码质量，指导团队成员。
        - 主要职责:
            - 参与技术方案讨论，细化设计。
            - 带领团队进行技术实现。
            - 进行代码评审，保障质量。
            - 帮助成员解决技术难题。
            - 协调技术对接。
        - 活跃阶段:
            - 需求定义与规划
            - 设计 (详细技术方案讨论)
            - 开发 (核心)
            - 联调与测试
    - 前端开发工程师 (Frontend Developer)
        - 作用: 实现用户界面和客户端交互逻辑。构建用户看到和操作的部分。
        - 主要职责:
            - 根据设计稿编写页面结构、样式、交互逻辑。
            - 使用前端框架/库开发。
            - 调用后端API，处理前端路由和状态。
            - 实现客户端数据处理和展示逻辑。
            - 进行前端测试。
            - 与后端协作进行接口联调。
        - 活跃阶段:
            - 设计 (评审可行性)
            - 接口设计 (参与定义)
            - 开发 (核心)
            - 联调与测试
            - 部署 (构建静态资源)
    - 后端开发工程师 (Backend Developer)
        - 作用: 实现服务器端业务逻辑、数据存储、API接口。支撑前端功能的“大脑”。
        - 主要职责:
            - 根据需求设计实现业务逻辑。
            - 设计维护数据库结构，编写数据库操作。
            - 使用后端语言/框架开发API接口。
            - 实现复杂后端功能 (如推荐算法、AR数据处理)。
            - 编写后端测试。
            - 与前端协作进行接口联调。
        - 活跃阶段:
            - 需求定义与规划 (技术可行性评估)
            - 设计 (数据库设计、参与架构)
            - 接口设计 (参与定义)
            - 开发 (核心)
            - 联调与测试
            - 部署
    - 数据库管理员 (Database Administrator - DBA)
        - 作用: 规划、安装、配置、维护、监控数据库，确保数据安全完整高可用。
        - 主要职责:
            - 选择数据库技术。
            - 设计优化数据库模式。
            - 管理用户权限安全性。
            - 备份恢复。
            - 监控性能，调优。
            - 规划执行迁移升级。
        - 活跃阶段:
            - 设计 (数据库设计评审)
            - 开发 (提供建议)
            - 部署 (环境搭建、数据迁移)
            - 运维与维护 (核心)
        - 备注: 小团队中可能由后端或 DevOps 兼任。
    - 测试工程师 / QA (Quality Assurance Engineer)
        - 作用: 确保产品质量 (功能、性能、安全、可用性等)。产品质量的“守护者”。
        - 主要职责:
            - 理解评审需求，从测试角度提建议。
            - 编写测试计划和用例。
            - 准备测试环境和数据。
            - 执行各类测试 (功能、集成、性能、安全等)。
            - 发现记录跟踪 Bug，验证修复。
            - 参与组织用户验收测试 (UAT)。
        - 活跃阶段:
            - 需求定义与规划
            - 设计 (评审测试点)
            - 开发 (准备测试、执行测试)
            - 联调与测试 (核心)
            - 部署 (上线验证)
    - DevOps 工程师 (Development Operations Engineer)
        - 作用: 打通开发运维，自动化流程，提高效率和系统稳定性。关注 CI/CD, 自动化运维, 监控。
        - 主要职责:
            - 搭建维护 CI/CD 流水线。
            - 管理自动化部署环境。
            - 使用 IaC 工具管理基础设施。
            - 搭建维护监控、日志、报警系统。
            - 协助开发排查环境故障。
            - 确保系统稳定运行、性能和安全。
        - 活跃阶段:
            - 开发 (环境支持)
            - 联调与测试 (自动化测试集成)
            - 部署 (核心)
            - 运维与维护 (核心)