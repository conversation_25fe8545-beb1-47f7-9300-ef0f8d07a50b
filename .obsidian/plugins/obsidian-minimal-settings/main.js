/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var x=Object.create;var u=Object.defineProperty;var L=Object.getOwnPropertyDescriptor;var W=Object.getOwnPropertyNames;var T=Object.getPrototypeOf,N=Object.prototype.hasOwnProperty;var v=n=>u(n,"__esModule",{value:!0});var O=(n,t)=>{v(n);for(var s in t)u(n,s,{get:t[s],enumerable:!0})},E=(n,t,s)=>{if(t&&typeof t=="object"||typeof t=="function")for(let l of W(t))!N.call(n,l)&&l!=="default"&&u(n,l,{get:()=>t[l],enumerable:!(s=L(t,l))||s.enumerable});return n},y=n=>E(v(u(n!=null?x(T(n)):{},"default",n&&n.__esModule&&"default"in n?{get:()=>n.default,enumerable:!0}:{value:n,enumerable:!0})),n);var p=(n,t,s)=>new Promise((l,d)=>{var f=h=>{try{m(s.next(h))}catch(o){d(o)}},g=h=>{try{m(s.throw(h))}catch(o){d(o)}},m=h=>h.done?l(h.value):Promise.resolve(h.value).then(f,g);m((s=s.apply(n,t)).next())});O(exports,{default:()=>S});var C=y(require("obsidian"));var a=y(require("obsidian")),D={lightStyle:"minimal-light",darkStyle:"minimal-dark",lightScheme:"minimal-default-light",darkScheme:"minimal-default-dark",editorFont:"",lineHeight:1.5,lineWidth:40,lineWidthWide:50,maxWidth:88,textNormal:16,textSmall:13,imgGrid:!1,imgWidth:"img-default-width",tableWidth:"table-default-width",iframeWidth:"iframe-default-width",mapWidth:"map-default-width",chartWidth:"chart-default-width",colorfulHeadings:!1,colorfulFrame:!1,colorfulActiveStates:!1,trimNames:!0,labeledNav:!1,fullWidthMedia:!0,bordersToggle:!0,minimalStatus:!0,focusMode:!1,underlineInternal:!0,underlineExternal:!0,folding:!0,lineNumbers:!1,readableLineLength:!1,devBlockWidth:!1},b=class extends a.PluginSettingTab{constructor(t,s){super(t,s);this.plugin=s}display(){let{containerEl:t}=this;t.empty();let l=t.createEl("div",{cls:"setting-item setting-item-heading"}).createEl("div",{cls:"setting-item-info"});l.createEl("div",{text:"Color scheme",cls:"setting-item-name"});let d=l.createEl("div",{cls:"setting-item-description"});d.appendChild(createEl("span",{text:"To create a custom color scheme use the "})),d.appendChild(createEl("a",{text:"Style Settings",href:"obsidian://show-plugin?id=obsidian-style-settings"})),d.appendText(" plugin. See "),d.appendChild(createEl("a",{text:"documentation",href:"https://minimal.guide/features/color-schemes"})),d.appendText(" for details."),new a.Setting(t).setName("Light mode color scheme").setDesc("Preset color options for light mode.").addDropdown(i=>i.addOption("minimal-default-light","Default").addOption("minimal-atom-light","Atom").addOption("minimal-ayu-light","Ayu").addOption("minimal-catppuccin-light","Catppuccin").addOption("minimal-eink-light","E-ink (beta)").addOption("minimal-everforest-light","Everforest").addOption("minimal-flexoki-light","Flexoki").addOption("minimal-gruvbox-light","Gruvbox").addOption("minimal-macos-light","macOS").addOption("minimal-nord-light","Nord").addOption("minimal-rose-pine-light","Ros\xE9 Pine").addOption("minimal-notion-light","Sky").addOption("minimal-solarized-light","Solarized").addOption("minimal-things-light","Things").setValue(this.plugin.settings.lightScheme).onChange(e=>{this.plugin.settings.lightScheme=e,this.plugin.saveData(this.plugin.settings),this.plugin.updateLightScheme()})),new a.Setting(t).setName("Light mode background contrast").setDesc("Level of contrast between sidebar and main content.").addDropdown(i=>i.addOption("minimal-light","Default").addOption("minimal-light-white","All white").addOption("minimal-light-tonal","Low contrast").addOption("minimal-light-contrast","High contrast").setValue(this.plugin.settings.lightStyle).onChange(e=>{this.plugin.settings.lightStyle=e,this.plugin.saveData(this.plugin.settings),this.plugin.updateLightStyle()})),new a.Setting(t).setName("Dark mode color scheme").setDesc("Preset colors options for dark mode.").addDropdown(i=>i.addOption("minimal-default-dark","Default").addOption("minimal-atom-dark","Atom").addOption("minimal-ayu-dark","Ayu").addOption("minimal-catppuccin-dark","Catppuccin").addOption("minimal-dracula-dark","Dracula").addOption("minimal-eink-dark","E-ink (beta)").addOption("minimal-everforest-dark","Everforest").addOption("minimal-flexoki-dark","Flexoki").addOption("minimal-gruvbox-dark","Gruvbox").addOption("minimal-macos-dark","macOS").addOption("minimal-nord-dark","Nord").addOption("minimal-rose-pine-dark","Ros\xE9 Pine").addOption("minimal-notion-dark","Sky").addOption("minimal-solarized-dark","Solarized").addOption("minimal-things-dark","Things").setValue(this.plugin.settings.darkScheme).onChange(e=>{this.plugin.settings.darkScheme=e,this.plugin.saveData(this.plugin.settings),this.plugin.updateDarkScheme()})),new a.Setting(t).setName("Dark mode background contrast").setDesc("Level of contrast between sidebar and main content.").addDropdown(i=>i.addOption("minimal-dark","Default").addOption("minimal-dark-tonal","Low contrast").addOption("minimal-dark-black","True black").setValue(this.plugin.settings.darkStyle).onChange(e=>{this.plugin.settings.darkStyle=e,this.plugin.saveData(this.plugin.settings),this.plugin.updateDarkStyle()})),t.createEl("br");let g=t.createEl("div",{cls:"setting-item setting-item-heading"}).createEl("div",{cls:"setting-item-info"});g.createEl("div",{text:"Features",cls:"setting-item-name"});let m=g.createEl("div",{cls:"setting-item-description"});m.appendChild(createEl("span",{text:"See "})),m.appendChild(createEl("a",{text:"documentation",href:"https://minimal.guide"})),m.appendText(" for details."),new a.Setting(t).setName("Text labels for primary navigation").setDesc("Navigation items in the left sidebar uses text labels.").addToggle(i=>i.setValue(this.plugin.settings.labeledNav).onChange(e=>{this.plugin.settings.labeledNav=e,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new a.Setting(t).setName("Colorful window frame").setDesc("The top area of the app uses your accent color.").addToggle(i=>i.setValue(this.plugin.settings.colorfulFrame).onChange(e=>{this.plugin.settings.colorfulFrame=e,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new a.Setting(t).setName("Colorful active states").setDesc("Active file and menu items use your accent color.").addToggle(i=>i.setValue(this.plugin.settings.colorfulActiveStates).onChange(e=>{this.plugin.settings.colorfulActiveStates=e,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new a.Setting(t).setName("Colorful headings").setDesc("Headings use a different color for each size.").addToggle(i=>i.setValue(this.plugin.settings.colorfulHeadings).onChange(e=>{this.plugin.settings.colorfulHeadings=e,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new a.Setting(t).setName("Minimal status bar").setDesc("Turn off to use full-width status bar.").addToggle(i=>i.setValue(this.plugin.settings.minimalStatus).onChange(e=>{this.plugin.settings.minimalStatus=e,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new a.Setting(t).setName("Trim file names in sidebars").setDesc("Use ellipses to fit file names on a single line.").addToggle(i=>i.setValue(this.plugin.settings.trimNames).onChange(e=>{this.plugin.settings.trimNames=e,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new a.Setting(t).setName("Workspace borders").setDesc("Display divider lines between workspace elements.").addToggle(i=>i.setValue(this.plugin.settings.bordersToggle).onChange(e=>{this.plugin.settings.bordersToggle=e,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new a.Setting(t).setName("Focus mode").setDesc("Hide tab bar and status bar, hover to display. Can be toggled via hotkey.").addToggle(i=>i.setValue(this.plugin.settings.focusMode).onChange(e=>{this.plugin.settings.focusMode=e,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new a.Setting(t).setName("Underline internal links").setDesc("Show underlines on internal links.").addToggle(i=>i.setValue(this.plugin.settings.underlineInternal).onChange(e=>{this.plugin.settings.underlineInternal=e,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new a.Setting(t).setName("Underline external links").setDesc("Show underlines on external links.").addToggle(i=>i.setValue(this.plugin.settings.underlineExternal).onChange(e=>{this.plugin.settings.underlineExternal=e,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new a.Setting(t).setName("Maximize media").setDesc("Images and videos fill the width of the line.").addToggle(i=>i.setValue(this.plugin.settings.fullWidthMedia).onChange(e=>{this.plugin.settings.fullWidthMedia=e,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),t.createEl("br");let o=t.createEl("div",{cls:"setting-item setting-item-heading"}).createEl("div",{cls:"setting-item-info"});o.createEl("div",{text:"Layout",cls:"setting-item-name"});let r=o.createEl("div",{cls:"setting-item-description"});r.appendChild(createEl("span",{text:"These options can also be defined on a per-file basis, see "})),r.appendChild(createEl("a",{text:"documentation",href:"https://minimal.guide/features/block-width"})),r.appendText(" for details."),new a.Setting(t).setName("Image grids").setDesc("Turn consecutive images into columns \u2014 to make a new row, add an extra line break between images.").addToggle(i=>i.setValue(this.plugin.settings.imgGrid).onChange(e=>{this.plugin.settings.imgGrid=e,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new a.Setting(t).setName("Chart width").setDesc("Default width for chart blocks.").addDropdown(i=>i.addOption("chart-default-width","Default").addOption("chart-wide","Wide line width").addOption("chart-max","Maximum line width").addOption("chart-100","100% pane width").setValue(this.plugin.settings.chartWidth).onChange(e=>{this.plugin.settings.chartWidth=e,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new a.Setting(t).setName("Iframe width").setDesc("Default width for iframe blocks.").addDropdown(i=>i.addOption("iframe-default-width","Default").addOption("iframe-wide","Wide line width").addOption("iframe-max","Maximum line width").addOption("iframe-100","100% pane width").setValue(this.plugin.settings.iframeWidth).onChange(e=>{this.plugin.settings.iframeWidth=e,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new a.Setting(t).setName("Image width").setDesc("Default width for image blocks.").addDropdown(i=>i.addOption("img-default-width","Default").addOption("img-wide","Wide line width").addOption("img-max","Maximum line width").addOption("img-100","100% pane width").setValue(this.plugin.settings.imgWidth).onChange(e=>{this.plugin.settings.imgWidth=e,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new a.Setting(t).setName("Map width").setDesc("Default width for map blocks.").addDropdown(i=>i.addOption("map-default-width","Default").addOption("map-wide","Wide line width").addOption("map-max","Maximum line width").addOption("map-100","100% pane width").setValue(this.plugin.settings.mapWidth).onChange(e=>{this.plugin.settings.mapWidth=e,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new a.Setting(t).setName("Table width").setDesc("Default width for table and Dataview blocks.").addDropdown(i=>i.addOption("table-default-width","Default").addOption("table-wide","Wide line width").addOption("table-max","Maximum line width").addOption("table-100","100% pane width").setValue(this.plugin.settings.tableWidth).onChange(e=>{this.plugin.settings.tableWidth=e,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),t.createEl("br"),t.createEl("div",{text:"Typography",cls:"setting-item setting-item-heading"}),new a.Setting(t).setName("Text font size").setDesc("Used for the main text (default 16).").addText(i=>i.setPlaceholder("16").setValue((this.plugin.settings.textNormal||"")+"").onChange(e=>{this.plugin.settings.textNormal=parseFloat(e),this.plugin.saveData(this.plugin.settings),this.plugin.setFontSize()})),new a.Setting(t).setName("Small font size").setDesc("Used for text in the sidebars and tabs (default 13).").addText(i=>i.setPlaceholder("13").setValue((this.plugin.settings.textSmall||"")+"").onChange(e=>{this.plugin.settings.textSmall=parseFloat(e),this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new a.Setting(t).setName("Line height").setDesc("Line height of text (default 1.5).").addText(i=>i.setPlaceholder("1.5").setValue((this.plugin.settings.lineHeight||"")+"").onChange(e=>{this.plugin.settings.lineHeight=parseFloat(e),this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new a.Setting(t).setName("Normal line width").setDesc("Number of characters per line (default 40).").addText(i=>i.setPlaceholder("40").setValue((this.plugin.settings.lineWidth||"")+"").onChange(e=>{this.plugin.settings.lineWidth=parseInt(e.trim()),this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new a.Setting(t).setName("Wide line width").setDesc("Number of characters per line for wide elements (default 50).").addText(i=>i.setPlaceholder("50").setValue((this.plugin.settings.lineWidthWide||"")+"").onChange(e=>{this.plugin.settings.lineWidthWide=parseInt(e.trim()),this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new a.Setting(t).setName("Maximum line width %").setDesc("Percentage of space inside a pane that a line can fill (default 88).").addText(i=>i.setPlaceholder("88").setValue((this.plugin.settings.maxWidth||"")+"").onChange(e=>{this.plugin.settings.maxWidth=parseInt(e.trim()),this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new a.Setting(t).setName("Editor font").setDesc("Overrides the text font defined in Obsidian Appearance settings when in edit mode.").addText(i=>i.setPlaceholder("").setValue((this.plugin.settings.editorFont||"")+"").onChange(e=>{this.plugin.settings.editorFont=e,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()}))}};var S=class extends C.Plugin{onload(){return p(this,null,function*(){yield this.loadSettings(),this.addSettingTab(new b(this.app,this)),this.loadRules();let t=()=>{let i=this.app.vault.getConfig("baseFontSize");this.settings.textNormal=i;let e=!1,w=!1,k=!1;this.app.vault.getConfig("foldHeading")?(this.settings.folding=!0,console.log("Folding is on"),e=!0):(this.settings.folding=!1,console.log("Folding is off")),this.app.vault.getConfig("showLineNumber")?(this.settings.lineNumbers=!0,console.log("Line numbers are on"),w=!0):(this.settings.lineNumbers=!1,console.log("Line numbers are off")),this.app.vault.getConfig("readableLineLength")?(this.settings.readableLineLength=!0,console.log("Readable line length is on"),k=!0):(this.settings.readableLineLength=!1,console.log("Readable line length is off"));let c=document.body.classList;c.toggle("minimal-folding",e),c.toggle("minimal-line-nums",w),c.toggle("minimal-readable",k),c.toggle("minimal-readable-off",!k),this.saveData(this.settings)},s=()=>{let i=document.getElementsByClassName("mod-left-split")[0],e=document.getElementsByClassName("side-dock-ribbon")[0];i&&e&&document.body.classList.contains("theme-light")&&this.settings.lightStyle=="minimal-light-contrast"?(i.addClass("theme-dark"),e.addClass("theme-dark")):i&&e&&(i.removeClass("theme-dark"),e.removeClass("theme-dark"))};this.registerEvent(app.vault.on("config-changed",t)),this.registerEvent(app.workspace.on("css-change",s)),t(),app.workspace.onLayoutReady(()=>{s()});let l=["minimal-light","minimal-light-tonal","minimal-light-contrast","minimal-light-white"],d=["minimal-dark","minimal-dark-tonal","minimal-dark-black"],f=["img-grid","img-grid-ratio","img-nogrid"],g=["table-100","table-default-width","table-wide","table-max"],m=["iframe-100","iframe-default-width","iframe-wide","iframe-max"],h=["img-100","img-default-width","img-wide","img-max"],o=["map-100","map-default-width","map-wide","map-max"],r=["chart-100","chart-default-width","chart-wide","chart-max"];this.addCommand({id:"increase-body-font-size",name:"Increase body font size",callback:()=>{this.settings.textNormal=this.settings.textNormal+.5,this.saveData(this.settings),this.setFontSize()}}),this.addCommand({id:"decrease-body-font-size",name:"Decrease body font size",callback:()=>{this.settings.textNormal=this.settings.textNormal-.5,this.saveData(this.settings),this.setFontSize()}}),this.addCommand({id:"toggle-minimal-dark-cycle",name:"Cycle between dark mode styles",callback:()=>{this.settings.darkStyle=d[(d.indexOf(this.settings.darkStyle)+1)%d.length],this.saveData(this.settings),this.updateDarkStyle()}}),this.addCommand({id:"toggle-minimal-light-cycle",name:"Cycle between light mode styles",callback:()=>{this.settings.lightStyle=l[(l.indexOf(this.settings.lightStyle)+1)%l.length],this.saveData(this.settings),this.updateLightStyle()}}),this.addCommand({id:"toggle-hidden-borders",name:"Toggle sidebar borders",callback:()=>{this.settings.bordersToggle=!this.settings.bordersToggle,this.saveData(this.settings),this.refresh()}}),this.addCommand({id:"toggle-colorful-headings",name:"Toggle colorful headings",callback:()=>{this.settings.colorfulHeadings=!this.settings.colorfulHeadings,this.saveData(this.settings),this.refresh()}}),this.addCommand({id:"toggle-minimal-focus-mode",name:"Toggle focus mode",callback:()=>{this.settings.focusMode=!this.settings.focusMode,this.saveData(this.settings),this.refresh()}}),this.addCommand({id:"toggle-minimal-colorful-frame",name:"Toggle colorful window frame",callback:()=>{this.settings.colorfulFrame=!this.settings.colorfulFrame,this.saveData(this.settings),this.refresh()}}),this.addCommand({id:"cycle-minimal-table-width",name:"Cycle between table width options",callback:()=>{this.settings.tableWidth=g[(g.indexOf(this.settings.tableWidth)+1)%g.length],this.saveData(this.settings),this.refresh()}}),this.addCommand({id:"cycle-minimal-image-width",name:"Cycle between image width options",callback:()=>{this.settings.imgWidth=h[(h.indexOf(this.settings.imgWidth)+1)%h.length],this.saveData(this.settings),this.refresh()}}),this.addCommand({id:"cycle-minimal-iframe-width",name:"Cycle between iframe width options",callback:()=>{this.settings.iframeWidth=m[(m.indexOf(this.settings.iframeWidth)+1)%m.length],this.saveData(this.settings),this.refresh()}}),this.addCommand({id:"cycle-minimal-chart-width",name:"Cycle between chart width options",callback:()=>{this.settings.chartWidth=r[(r.indexOf(this.settings.chartWidth)+1)%r.length],this.saveData(this.settings),this.refresh()}}),this.addCommand({id:"cycle-minimal-map-width",name:"Cycle between map width options",callback:()=>{this.settings.mapWidth=o[(o.indexOf(this.settings.mapWidth)+1)%o.length],this.saveData(this.settings),this.refresh()}}),this.addCommand({id:"toggle-minimal-img-grid",name:"Toggle image grids",callback:()=>{this.settings.imgGrid=!this.settings.imgGrid,this.saveData(this.settings),this.refresh()}}),this.addCommand({id:"toggle-minimal-switch",name:"Switch between light and dark mode",callback:()=>{this.updateTheme()}}),this.addCommand({id:"toggle-minimal-light-default",name:"Use light mode (default)",callback:()=>{this.settings.lightStyle="minimal-light",this.saveData(this.settings),this.updateLightStyle()}}),this.addCommand({id:"toggle-minimal-light-white",name:"Use light mode (all white)",callback:()=>{this.settings.lightStyle="minimal-light-white",this.saveData(this.settings),this.updateLightStyle()}}),this.addCommand({id:"toggle-minimal-light-tonal",name:"Use light mode (low contrast)",callback:()=>{this.settings.lightStyle="minimal-light-tonal",this.saveData(this.settings),this.updateLightStyle()}}),this.addCommand({id:"toggle-minimal-light-contrast",name:"Use light mode (high contrast)",callback:()=>{this.settings.lightStyle="minimal-light-contrast",this.saveData(this.settings),this.updateLightStyle()}}),this.addCommand({id:"toggle-minimal-dark-default",name:"Use dark mode (default)",callback:()=>{this.settings.darkStyle="minimal-dark",this.saveData(this.settings),this.updateDarkStyle()}}),this.addCommand({id:"toggle-minimal-dark-tonal",name:"Use dark mode (low contrast)",callback:()=>{this.settings.darkStyle="minimal-dark-tonal",this.saveData(this.settings),this.updateDarkStyle()}}),this.addCommand({id:"toggle-minimal-dark-black",name:"Use dark mode (true black)",callback:()=>{this.settings.darkStyle="minimal-dark-black",this.saveData(this.settings),this.updateDarkStyle()}}),this.addCommand({id:"toggle-minimal-atom-light",name:"Switch light color scheme to Atom (light)",callback:()=>{this.settings.lightScheme="minimal-atom-light",this.saveData(this.settings),this.updateLightScheme(),this.updateLightStyle()}}),this.addCommand({id:"toggle-minimal-ayu-light",name:"Switch light color scheme to Ayu (light)",callback:()=>{this.settings.lightScheme="minimal-ayu-light",this.saveData(this.settings),this.updateLightScheme(),this.updateLightStyle()}}),this.addCommand({id:"toggle-minimal-catppuccin-light",name:"Switch light color scheme to Catppuccin (light)",callback:()=>{this.settings.lightScheme="minimal-catppuccin-light",this.saveData(this.settings),this.updateLightScheme(),this.updateLightStyle()}}),this.addCommand({id:"toggle-minimal-default-light",name:"Switch light color scheme to default (light)",callback:()=>{this.settings.lightScheme="minimal-default-light",this.saveData(this.settings),this.updateLightScheme(),this.updateLightStyle()}}),this.addCommand({id:"toggle-minimal-gruvbox-light",name:"Switch light color scheme to Gruvbox (light)",callback:()=>{this.settings.lightScheme="minimal-gruvbox-light",this.saveData(this.settings),this.updateLightScheme(),this.updateLightStyle()}}),this.addCommand({id:"toggle-minimal-eink-light",name:"Switch light color scheme to E-ink (light)",callback:()=>{this.settings.lightScheme="minimal-eink-light",this.saveData(this.settings),this.updateLightScheme(),this.updateLightStyle()}}),this.addCommand({id:"toggle-minimal-everforest-light",name:"Switch light color scheme to Everforest (light)",callback:()=>{this.settings.lightScheme="minimal-everforest-light",this.saveData(this.settings),this.updateLightScheme(),this.updateLightStyle()}}),this.addCommand({id:"toggle-minimal-flexoki-light",name:"Switch light color scheme to Flexoki (light)",callback:()=>{this.settings.lightScheme="minimal-flexoki-light",this.saveData(this.settings),this.updateLightScheme(),this.updateLightStyle()}}),this.addCommand({id:"toggle-minimal-macos-light",name:"Switch light color scheme to macOS (light)",callback:()=>{this.settings.lightScheme="minimal-macos-light",this.saveData(this.settings),this.updateLightScheme(),this.updateLightStyle()}}),this.addCommand({id:"toggle-minimal-notion-light",name:"Switch light color scheme to Sky (light)",callback:()=>{this.settings.lightScheme="minimal-notion-light",this.saveData(this.settings),this.updateLightScheme(),this.updateLightStyle()}}),this.addCommand({id:"toggle-minimal-nord-light",name:"Switch light color scheme to Nord (light)",callback:()=>{this.settings.lightScheme="minimal-nord-light",this.saveData(this.settings),this.updateLightScheme(),this.updateLightStyle()}}),this.addCommand({id:"toggle-minimal-rose-pine-light",name:"Switch light color scheme to Ros\xE9 Pine (light)",callback:()=>{this.settings.lightScheme="minimal-rose-pine-light",this.saveData(this.settings),this.updateLightScheme(),this.updateLightStyle()}}),this.addCommand({id:"toggle-minimal-solarized-light",name:"Switch light color scheme to Solarized (light)",callback:()=>{this.settings.lightScheme="minimal-solarized-light",this.saveData(this.settings),this.updateLightScheme(),this.updateLightStyle()}}),this.addCommand({id:"toggle-minimal-things-light",name:"Switch light color scheme to Things (light)",callback:()=>{this.settings.lightScheme="minimal-things-light",this.saveData(this.settings),this.updateLightScheme(),this.updateLightStyle()}}),this.addCommand({id:"toggle-minimal-atom-dark",name:"Switch dark color scheme to Atom (dark)",callback:()=>{this.settings.darkScheme="minimal-atom-dark",this.saveData(this.settings),this.updateDarkScheme(),this.updateDarkStyle()}}),this.addCommand({id:"toggle-minimal-ayu-dark",name:"Switch dark color scheme to Ayu (dark)",callback:()=>{this.settings.darkScheme="minimal-ayu-dark",this.saveData(this.settings),this.updateDarkScheme(),this.updateDarkStyle()}}),this.addCommand({id:"toggle-minimal-catppuccin-dark",name:"Switch dark color scheme to Catppuccin (dark)",callback:()=>{this.settings.darkScheme="minimal-catppuccin-dark",this.saveData(this.settings),this.updateDarkScheme(),this.updateDarkStyle()}}),this.addCommand({id:"toggle-minimal-dracula-dark",name:"Switch dark color scheme to Dracula (dark)",callback:()=>{this.settings.darkScheme="minimal-dracula-dark",this.saveData(this.settings),this.updateDarkScheme(),this.updateDarkStyle()}}),this.addCommand({id:"toggle-minimal-default-dark",name:"Switch dark color scheme to default (dark)",callback:()=>{this.settings.darkScheme="minimal-default-dark",this.saveData(this.settings),this.updateDarkScheme(),this.updateDarkStyle()}}),this.addCommand({id:"toggle-minimal-eink-dark",name:"Switch dark color scheme to E-ink (dark)",callback:()=>{this.settings.darkScheme="minimal-eink-dark",this.saveData(this.settings),this.updateDarkScheme(),this.updateDarkStyle()}}),this.addCommand({id:"toggle-minimal-everforest-dark",name:"Switch dark color scheme to Everforest (dark)",callback:()=>{this.settings.darkScheme="minimal-everforest-dark",this.saveData(this.settings),this.updateDarkScheme(),this.updateDarkStyle()}}),this.addCommand({id:"toggle-minimal-flexoki-dark",name:"Switch dark color scheme to Flexoki (dark)",callback:()=>{this.settings.darkScheme="minimal-flexoki-dark",this.saveData(this.settings),this.updateDarkScheme(),this.updateDarkStyle()}}),this.addCommand({id:"toggle-minimal-gruvbox-dark",name:"Switch dark color scheme to Gruvbox (dark)",callback:()=>{this.settings.darkScheme="minimal-gruvbox-dark",this.saveData(this.settings),this.updateDarkScheme(),this.updateDarkStyle()}}),this.addCommand({id:"toggle-minimal-macos-dark",name:"Switch dark color scheme to macOS (dark)",callback:()=>{this.settings.darkScheme="minimal-macos-dark",this.saveData(this.settings),this.updateDarkScheme(),this.updateDarkStyle()}}),this.addCommand({id:"toggle-minimal-nord-dark",name:"Switch dark color scheme to Nord (dark)",callback:()=>{this.settings.darkScheme="minimal-nord-dark",this.saveData(this.settings),this.updateDarkScheme(),this.updateDarkStyle()}}),this.addCommand({id:"toggle-minimal-notion-dark",name:"Switch dark color scheme to Sky (dark)",callback:()=>{this.settings.darkScheme="minimal-notion-dark",this.saveData(this.settings),this.updateDarkScheme(),this.updateDarkStyle()}}),this.addCommand({id:"toggle-minimal-rose-pine-dark",name:"Switch dark color scheme to Ros\xE9 Pine (dark)",callback:()=>{this.settings.darkScheme="minimal-rose-pine-dark",this.saveData(this.settings),this.updateDarkScheme(),this.updateDarkStyle()}}),this.addCommand({id:"toggle-minimal-solarized-dark",name:"Switch dark color scheme to Solarized (dark)",callback:()=>{this.settings.darkScheme="minimal-solarized-dark",this.saveData(this.settings),this.updateDarkScheme(),this.updateDarkStyle()}}),this.addCommand({id:"toggle-minimal-things-dark",name:"Switch dark color scheme to Things (dark)",callback:()=>{this.settings.darkScheme="minimal-things-dark",this.saveData(this.settings),this.updateDarkScheme(),this.updateDarkStyle()}}),this.addCommand({id:"toggle-minimal-dev-block-width",name:"Dev \u2014 Show block widths",callback:()=>{this.settings.devBlockWidth=!this.settings.devBlockWidth,this.saveData(this.settings),this.refresh()}}),this.refresh()})}onunload(){console.log("Unloading Minimal Theme Settings plugin");let t=document.getElementsByClassName("mod-left-split")[0];t&&t.removeClass("theme-dark");let s=document.getElementsByClassName("side-dock-ribbon")[0];s&&s.removeClass("theme-dark"),this.unloadRules(),this.removeStyle(),this.removeSettings(),this.removeLightScheme(),this.removeDarkScheme()}loadSettings(){return p(this,null,function*(){this.settings=Object.assign(D,yield this.loadData())})}saveSettings(){return p(this,null,function*(){yield this.saveData(this.settings)})}refresh(){this.updateStyle()}loadRules(){let t=document.createElement("style");t.id="minimal-theme",document.getElementsByTagName("head")[0].appendChild(t),document.body.classList.add("minimal-theme"),this.updateStyle()}unloadRules(){let t=document.getElementById("minimal-theme");t&&t.parentNode.removeChild(t),document.body.classList.remove("minimal-theme")}setFontSize(){this.app.vault.setConfig("baseFontSize",this.settings.textNormal),this.app.updateFontSize()}updateStyle(){this.removeStyle(),this.removeSettings(),document.body.addClass(this.settings.lightStyle,this.settings.lightScheme,this.settings.darkStyle,this.settings.darkScheme),document.body.classList.toggle("borders-none",!this.settings.bordersToggle),document.body.classList.toggle("colorful-headings",this.settings.colorfulHeadings),document.body.classList.toggle("colorful-frame",this.settings.colorfulFrame),document.body.classList.toggle("colorful-active",this.settings.colorfulActiveStates),document.body.classList.toggle("minimal-focus-mode",this.settings.focusMode),document.body.classList.toggle("links-int-on",this.settings.underlineInternal),document.body.classList.toggle("links-ext-on",this.settings.underlineExternal),document.body.classList.toggle("full-width-media",this.settings.fullWidthMedia),document.body.classList.toggle("img-grid",this.settings.imgGrid),document.body.classList.toggle("minimal-dev-block-width",this.settings.devBlockWidth),document.body.classList.toggle("minimal-status-off",!this.settings.minimalStatus),document.body.classList.toggle("full-file-names",!this.settings.trimNames),document.body.classList.toggle("labeled-nav",this.settings.labeledNav),document.body.classList.toggle("minimal-folding",this.settings.folding),document.body.addClass(this.settings.chartWidth,this.settings.tableWidth,this.settings.imgWidth,this.settings.iframeWidth,this.settings.mapWidth);let t=document.getElementById("minimal-theme");if(t)t.innerText="body.minimal-theme{--font-ui-small:"+this.settings.textSmall+"px;--line-height:"+this.settings.lineHeight+";--line-width:"+this.settings.lineWidth+"rem;--line-width-wide:"+this.settings.lineWidthWide+"rem;--max-width:"+this.settings.maxWidth+"%;--font-editor-override:"+this.settings.editorFont+";";else throw"minimal-theme element not found!"}updateDarkStyle(){document.body.removeClass("theme-light","minimal-dark","minimal-dark-tonal","minimal-dark-black"),document.body.addClass("theme-dark",this.settings.darkStyle),this.app.vault.getConfig("theme")!=="system"&&(this.app.setTheme("obsidian"),this.app.vault.setConfig("theme","obsidian")),this.app.workspace.trigger("css-change")}updateLightStyle(){document.body.removeClass("theme-dark","minimal-light","minimal-light-tonal","minimal-light-contrast","minimal-light-white"),document.body.addClass("theme-light",this.settings.lightStyle),this.app.vault.getConfig("theme")!=="system"&&(this.app.setTheme("moonstone"),this.app.vault.setConfig("theme","moonstone")),this.app.workspace.trigger("css-change")}updateDarkScheme(){this.removeDarkScheme(),document.body.addClass(this.settings.darkScheme)}updateLightScheme(){this.removeLightScheme(),document.body.addClass(this.settings.lightScheme)}updateTheme(){if(this.app.vault.getConfig("theme")==="system")document.body.classList.contains("theme-light")?(document.body.removeClass("theme-light"),document.body.addClass("theme-dark")):(document.body.removeClass("theme-dark"),document.body.addClass("theme-light"));else{document.body.classList.contains("theme-light")?(document.body.removeClass("theme-light"),document.body.addClass("theme-dark")):(document.body.removeClass("theme-dark"),document.body.addClass("theme-light"));let s=this.app.vault.getConfig("theme")==="moonstone"?"obsidian":"moonstone";this.app.setTheme(s),this.app.vault.setConfig("theme",s)}this.app.workspace.trigger("css-change")}removeSettings(){document.body.removeClass("borders-none","colorful-headings","colorful-frame","colorful-active","minimal-focus-mode","links-int-on","links-ext-on","full-width-media","img-grid","minimal-dev-block-width","minimal-status-off","full-file-names","labeled-nav","minimal-folding"),document.body.removeClass("table-wide","table-max","table-100","table-default-width","iframe-wide","iframe-max","iframe-100","iframe-default-width","img-wide","img-max","img-100","img-default-width","chart-wide","chart-max","chart-100","chart-default-width","map-wide","map-max","map-100","map-default-width")}removeStyle(){document.body.removeClass("minimal-light","minimal-light-tonal","minimal-light-contrast","minimal-light-white","minimal-dark","minimal-dark-tonal","minimal-dark-black")}removeDarkScheme(){document.body.removeClass("minimal-atom-dark","minimal-ayu-dark","minimal-catppuccin-dark","minimal-default-dark","minimal-dracula-dark","minimal-eink-dark","minimal-everforest-dark","minimal-flexoki-dark","minimal-gruvbox-dark","minimal-macos-dark","minimal-nord-dark","minimal-notion-dark","minimal-rose-pine-dark","minimal-solarized-dark","minimal-things-dark")}removeLightScheme(){document.body.removeClass("minimal-atom-light","minimal-ayu-light","minimal-catppuccin-light","minimal-default-light","minimal-eink-light","minimal-everforest-light","minimal-flexoki-light","minimal-gruvbox-light","minimal-macos-light","minimal-nord-light","minimal-notion-light","minimal-rose-pine-light","minimal-solarized-light","minimal-things-light")}};

/* nosourcemap */