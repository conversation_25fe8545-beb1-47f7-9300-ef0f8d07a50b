/*
THIS IS A GENERATED/BUNDLED FILE BY ROLLUP
if you want to view the source visit the plugins github repository
*/

'use strict';

var obsidian = require('obsidian');

/******************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERC<PERSON>NTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, <PERSON><PERSON><PERSON><PERSON>ENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */

function __awaiter(thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
}

class FocusMode extends obsidian.Plugin {
    constructor() {
        super(...arguments);
        this.focusModeActive = false;
        this.maximisedClass = "maximised";
        this.focusModeClass = "focus-mode";
        this.superFocusModeClass = "super-focus-mode";
    }
    storeSplitsValues() {
        // @ts-ignore
        this.leftSplitCollapsed = this.app.workspace.leftSplit.collapsed;
        // @ts-ignore
        this.rightSplitCollapsed = this.app.workspace.rightSplit.collapsed;
    }
    collapseSplits() {
        // @ts-ignore
        this.app.workspace.leftSplit.collapse();
        // @ts-ignore
        this.app.workspace.rightSplit.collapse();
    }
    restoreSplits() {
        if (!this.leftSplitCollapsed) {
            // @ts-ignore
            this.app.workspace.leftSplit.expand();
        }
        if (!this.rightSplitCollapsed) {
            // @ts-ignore
            this.app.workspace.rightSplit.expand();
        }
    }
    removeExtraneousClasses() {
        if (
        // @ts-ignore
        this.app.workspace.rootSplit.containerEl.hasClass(this.maximisedClass)) {
            // @ts-ignore
            this.app.workspace.rootSplit.containerEl.removeClass(this.maximisedClass);
            // @ts-ignore
            this.app.workspace.onLayoutChange();
        }
        if (document.body.classList.contains(this.superFocusModeClass)) {
            document.body.classList.remove(this.superFocusModeClass);
        }
    }
    sharedFocusModeCommands() {
        this.focusModeActive = true;
        // @ts-ignore
        this.app.on("active-leaf-change", () => {
            try {
                // @ts-ignore
                this.app.workspace.activeLeaf.view.editor.blur();
                // @ts-ignore
                this.app.workspace.activeLeaf.view.editor.focus();
                // @ts-ignore
                this.app.workspace.activeLeaf.view.editor.refresh();
            }
            catch (ignore) { }
        });
        if (!document.body.classList.contains(this.focusModeClass)) {
            this.storeSplitsValues();
        }
        this.collapseSplits();
    }
    enableSuperFocusMode() {
        this.sharedFocusModeCommands();
        // @ts-ignore
        this.app.workspace.rootSplit.containerEl.toggleClass(this.maximisedClass, 
        // @ts-ignore
        !this.app.workspace.rootSplit.containerEl.hasClass(this.maximisedClass));
        document.body.classList.toggle(this.superFocusModeClass, !document.body.classList.contains(this.superFocusModeClass));
        if (!document.body.classList.contains(this.focusModeClass)) {
            document.body.classList.add(this.focusModeClass);
        }
        if (document.body.classList.contains(this.superFocusModeClass)) {
            Array.from(document.querySelectorAll(`.${this.superFocusModeClass} .workspace-split`)).forEach((node) => {
                const theNode = node;
                const hasActiveKids = theNode.querySelector(".mod-active");
                if (hasActiveKids) {
                    theNode.style.display = "flex";
                }
                else {
                    theNode.style.display = "none";
                }
            });
        }
        // @ts-ignore
        this.app.workspace.onLayoutChange();
    }
    enableFocusMode() {
        this.sharedFocusModeCommands();
        this.removeExtraneousClasses();
        document.body.classList.toggle(this.focusModeClass, !document.body.classList.contains(this.focusModeClass));
    }
    disableFocusMode() {
        this.removeExtraneousClasses();
        if (document.body.classList.contains(this.focusModeClass)) {
            document.body.classList.remove(this.focusModeClass);
        }
        this.restoreSplits();
        Array.from(document.querySelectorAll(".workspace-split")).forEach((node) => {
            const theNode = node;
            theNode.style.display = "flex";
        });
        this.focusModeActive = false;
    }
    toggleFocusMode(superFocus = false) {
        if (superFocus) {
            this.enableSuperFocusMode();
        }
        else if (this.focusModeActive) {
            this.disableFocusMode();
        }
        else {
            this.enableFocusMode();
        }
    }
    onload() {
        return __awaiter(this, void 0, void 0, function* () {
            console.log("Loading Focus Mode plugin ...");
            this.addRibbonIcon("enter", "Toggle Focus Mode (Shift + Click to show active pane only)", (event) => {
                this.toggleFocusMode(event.shiftKey);
            });
            this.addCommand({
                id: "toggle-focus-mode",
                name: "Toggle Focus Mode",
                callback: () => {
                    this.toggleFocusMode();
                },
                hotkeys: [{ modifiers: ["Alt", "Mod"], key: "Z" }],
            });
            this.addCommand({
                id: "toggle-super-focus-mode",
                name: "Toggle Super Focus Mode (Active pane only)",
                callback: () => {
                    this.toggleFocusMode(true);
                },
                hotkeys: [{ modifiers: ["Alt", "Mod", "Shift"], key: "Z" }],
            });
        });
    }
    onunload() {
        console.log("Unloading Focus Mode plugin ...");
    }
}

module.exports = FocusMode;


/* nosourcemap */