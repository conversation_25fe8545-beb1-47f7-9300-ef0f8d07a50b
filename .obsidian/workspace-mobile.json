{"main": {"id": "e4728e2e07fdae38", "type": "split", "children": [{"id": "18dcc08a571975d5", "type": "tabs", "children": [{"id": "e3f1e4b9a07134b1", "type": "leaf", "state": {"type": "markdown", "state": {"file": "AA 处理方法/1.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "1"}}, {"id": "a94ff0d4c7b10f22", "type": "leaf", "state": {"type": "markdown", "state": {"file": "1-Rough Notes/Todo/随机检测点小程序.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "随机检测点小程序"}}, {"id": "0be0b4872399b3ef", "type": "leaf", "state": {"type": "markdown", "state": {"file": "随机检测选点.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "随机检测选点"}}, {"id": "632ee0b7b08989d3", "type": "leaf", "state": {"type": "markdown", "state": {"file": "1-Rough Notes/Todo/随机检测点小程序.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "随机检测点小程序"}}, {"id": "3262691e0b111de4", "type": "leaf", "state": {"type": "markdown", "state": {"file": "计划.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "计划"}}, {"id": "a3b42f176f40d473", "type": "leaf", "state": {"type": "markdown", "state": {"file": "银行.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "银行"}}, {"id": "174e1e7c9ce4ab5c", "type": "leaf", "state": {"type": "markdown", "state": {"file": "5-Full Notes/斑马小超小程序开发要求文档.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "斑马小超小程序开发要求文档"}}, {"id": "d4814918b2794ff8", "type": "leaf", "state": {"type": "markdown", "state": {"file": "5-Full Notes/提示词.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "提示词"}}, {"id": "05ff94b6f8cd1952", "type": "leaf", "state": {"type": "markdown", "state": {"file": "提示词22.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "提示词22"}}, {"id": "f3820a778c02034b", "type": "leaf", "state": {"type": "markdown", "state": {"file": "最终版提示词.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "最终版提示词"}}, {"id": "942c0e9f31bccb07", "type": "leaf", "state": {"type": "markdown", "state": {"file": "小说/大纲/总体大纲.md", "mode": "preview", "source": false}, "icon": "lucide-file", "title": "总体大纲"}}, {"id": "f9811541619cd6b2", "type": "leaf", "state": {"type": "markdown", "state": {"file": "小说/大纲/总体大纲.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "总体大纲"}}, {"id": "9299ef83d6e92436", "type": "leaf", "state": {"type": "markdown", "state": {"file": "1.AAA_开发流程/小程序/小程序手机端前端设计.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "小程序手机端前端设计"}}, {"id": "b91ecc68dd28e384", "type": "leaf", "state": {"type": "markdown", "state": {"file": "英语vue2基础架构.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "英语vue2基础架构"}}, {"id": "20fcc024856af8a7", "type": "leaf", "state": {"type": "markdown", "state": {"file": "5-Full Notes/斑马小超小程序开发要求文档.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "斑马小超小程序开发要求文档"}}, {"id": "9e154935d6081718", "type": "leaf", "state": {"type": "markdown", "state": {"file": "ai开发.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "ai开发"}}, {"id": "30e043822d28a85a", "type": "leaf", "state": {"type": "markdown", "state": {"file": "思维导图ai.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "思维导图ai"}}, {"id": "0d5ff4975fbdb310", "type": "leaf", "state": {"type": "markdown", "state": {"file": "1.AAA_开发流程/cursor rules.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "cursor rules"}}, {"id": "1d559914b1f935be", "type": "leaf", "state": {"type": "markdown", "state": {"file": "未命名 1.md", "mode": "preview", "source": false}, "icon": "lucide-file", "title": "未命名 1"}}, {"id": "80fcbbbdf7bd720e", "type": "leaf", "state": {"type": "markdown", "state": {"file": "5-Full Notes/投票.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "投票"}}, {"id": "bef52a96d17c6b38", "type": "leaf", "state": {"type": "markdown", "state": {"file": "mcp构建/详细构建方案.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "详细构建方案"}}, {"id": "2f21e76a1b17b7c5", "type": "leaf", "state": {"type": "markdown", "state": {"file": "mcp构建/global_standards.md.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "global_standards.md"}}, {"id": "6210950eca6d618b", "type": "leaf", "state": {"type": "markdown", "state": {"file": "mcp构建/全部角色卡.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "全部角色卡"}}, {"id": "b574adcff79983f4", "type": "leaf", "state": {"type": "markdown", "state": {"file": "mcp构建/用户视角.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "用户视角"}}, {"id": "d0890c4b7ffef914", "type": "leaf", "state": {"type": "markdown", "state": {"file": "mcp构建/开发视角.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "开发视角"}}, {"id": "50798aaa79c3bcbb", "type": "leaf", "state": {"type": "markdown", "state": {"file": "mcp构建/开发流程遵守.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "开发流程遵守"}}, {"id": "3dd53abece9692d3", "type": "leaf", "state": {"type": "markdown", "state": {"file": "mcp构建/开发流程遵守.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "开发流程遵守"}}, {"id": "73cc329ebedee8d9", "type": "leaf", "state": {"type": "markdown", "state": {"file": "mcp构建/配置使用方式.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "配置使用方式"}}, {"id": "fc04188cca650e50", "type": "leaf", "state": {"type": "markdown", "state": {"file": "mcp构建/用户视角.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "用户视角"}}, {"id": "84d4fd47a6edac5c", "type": "leaf", "state": {"type": "markdown", "state": {"file": "mcp构建/16-迭代计划.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "16-迭代计划"}}, {"id": "6bffafadf8b4a376", "type": "leaf", "state": {"type": "markdown", "state": {"file": "mcp构建/16-迭代后的工具数量.md", "mode": "preview", "source": false}, "icon": "lucide-file", "title": "16-迭代后的工具数量"}}, {"id": "ea41b37946f5ab7e", "type": "leaf", "state": {"type": "markdown", "state": {"file": "mcp构建/16-详细开发清单.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "16-详细开发清单"}}, {"id": "e74fae0c3fb4cd35", "type": "leaf", "state": {"type": "markdown", "state": {"file": "mcp构建/16-oes.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "16-oes"}}, {"id": "1e3739dcf929932d", "type": "leaf", "state": {"type": "markdown", "state": {"file": "mcp构建/16-角色卡与全局规范.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "16-角色卡与全局规范"}}, {"id": "670ac7e2791c5748", "type": "leaf", "state": {"type": "markdown", "state": {"file": "mcp构建/16-内置方法论.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "16-内置方法论"}}, {"id": "2d1a29fda04f0fb6", "type": "leaf", "state": {"type": "markdown", "state": {"file": "mcp构建/开发视角.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "开发视角"}}, {"id": "94ce0e091c46bdad", "type": "leaf", "state": {"type": "markdown", "state": {"file": "mcp构建/17-再次优化方案.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "17-再次优化方案"}}, {"id": "814311229d2f7da8", "type": "leaf", "state": {"type": "markdown", "state": {"file": "mcp构建/17-迭代方案.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "17-迭代方案"}}, {"id": "e42d2e30644d3991", "type": "leaf", "state": {"type": "markdown", "state": {"file": "mcp构建/17-迭代方案.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "17-迭代方案"}}, {"id": "5a2d717f0fc1c11e", "type": "leaf", "state": {"type": "markdown", "state": {"file": "0-mcp构建/未命名 2.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "未命名 2"}}, {"id": "6a175e6bcc91d3ea", "type": "leaf", "state": {"type": "markdown", "state": {"file": "api/README_PURCHASE_REBATE.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "README_PURCHASE_REBATE"}}, {"id": "dfcd1b2a6dbceb07", "type": "leaf", "state": {"type": "markdown", "state": {"file": "刻意练习.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "刻意练习"}}, {"id": "af6ea0f76172b657", "type": "leaf", "state": {"type": "markdown", "state": {"file": "Peakpal.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "<PERSON>pal"}}], "currentTab": 42}], "direction": "vertical"}, "left": {"id": "0f2da78cd5e20a3d", "type": "mobile-drawer", "children": [{"id": "f50e33e5a25b9338", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "文件列表"}}, {"id": "74973f45dcc62b4f", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "搜索"}}, {"id": "05fb74f18d6a43f8", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true}, "icon": "lucide-tags", "title": "标签"}}, {"id": "bc39c0c8ee04c89f", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "书签"}}], "currentTab": 0}, "right": {"id": "ff0af3e667401ae1", "type": "mobile-drawer", "children": [{"id": "b22329d2d11c2e53", "type": "leaf", "state": {"type": "backlink", "state": {"file": "mcp构建/开发视角.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "反向链接"}}, {"id": "a2010f0411933312", "type": "leaf", "state": {"type": "outgoing-link", "state": {"linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "出链"}}, {"id": "3d118de99825eeba", "type": "leaf", "state": {"type": "outline", "state": {}, "icon": "lucide-list", "title": "大纲"}}, {"id": "8cbeec69292a159b", "type": "leaf", "state": {"type": "calendar", "state": {}, "icon": "calendar-with-checkmark", "title": "Calendar"}}], "currentTab": 0}, "left-ribbon": {"hiddenItems": {"markdown-importer:打开 Markdown 格式转换器": false, "switcher:打开快速切换": false, "graph:查看关系图谱": false, "canvas:新建白板": false, "daily-notes:打开/创建今天的日记": false, "templates:插入模板": false, "command-palette:打开命令面板": false, "periodic-notes:Open today": false}}, "active": "af6ea0f76172b657", "lastOpenFiles": ["刻意练习.md", "Peakpal.md", "api/README_PURCHASE_REBATE.md", "api/PURCHASE_REBATE_IMPLEMENTATION_REPORT.md", "接单.md", "api/test-README.md", "api", "0-mcp构建/未命名 2.md", "0-mcp构建/未命名 1.md", "0-mcp构建/16/未命名.md", "0-mcp构建/16/16.2.md", "mcp构建/17-迭代方案.md", "mcp构建/17-再次优化方案.md", "mcp构建/开发视角.md", "mcp构建/开发流程遵守.md", "mcp构建/详细构建方案.md", "mcp构建/16-内置方法论.md", "mcp构建/16-角色卡与全局规范.md", "mcp构建/16-oes.md", "mcp构建/16-核心工具和伪代码.md", "mcp构建/16-详细开发清单.md", "mcp构建/16-迭代后的工具数量.md", "mcp构建/16-迭代计划.md", "mcp构建/用户视角.md", "总览.md", "mcp构建/配置使用方式.md", "自动化工作流", "mcp构建/项目核心源码开发实例.md", "mcp构建", "AA 处理方法", "9-Mind Maps/Images/PNG/测试分类.png", "9-Mind Maps/Images/PNG/车辆管理.png", "Obsidian/7-Flashcards", "Obsidian/6-Reviews/定期复习/Weekly", "Obsidian/6-Reviews/定期复习/Daily", "Obsidian/6-Reviews/定期复习", "Obsidian/6-Reviews", "Obsidian/5-Full Notes/其他", "Obsidian/5-Full Notes/Attachements-附件/695dafd81baa45e164299f5de397828e.png", "Obsidian/5-Full Notes/Attachements-附件/1669342349202-73056be5-24f3-475a-a967-0b0d9787d91d-263424.png", "Obsidian/5-Full Notes/Attachements-附件/1648877855554-79d2161f-11c4-4563-a088-123f04e4a39b-844735.png", "Obsidian/5-Full Notes/Attachements-附件/1648811275588-dfc2f797-6315-45f8-8fd1-7dd0b98e4f77-186122.png", "Obsidian/5-Full Notes/Attachements-附件/1648705468906-f521804c-6607-4098-a851-e32d62b9dc77-129399.png", "Obsidian/5-Full Notes/Attachements-附件/1648699070613-e92d3c36-93d2-43a7-b054-75627ce080f3-839396.png", "Obsidian/5-Full Notes/Attachements-附件/1648522378427-01ec486c-46db-4740-85ad-58140896a429-327201.png", "Obsidian/5-Full Notes/Attachements-附件/1648285462408-4a1173dd-22d2-4d71-827e-43a8d5b70b52-368982.png", "4-Indexes/未命名.canvas"]}