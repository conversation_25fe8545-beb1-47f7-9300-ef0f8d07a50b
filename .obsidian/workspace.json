{"main": {"id": "092692333b1f4ec8", "type": "split", "children": [{"id": "32e5f5692ab63f3e", "type": "tabs", "children": [{"id": "0a5144ca886e1530", "type": "leaf", "state": {"type": "empty", "state": {}, "icon": "lucide-file", "title": "新标签页"}}]}], "direction": "vertical"}, "left": {"id": "dd6b89ccaace2080", "type": "split", "children": [{"id": "2237d4c2e9836508", "type": "tabs", "children": [{"id": "1bbe8733b84546a5", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "文件列表"}}, {"id": "bd8802151fc63dfd", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "搜索"}}, {"id": "4c3d99ef089ed882", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "书签"}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "6fda84fe0e5b91f7", "type": "split", "children": [{"id": "2b8038fa0adbf78d", "type": "tabs", "children": [{"id": "58c6807462a1c941", "type": "leaf", "state": {"type": "backlink", "state": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "反向链接"}}, {"id": "ef0b471e880adcbc", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "添加自己的mcp到Claude code.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "添加自己的mcp到Claude code 的出链列表"}}, {"id": "328c68f566500c9f", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "标签"}}, {"id": "3a6284f7efa802df", "type": "leaf", "state": {"type": "outline", "state": {"file": "添加自己的mcp到Claude code.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "添加自己的mcp到Claude code 的大纲"}}, {"id": "0945ce1183baaa60", "type": "leaf", "state": {"type": "calendar", "state": {}, "icon": "lucide-file", "title": "插件不再活动"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:打开快速切换": false, "graph:查看关系图谱": false, "canvas:新建白板": false, "daily-notes:打开/创建今天的日记": false, "templates:插入模板": false, "command-palette:打开命令面板": false, "markdown-importer:打开 Markdown 格式转换器": false, "periodic-notes:Open today": false, "obsidian-focus-mode:Toggle Focus Mode (Shift + Click to show active pane only)": false}}, "active": "0a5144ca886e1530", "lastOpenFiles": ["未命名.canvas", "1.AAA_开发流程/小程序/2.前端转向开发uniapp.md", "1.AAA_开发流程/小程序/小程序端开发流程.md", "1.AAA_开发流程/小程序/uni-icon 官网以及 使用流程.md", "1.AAA_开发流程/小程序/3.后端开发提示词.md", "1.AAA_开发流程/小程序/1.小程序手机端前端设计.md", "1.AAA_开发流程/小程序/小程序.md", "claude code 配置全局mcp.md", "添加自己的mcp到Claude code.md", "【保-姆-级教程】免费用上Qwen3-Code！将Claude Code接入魔塔社区，从零到一全流程指南.md", "Peakpal.md", "药膳搭配.md", "药膳分类.md", "英语原型图.md", "1.AAA_开发流程/小程序/前端转向开发uniapp.md", "5-Full Notes/药膳推荐系统总设计.md", "taskmaster ai.md", "1-Rough Notes/Daily/代办.md", "5-Full Notes/慢性胃病.md", "5-Full Notes/高血脂.md", "5-Full Notes/投票.md"]}