{"main": {"id": "092692333b1f4ec8", "type": "split", "children": [{"id": "32e5f5692ab63f3e", "type": "tabs", "children": [{"id": "0a5144ca886e1530", "type": "leaf", "state": {"type": "empty", "state": {}, "icon": "lucide-file", "title": "新标签页"}}]}], "direction": "vertical"}, "left": {"id": "dd6b89ccaace2080", "type": "split", "children": [{"id": "2237d4c2e9836508", "type": "tabs", "children": [{"id": "1bbe8733b84546a5", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "文件列表"}}, {"id": "bd8802151fc63dfd", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "搜索"}}, {"id": "4c3d99ef089ed882", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "书签"}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "6fda84fe0e5b91f7", "type": "split", "children": [{"id": "2b8038fa0adbf78d", "type": "tabs", "children": [{"id": "58c6807462a1c941", "type": "leaf", "state": {"type": "backlink", "state": {"collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "反向链接"}}, {"id": "ef0b471e880adcbc", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "添加自己的mcp到Claude code.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "添加自己的mcp到Claude code 的出链列表"}}, {"id": "328c68f566500c9f", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "标签"}}, {"id": "3a6284f7efa802df", "type": "leaf", "state": {"type": "outline", "state": {"file": "添加自己的mcp到Claude code.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "添加自己的mcp到Claude code 的大纲"}}, {"id": "0945ce1183baaa60", "type": "leaf", "state": {"type": "calendar", "state": {}, "icon": "lucide-file", "title": "插件不再活动"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:打开快速切换": false, "graph:查看关系图谱": false, "canvas:新建白板": false, "daily-notes:打开/创建今天的日记": false, "templates:插入模板": false, "command-palette:打开命令面板": false, "markdown-importer:打开 Markdown 格式转换器": false, "periodic-notes:Open today": false, "obsidian-focus-mode:Toggle Focus Mode (Shift + Click to show active pane only)": false}}, "active": "0a5144ca886e1530", "lastOpenFiles": ["📝 Notes/📥 Inbox/迁移进度记录.md", "📝 Notes/📥 Inbox/Ideas/三体协同自动化软件工厂.md", "📝 Notes/📥 Inbox/Ideas/AI刻意练习伙伴App想法.md", "📝 Notes/🎯 Projects/PRD模板项目/PRD模板.md", "📝 Notes/🎯 Projects/PRD模板项目/README.md", "📝 Notes/🎯 Projects/PRD模板项目", "📝 Notes/📖 Knowledge/Testing/Selenium自动化测试.md", "📝 Notes/📖 Knowledge/Development/Python语言基础.md", "🔄 Reviews/📇 Flashcards/金融项目测试实战-Flashcard.md", "🔄 Reviews/📇 Flashcards/软件测试-Flashcard.md", "🔄 Reviews/📇 Flashcards/车辆预订模块测试-Flashcard.md", "🔄 Reviews/📇 Flashcards/车辆管理-Flashcard.md", "🔄 Reviews/📇 Flashcards/购物车测试-Flashcard.md", "🔄 Reviews/📇 Flashcards/接口测试-Flashcard.md", "🔄 Reviews/📇 Flashcards/性能测试基础-Flashcard.md", "🔄 Reviews/📇 Flashcards/微信支付测试-Flashcard.md", "🔄 Reviews/📇 Flashcards/库存管理系统登录功能测试-Flashcard.md", "🔄 Reviews/📇 Flashcards/安全测试基础-Flashcard.md", "🔄 Reviews/📇 Flashcards/selenium复习带答案.md", "🔄 Reviews/📇 Flashcards/Linux项目部署流程与服务检查-Flashcard.md", "🔄 Reviews/📇 Flashcards/Linux系统管理与服务部署-Flashcard.md", "🔄 Reviews/📇 Flashcards/Linux环境下的Java项目部署-Flashcard.md", "🔄 Reviews/📇 Flashcards/Linux基础命令和软件测试入门-Flashcard.md", "🔄 Reviews/📇 Flashcards/Linux中间件服务安装与配置-Flashcard.md", "🔄 Reviews/📇 Flashcards/K8S学习笔记-Flashcard.md", "🔄 Reviews/📇 Flashcards/Docker-Flashcard.md", "🔄 Reviews/📇 Flashcards/APP测试-Flashcard.md", "🔄 Reviews/📇 Flashcards", "📁 Archive", "🔄 Reviews", "📚 Resources/📋 Templates", "📚 Resources", "��️ Topics/Personal", "��️ Topics", "🏷️ Topics/Testing", "🏷️ Topics/Development", "未命名.canvas"]}