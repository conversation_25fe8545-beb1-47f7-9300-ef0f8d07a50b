---
id: 5b91b4f2-5164-4b45-bda2-97cc32435e7f
---

# 用 Cursor 重写了微软开源的32K项目，MarkItDown现在可直接在桌面端使用!
#Omnivore

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sNVp__d4eGWIQTVV-kOPh8yG2oBJBJH9c7OYOhg65NpQ/https://mmbiz.qpic.cn/sz_mmbiz_png/4HWyricuhgQdvnKV7a5KUkPeNUecaWib5cKmpaSnTrial7BVHkw9picYCBeqGOzBlq1VicA8JVuRaKVU6vrriapQxedA/640?wx_fmt=png)

微软开源的MarkItDown库实在是太热了，一个月不到涨到了32k star!究其原因，还是因为 AI应用的快速发展带动了 Markdown 的大火。

但是MarkItDown似乎更适合于系统集成，普通用户使用需要配置本地开发环境和相关处理工具，面对黑漆漆的命令行窗口，使用门槛较高，所以在周末的时候我用 cursor 把它重写成了一个桌面的客户端，方便大家在日常进行文档格式的转化。演示视频如下:

**(Mac/Windows客户端下载链接在本文下方的留言置顶区)**

—

MarkItDown 是一个开源的Python库，由微软团队开发。截止目前已有32k star，足见其炙手可热。

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sxlkRdEhJgnPe4RvBEXZK3BxpSDmeEAP5h9nW1JG3mXE/https://mmbiz.qpic.cn/sz_mmbiz_png/4HWyricuhgQdvnKV7a5KUkPeNUecaWib5clYCj4icIkqHLERsqiayiadWtLlVClqyNjhy3RZg7289qib5kEO7ZgIkZibQ/640?wx_fmt=png)

MarkItDown支持将各种文件格式转换为Markdown格式，包括但不限于PDF、PPTX、DOCX、XLSX、图片、音频、HTML以及各种文本格式。

简单来说，它就像一个全能的文件转换器，让你的文件处理变得更加轻松便捷。

地址: https://github.com/microsoft/markitdown

## 重写

—

在MarkItDown还是8k 左右star的时候,我就根据这个库基于WebAssembly写了一个web应用，地址如下:

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,s1dJX-uJY4hYeShVP5kylWWV9FrGQGELdEUqwc3erFRY/https://mmbiz.qpic.cn/sz_mmbiz_png/4HWyricuhgQdvnKV7a5KUkPeNUecaWib5cDpCz1tfhnXS65UvLcSBq8mPD4vtSM9OOUUcV3kF4Ge6nAIibkvd24Rg/640?wx_fmt=png)

> markdown.songya.top

但是网页用起来还是有很多的问题:

一个是加载速度太慢,初始化环境需要拉很长时间的第三方包,每次刷新都得"准备环境" 老半天;

二是不方便拓展, web应用本身就是在沙盒环境中,功能受限太大;

三我本身是浏览器的多标签党,常年开着100多个标签舍不得关,能少打开一个网页标签,肯定是希望少开一个,不用一个一个翻找;

基于上面的原因,我把markitdown这个项目使用cursor从python整体重写成了node,并使用electron打包成跨平台应用:**MarkDown It**

不需要再等待加载,也不用安装 python 环境和各种包,下载应用安装完后轻松上手用!

整体应用还在开发过程中,目前已完成的功能包含:

* Markdown的直接渲染,(你可以把这个当成 markdown 的编辑器来使用)
* HTML格式转Markdown
* Word格式转Markdown
* Excel格式转Markdown
* PDF格式转Markdown
* PPT格式转Markdown
* URL格式转Markdown

## MarkDown It的应用场景有哪些?

—

* 当灵感来临，打开浏览器经常会有十几个标签页的参考资料。MarkDown It 帮你一键转换网页内容，同时去掉网页上不相关的广告内容和旁枝，再配合 AI 工具，创作过程丝般顺滑。不管是写技术博客还是整理学习笔记，都能保持思路清晰。
* 内容创作者可以将收集的各类素材(网页、文档等)转换为 Markdown，再通过 Claude、ChatGPT 等 AI 工具进行内容重组和优化
* 自媒体作者可以将一份 Markdown 内容轻松发布到不同平台，无需担心格式兼容问题
* 技术博主可以将各种技术文档转换为 Markdown，方便在技术社区分享和传播
* 数据分析师可以将 Excel 表格,商业报告从 PPT、PDF 等格式转换为 Markdown，便于在线协作和版本控制

...

应用的场景完全取决于你的想象力，不管怎么样，会用 Markdown, 用好Markdown真的是AI时代人人都需要掌握的基本功:

## 为什么说 Markdown 是 AI时代的语言?

—

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sPZ77PAK-lRBz_qS_PoZuDUyl2JbylrEK2WUT3l8sFzc/https://mmbiz.qpic.cn/sz_mmbiz_png/4HWyricuhgQdvnKV7a5KUkPeNUecaWib5c8E2AAbRFCRS9SpmUZTb1Au7icpdU0HFqBFibOWGLNejNSLFGs2W0X2tQ/640?wx_fmt=png)

Cursor、Claude、ChatGPT 这些 AI 工具都默认支持 Markdown 格式，这不是巧合。

(如果你还了解他的具体语法可以点击这里:[Markdown 零基础入门指南：10分钟掌握写作利器](https://mp.weixin.qq.com/s?%5F%5Fbiz=MzIzMzQyMzUzNw==&mid=2247491733&idx=2&sn=6f4df7902a29c33ce5b838367a5a79ca&scene=21#wechat%5Fredirect))

就像我们在生活中需要标点符号来表达语气一样，Markdown 用简单的符号（如 # 表示标题，\*\* 表示重点）让 AI 更容易理解我们的意图，就像给 AI 装了一个翻译官。

使用markdown，只需要用三个反引号（\`\`\`）就能标记代码块，AI 立刻就能识别并提供代码建议；在 Claude 里写文档时，用简单的 \* 就能创建清晰的列表，让交流变得井井有条。

这种简单但强大的格式化方式，让人类和 AI 的对话变得更加顺畅，就像我们终于找到了一种双方都能舒服使用的"普通话"。

在这个使用 Cline 生成代码的演示视频中,我使用的和 AI交流的提示词就是全部由Markdown来写成的。[Cline+ Deepseek-V3 实战Twitter(X) 首页的完整案例和演示视频](https://mp.weixin.qq.com/s?%5F%5Fbiz=MzIzMzQyMzUzNw==&mid=2247492647&idx=1&sn=b711b72126335cd83c724dd09c422666&scene=21#wechat%5Fredirect)

Markdown 从一个简单的写作工具，逐渐成长为 AI 时代的通用语言。

不管是写文档、做笔记，还是和 AI 对话，Markdown 都能帮我们把想法清楚地表达出来。

它不仅是一种格式，更是连接人类智慧和人工智能的桥梁。

最后:

MarkDown It未来的路线是可以通过大语言模型实现增强型的 PDF,音频,视频,图片转Markdown,围绕Markdown生态实现更多功能，敬请关注!

## 扩展:Electron 技术栈简介

—

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sidR05lbhAExWFeb5yMwRZyOGRq5tJj6QHkcQDiMZYgs/https://mmbiz.qpic.cn/sz_mmbiz_png/4HWyricuhgQdvnKV7a5KUkPeNUecaWib5cwPr4QLvCmiaDEyw2iaYglSrMsy4cT34fAo1E8fnSXBiaSWNsdxpib1OiaLg/640?wx_fmt=png)

了解这个Electron技术可以为你以后的应用技术选型提供参考:

VSCode、Cursor 都是基于这个Electron来开发的，Electron 就像一个神奇的容器，它能让网页技术（HTML、CSS、JavaScript）变成功能强大的桌面应用程序。

Electron 让开发者只需要会写网页就能制作出跨平台的桌面应用，这也是为什么 Discord、Slack、WhatsApp 这些现代桌面应用能快速开发并保持高频率更新，因为它们本质上是运行在一个特殊浏览器中的网页应用，但又能像原生应用一样调用系统功能。

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sOZORXXl6oIth8Iokv94qJb9Ij8khsBGmPzHdHFIRZDs/https://mmbiz.qpic.cn/sz_mmbiz_jpg/4HWyricuhgQdvnKV7a5KUkPeNUecaWib5c8bX0wlIJvGL6BibaCU43PX0zy0GsWNKUjHurkLyPGSyYzAGu1q7l5Hw/640?wx_fmt=jpeg)

[Read on Omnivore](https://omnivore.app/me/https-mp-weixin-qq-com-s-biz-mz-iz-mz-qy-mz-uz-nw-3-d-3-d-ascene-1943cb30340)
[Read Original](https://mp.weixin.qq.com/s?__biz=MzIzMzQyMzUzNw%3D%3D&ascene=56&chksm=e8877343dff0fa5578a6bc652f8f65dbd2c98a65eaa3c555d2236e4513bb95f5f4fa21e94ca4&clicktime=1736185116&countrycode=HR&devicetype=android-35&enterid=1736185116&exportkey=n_ChQIAhIQWANt0Sf%2BxJqwsSbg%2FsqKgRLxAQIE97dBBAEAAAAAAKdCOMFMT8IAAAAOpnltbLcz9gKNyK89dVj0eRPIPnjOaq9C%2FZ3ifZhRclKGCHUJKa9EsBK850f3rzVuZ7gBMYhU8%2FiO9T32JETNRYHu%2BttL%2BRvNq1q5IagzgSH4zV6ZiqAGbi6zH840RFfd1hdqnrd7bqJUAx4km5JVTUzQyY1RjwMCiVehqv%2BMLYDu8RDbGTzHbdDFFfDzaWYP5qAFhafwb88mXbOnd4CYVZawN8Y8qTfFjCMZI5KRsSXUxzK7BhSJZYIJvBwzv35OL02Ea9c5FME8yFshNUGHufITdRfpDDzlEYU%3D&exptype=unsubscribed_card_recommend_article_u2i_mainprocess_coarse_sort_tlfeeds&fasttmpl_flag=0&fasttmpl_fullversion=7547503-zh_CN-zip&fasttmpl_type=0&finder_biz_enter_id=5&flutter_pos=18&idx=1&lang=zh_CN&mid=2247492765&nettype=WIFI&pass_ticket=%2F310i3gNiJM7dhnjjsj53o2z4N6JS%2FhFBPQ9P6WrS3SeA%2FYXRH215StWC7wl1tkw&ranksessionid=1736185033_2&realreporttime=1736185116858&scene=169&session_us=gh_d39605d76a1e&sessionid=1736185032&sn=0deaf25e9f3f931dd17e45a5db913ad0&subscene=200&version=2800373b&wx_header=3)

