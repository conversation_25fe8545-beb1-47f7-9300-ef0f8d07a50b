---
id: e2421936-4d58-47fb-af95-e9a2552a7f5c
---

# 4K Star！高颜值远程管理工具，在本地管理你所有连接！
#Omnivore

> 大家好，我是Jack Bytes，一个专注于将人工智能应用于日常生活的半吊子程序猿，平时主要分享AI、NAS、Docker、搞机技巧、开源项目等。

相信各位**搞机爱好者**、**程序猿**、**运维工程师**每天都在和终端打交道，一款高颜值、实用的远程连接管理工具能够提升你的效率。

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,s0YFrStpnP7ziiEHwbTNLmFc7d8hBjzq68TcSwM5NzbY/https://mmbiz.qpic.cn/mmbiz_gif/Z5oWxjb7VYSS6Eft82jg3jcjpoIcVLWJjAJNibIZcmSHgVo3RpXtCz9mz7libib1c7JUjHmNwNCd6FlWDKmGO4qcA/640?wx_fmt=gif&from=appmsg)

今天，给大家介绍一款高颜值的远程管理工具**XPipe**，项目地址在文末可以找到

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,scSU_qdiIq4XxO3XsZbfWuIgO0HYnUQfjXH2qRnbFOKU/https://mmbiz.qpic.cn/mmbiz_png/Z5oWxjb7VYSS6Eft82jg3jcjpoIcVLWJcffsdQYxKk8yIib4aWvRAbGSoOGQQTIUg4ZHAWoBOXiaSnYYYxNZ2UNw/640?wx_fmt=png&from=appmsg)

## 一、项目介绍

**XPipe**是一种新型的 shell 连接集线器和远程文件管理器，它允许您从本地计算机访问整个服务器基础架构。

它在您安装的命令行程序之上工作，不需要在远程系统上进行任何设置。

因此，如果您通常使用 **ssh**、**docker**、**kubectl** 等 CLI 工具连接到您的服务器，您可以在此基础上使用 **XPipe**。

XPipe 完全与你的工具集成，例如你最喜欢的文本 / 代码编辑器、终端、外壳程序、命令行工具等等。

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sQx9vzPEPK5DebLmzPhyfFMVgHhY4J2mxBAfTBH1xow0/https://mmbiz.qpic.cn/mmbiz_gif/Z5oWxjb7VYSS6Eft82jg3jcjpoIcVLWJKzdAEHZiawgSHdicPvQeHhd7kKh76LH7OMZLqyicDKhNDgn3I13f90prw/640?wx_fmt=gif&from=appmsg)

这个平台被设计为可扩展的，允许任何人通过模块化的扩展系统轻松添加对更多工具的支持或者实现自定义功能。

## 二、功能特性

* SSH 连接、配置文件以及隧道
* 位于任何主机上的 Docker、Podman 和 LXD 容器实例
* Proxmox PVE 虚拟机及容器
* Hyper-V、KVM/QEMU、VMware Player/Workstation/Fusion 虚拟机
* Kubernetes 集群、Pod 以及容器
* 适用于 Linux 的 Windows 子系统、Cygwin 以及 MSYS2 实例
* PowerShell 远程会话
* Teleport tsh 连接
* VNC 连接
* 任何其他可通过命令行运行的自定义远程连接方法

## 三、连接集成

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,snCGk474ExjZlxZPoUXEFM1OAOCiI405FRQHVQX1kbGg/https://mmbiz.qpic.cn/mmbiz_png/Z5oWxjb7VYSS6Eft82jg3jcjpoIcVLWJ9brAEQt2k5x1PEf4UQlzDuOz5xJ1RmA8CJsgL80V1OiavMtQ8NUMEeA/640?wx_fmt=png&from=appmsg)

* 轻松在一个地方连接并访问各类远程连接。
* 将你所有的连接按层级分类进行整理，这样即便有数百个连接，你也能对其一目了然。
* 在任何系统上创建特定的登录环境，以便针对每个使用场景能立即进入一个已妥善设置好的环境。
* 快速执行各种常用操作，如启动 / 停止容器、建立隧道等等。
* 创建桌面快捷方式，使其能在终端自动打开远程连接，而无需打开任何图形用户界面。

## 四、强大的文件管理

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,slgRiK-DfEBAl1Fxcb0PGpQbjLUqik98rFa7zjNWamJ4/https://mmbiz.qpic.cn/mmbiz_png/Z5oWxjb7VYSS6Eft82jg3jcjpoIcVLWJLJibuicOgrh6vicKlTib71a76Th7D50FMGjI0DEuYPdA4wON1ZLicOsmMDw/640?wx_fmt=png&from=appmsg)

* 系统交互：使用专为专业人士优化的工作流程与任何远程系统的文件系统进行交互。
* 任意目录：在你心仪的终端模拟器中，快速在任意目录下打开终端会话。
* 远程文件：利用本地安装的所有程序工具来打开和编辑远程文件。
* 动态sudo：必要时可动态使用 “sudo” 提升会话权限，而无需重启会话。
* 无缝传输文件：在系统桌面环境与远程系统之间无缝传输文件。
* 多系统文件传输：借助内置的多标签式多任务功能，同时在多个系统上开展工作并进行文件传输。

## 五、终端启动

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sn2Wtu-4YYPw6JXhtpcePBakZrTPECfQ-mPx1g_9SpFI/https://mmbiz.qpic.cn/mmbiz_gif/Z5oWxjb7VYSS6Eft82jg3jcjpoIcVLWJnxUXOv0lICHEsK37xp1mTNngOHGjVy0qk6WNr3x8fqiaw3JIqH6UVtQ/640?wx_fmt=gif&from=appmsg)

* 只需点击一下，就能让你在心仪的终端中进入一个命令行 shell 会话。可自动填写密码提示等内容。
* 支持所有操作系统中常用的各类终端模拟器。
* 也支持通过自定义命令行规范来打开自定义的终端模拟器。
* 适用于所有命令行 shell，如 bash、zsh、cmd、PowerShell 等等，无论是本地还是远程环境皆可使用。
* 在终端仍处于启动阶段时就能连接到系统，从而实现比常规情况更快的连接速度。

## 六、多功能脚本系统

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sDkskXLseO2_YG3CJMR-HKm-gckDpirgvLrMzbiz5JXg/https://mmbiz.qpic.cn/mmbiz_png/Z5oWxjb7VYSS6Eft82jg3jcjpoIcVLWJvBAEacdqhyVCdgRiak86gBVssjyibaVS2gMxNPqicCf8zxYnAhcCUXY3w/640?wx_fmt=png&from=appmsg)

* 创建可重复使用的简单 shell 脚本、模板以及分组，以便在已连接的远程系统上运行。
* 无需进行任何设置，就能自动让你的脚本在任何远程系统的环境变量 “PATH” 路径下可用。
* 为连接设置 shell 初始化环境，以便针对各种用途全面自定义你的工作环境。
* 通过提供你自己的命令来打开自定义的 shell 以及自定义远程连接。

## 七、安全库

* 所有数据都仅存储在你本地系统中一个具备加密安全防护的保管库内。你还可以选择使用自定义的主密码短语来进一步加密，以此提高安全性。
* XPipe 能够通过其命令行接口从你的密码管理器中自动获取机密信息。
* 不涉及任何服务器，你所有的信息都保留在你自己的系统上。XPipe 应用程序不会向外部服务发送任何个人或敏感信息。
* 保管库的变更可以由多个团队成员通过多个系统从你自己的远程 Git 仓库中进行推送和拉取操作。

## 八、通过 API 进行编程连接控制

* XPipe API 通过一个 HTTP 接口提供对 XPipe 各项功能的编程访问途径。
* 使用你最喜欢的编程语言来管理你所有的远程系统并访问它们的文件系统。
* 既可以直接调用该 API，也可以借助 Python 库来进行调用。
* 完整的文档既可以在应用程序自身的 “API” 选项卡下找到，也可以在 OpenAPI 规范文件中获取。

## 九、项目地址

xpipe-io/xpipe

## 我是Jack Bytes

一个专注于将人工智能应用于日常生活的半吊子程序猿！

**平时主要分享AI、NAS、Docker、搞机技巧、开源项目等技术，喜欢的话请关注吧！**

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,s9rJXla-mWbeX1E7fAXdABT7lHjSme7YbLep9ZmgWEiY/https://mmbiz.qpic.cn/mmbiz_gif/Z5oWxjb7VYSS6Eft82jg3jcjpoIcVLWJFSCsgJoic1wyYx3We5mV78z1EibMUYCV2wwteohmYpdF1g3dEEQYQdTw/640?wx_fmt=gif&from=appmsg)

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,saBP5lCdvVkup51G6cDZDwEiMm1CSGHH6QYDTqfh-zYI/https://mmbiz.qpic.cn/mmbiz_gif/dIu5KyYgR8VkicVE2HPlhiaLACsk5PPGgHmUoz1cuBrUCJ1bdBgOIQCNkoMuXX6v63Zod0I1orJxW9W8myzZPP0A/640?&wx_fmt=gif)

![](https://proxy-prod.omnivore-image-cache.app/0x0,sYEVhVX04Hr0d6IIg_TWKkxCQXorhQBWq60HUY8-WL1k/https://mmbiz.qpic.cn/mmbiz_png/bqaIib0l75Sia3gK9KRhyJw3ZjV72icgx0GzOU8iaPjIX3V5zbn4ibuu0gMWzW7IblehpsfexTic46PTXlc7iauE5ArYg/640?&wx_fmt=png)

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sy4LUrkUofPGpKAPWX0-Yv2Pw5gc0WxcvBmvWkgGLlFU/https://mmbiz.qpic.cn/sz_mmbiz_png/Sj0lBwsFxxrwAPHFMJnmxQDvric6v26E7YJqUVSHJhZFS8IUxtibSgjgSEfwgJnupX94Oj7KMdf7JicerygNmibm4A/640?&wx_fmt=png)

往期内容

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,s3YgNi0HHvmoDxmvnDDPikgNEY2v6HbIS045-3oajAXU/https://mmbiz.qpic.cn/mmbiz_png/v6tqVoaUYOyQhxzicIQJaf2E1dpQBibEyOSKWzqlfInHnh5pKSTwmgdJBkAkichR6a9Msia02jOXicSjTaGThP5L4Og/640?&wx_fmt=png)

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sCLAqXTlvHb_d16c_N2rD85QXN3kUhY8H5hAX4V0nreI/https://mmbiz.qpic.cn/mmbiz_png/oppQx1QsibD5wZmDCwYglsL1elX3zmeYzaniacvEG0qmytxoUg7d6NqiaWjkXkDGjc5ogw38EIHlS6Q76C0zsDI1Q/640?&wx_fmt=png)

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,syZ_iKSEczZgXB3LmZ6YOJ_dx4N0-RK83UEMv0ZJ8RNo/https://mmbiz.qpic.cn/sz_mmbiz_png/CGNO2W0DUtV6ofIE0Nw5LIVd13JMicBibY4E5qlYL6sJUiaXZhCZtiaVC79Y4hxQCZQcmicfUYleRRlCX9npDPIMK2A/640?&wx_fmt=png)

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sqwrYOE7r3Avt2yZXB7k3YC7XoSj4rAGNcOdMuRsbNgQ/https://mmbiz.qpic.cn/sz_mmbiz_png/OPS9dkG35vlQyD7XOYicyYhYxztqbdsjVlvrPom1SA7bia6Mdan41CjIR5Hp7V0qymjme25lLickRnCicWLSh5YPDQ/640?&wx_fmt=png)

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sxossCdGLg3Io6g_M5hs6zigv7RgLFKj1dRoO_qmE5ng/https://mmbiz.qpic.cn/mmbiz_png/R10pxSTnClIB5PicpoOVnLmiawwjPuZXBN2M9cicwq5KyqWH2DcUvWUc5HXkH1zSia27Blxu5SwV7ZjOzesubwXXBA/640?&wx_fmt=png)

[61.9K Star，超高颜值！监控你的所有服务，支持在NAS、服务器上通过Docker部署！![图片](https://proxy-prod.omnivore-image-cache.app/0x0,s2n1OyfALN4v1SYrU3ABHaM0gJOsoF-c7AXpV5exOx7U/https://mmbiz.qpic.cn/mmbiz_jpg/Z5oWxjb7VYQEfxljsGO31P7RHAPhTg6RQalic23Ymlb2T8wfL6uomUOEc0Z4CYM8c1Aple115S0hzeGq7GPdicjQ/640?wx_fmt=jpeg) ](https://mp.weixin.qq.com/s?%5F%5Fbiz=Mzg4NTIyNTU0NQ==&mid=2247487993&idx=1&sn=6bb7007491d1c94174ee715dc0c8989e&scene=21#wechat%5Fredirect)

[通过Docker一键部署群晖NAS！支持自定义内存和CPU、多磁盘挂载、KVM加速！![图片](https://proxy-prod.omnivore-image-cache.app/0x0,s0KNtbwHCImNSh8t3kdQ_Lod5HeLmJB97WjwDbBJ0YL0/https://mmbiz.qpic.cn/mmbiz_jpg/Z5oWxjb7VYS6o9WibrwE3eClZ46vZvlpicBJibOqc8Tml6yrzDoK4pFvAwHKCLpLkTVrKlPJd80TyNict2LOyLfQkA/640?wx_fmt=jpeg) ](https://mp.weixin.qq.com/s?%5F%5Fbiz=Mzg4NTIyNTU0NQ==&mid=2247487902&idx=1&sn=df632d509aa4801bf75eb19b58576668&scene=21#wechat%5Fredirect)

[手把手教你在NAS上搭建KMS服务器，免费永久激活Windows及Office！建议收藏![图片](https://proxy-prod.omnivore-image-cache.app/0x0,s4Bp-HOkmfSmxqS0dcpnigX7H1rAZmJmR8L69-niwVYg/https://mmbiz.qpic.cn/mmbiz_jpg/Z5oWxjb7VYQWSNdJ26usAYtzbQ2tKmP7xib15Y1444ol0ibiaqgpMialricne08VMnYaicdtQHATvWCmdD1FD3a7K3UQ/640?wx_fmt=jpeg) ](https://mp.weixin.qq.com/s?%5F%5Fbiz=Mzg4NTIyNTU0NQ==&mid=2247487853&idx=1&sn=a453b3c6106ccd08f530f14713240dfd&scene=21#wechat%5Fredirect)

[NAS和服务器用户必看，海量开源服务器应用大全，208K Star，不间断能玩好几年！建议收藏![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sbk7GKNr5EFCBOagkyCNlcsTdEwiXwDO9pwbC8ppNVBk/https://mmbiz.qpic.cn/mmbiz_jpg/Z5oWxjb7VYSibxaBaMwzhXr6icTJB9TsiacEqYibsbqbD7UH6cL2sDQSCS0qnj78FiczOCbqceOgKC1A7ZIqBFQFsCg/640?wx_fmt=jpeg) ](https://mp.weixin.qq.com/s?%5F%5Fbiz=Mzg4NTIyNTU0NQ==&mid=2247487774&idx=1&sn=2eb0430b1aa87938891e6d389d2c11b4&scene=21#wechat%5Fredirect)

[35.5k Star！AI声音克隆，支持本地部署！5秒内克隆您的声音并生成任意语音内容！![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sCUPpiEIRf4WtUUkd6380uOhYFHjm1gI4PLtq_A1jhas/https://mmbiz.qpic.cn/mmbiz_jpg/Z5oWxjb7VYRN6VzW5iadEToOPa9wN9FwBU1ohzxYYXLCuiaChRgliah1GdqNMJafmVQ7NulJO2ic8K8h7Ziaola7IUA/640?wx_fmt=jpeg) ](https://mp.weixin.qq.com/s?%5F%5Fbiz=Mzg4NTIyNTU0NQ==&mid=2247487666&idx=1&sn=3c0f026a73b443cb621b67cf56dbffc0&scene=21#wechat%5Fredirect)

[Read on Omnivore](https://omnivore.app/me/https-mp-weixin-qq-com-s-biz-mzg-4-nt-iy-ntu-0-nq-3-d-3-d-ascene-194416f3ed6)
[Read Original](https://mp.weixin.qq.com/s?__biz=Mzg4NTIyNTU0NQ%3D%3D&ascene=56&chksm=cfad7385f8dafa93de3a9d638715493d32f33f2734160fd5ac5605aad28be7fec3b466665b32&clicktime=1736264527&countrycode=HR&devicetype=android-35&enterid=1736264527&exportkey=n_ChQIAhIQJY561nBaCdEMBRPqnl9ITxLxAQIE97dBBAEAAAAAAAxzLtfB6fEAAAAOpnltbLcz9gKNyK89dVj0xPD1cd8505OugKGMvA8g1tFrK9HUc0wsZH6hY9PxOEPfyA%2FKg9QE93F6Inep9sQo5NMJ08nwd5vw3o8qvpIrvcflKME1o6jM%2FtpweyT9SAQ%2BHq0TFtxtQ3tfF%2B80MmB9ZiI4P81bIRAqABYJYS6Cw1JIcT0ki9hl3rQWmgaCtfrM7EWwiRldYtqYpHqwEVVCu6FDG97PDGWJvisdmqYfY4IvhrveJE19kuli9E1mTztF5ZzTw2Af23XUMzZD9UhMqsbd3ATSQhHrSSM%3D&exptype=unsubscribed_card_recommend_article_u2i_mainprocess_coarse_sort_tlfeeds&fasttmpl_flag=0&fasttmpl_fullversion=7548708-zh_CN-zip&fasttmpl_type=0&finder_biz_enter_id=5&flutter_pos=124&idx=1&lang=zh_CN&mid=2247488163&nettype=WIFI&pass_ticket=YHPmUDYqhDQX7vZzlat%2BDbOOCV22jha6%2F0EpstAyEz%2B6K9Ckjg0tRO0MS%2B4PiDJp&ranksessionid=1736262197_14&realreporttime=1736264527173&scene=169&session_us=gh_17ed84ff2394&sessionid=1736263787&sn=7f6b9cfa1e9742a4a89213e89f11cf1e&subscene=200&version=2800373d&wx_header=3)

