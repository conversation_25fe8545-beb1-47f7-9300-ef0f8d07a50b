---
id: 0ae0ccc7-a521-4dcc-b424-be39bf08474b
---

# Postman 汉化教程（小白）配置的具体操作_postman怎么设置中文-CSDN博客
#Omnivore

## Postman 汉化教程（小白）配置的具体操作

![](https://proxy-prod.omnivore-image-cache.app/0x0,sb9FtdCb_T4b1O697eqoXY9k3NVVgRsw8SlTyVuCA3pc/https://csdnimg.cn/release/blogv2/dist/pc/img/original.png)

 版权声明：本文为博主原创文章，遵循[ CC 4.0 BY-SA ](http://creativecommons.org/licenses/by-sa/4.0/)版权协议，转载请附上原文出处链接和本声明。

 前天有个小伙伴问我 Postman 如何进行中文汉化，说英文看着着实不习惯。其实，Postman 本身并不支持中文，那要怎么汉化呢？   
 这里分享一个 GitHub 上大佬分享的 Postman 汉化补丁包，亲测还是挺好用的，只需要复制到指定目录下进行解压，即可将 Postman 设置成中文，下面是我汉化成功的截图： 

![请添加图片描述](https://proxy-prod.omnivore-image-cache.app/0x0,s9dxt-bVy35vo_3dr4m4nsazDtCvXeL0LjC_qlMuA4qE/https://i-blog.csdnimg.cn/blog_migrate/4a5018cb89ca175152b1fbfeb7ff9e83.png)

#### 目录

* [Postman 简介](#Postman%5F%5F17)
* [下载&安装 Postman](#%5FPostman%5F23)
* [开始汉化 Postman](#%5FPostman%5F50)
* [Windows 系统](#Windows%5F%5F56)
* [Mac 系统](#Mac%5F%5F72)

  
## Postman 简介

---

Postman 是一款非常流行的 API 调试工具，可以说是[测试工程师](https://so.csdn.net/so/search?q=%E6%B5%8B%E8%AF%95%E5%B7%A5%E7%A8%8B%E5%B8%88&spm=1001.2101.3001.7020)、后端开发人员，基本上是人手必备。

## 下载&安装 Postman

---

> 注意：想要汉化 Postman， 就**必须导入汉化补丁包，且补丁包的版本号需与 Postman** 
> **版本号一致才行**，否则大概率无法汉化。所以，**需先确认汉化补丁包的版本号，再下载对应版本的 Postman 使用。**

我写这篇文章的时候，补丁包的最新版本号为 **9.12.2**

汉化补丁下载链接：<https://github.com/hlmd/Postman-cn/releases>

确认了补丁包版本号后，再下载对应版本的 Postman :

| Postman 历史版本下载 | 请把下面链接的"版本号"替换为指定的版本号, 然后浏览器中访问即可直接下载               |
| -------------- | --------------------------------------------------- |
| Windows64位     | https://dl.pstmn.io/download/version/版本号/win64      |
| Windows32位     | https://dl.pstmn.io/download/version/版本号/win32      |
| Mac Intel Chip | https://dl.pstmn.io/download/version/版本号/osx\_64    |
| Mac Apple Chip | https://dl.pstmn.io/download/version/版本号/osx\_arm64 |
| Linux          | https://dl.pstmn.io/download/version/版本号/linux      |

比如补丁版本号为 9.12.2, 如果想要下载 Windows 64 位的 Postman，则下载链接为 <https://dl.pstmn.io/download/version/9.12.2/win64>，浏览器访问该地址，则可直接下载：

![在这里插入图片描述](https://proxy-prod.omnivore-image-cache.app/0x0,sQfSyshq_js_-yb2Ad9rnovbtULefxez998JwklTlU5U/https://i-blog.csdnimg.cn/blog_migrate/92f58743550e05ce9137d7abe9b4ef4f.png)

下载成功后，双击安装即可。

## 开始汉化 Postman

---

安装成功后，我们开始汉化 Postman:

## Windows 系统

1、下载好对应版本的汉化补丁包 **app.zip;**

2、进入到 Postman 安装目录下的 **/resources** 文件夹中：

> 桌面找到 Postman 应用程序右键 -> 打开文件所在位置，再进入 **app-_._.\*/resources** 目录下，  
> 默认安装地址：**C:/Users/<USER>/AppData/Local/Postman，**  
> 示例：**C:/Users/<USER>/AppData/Local/Postman/app-8.8.0/resources**

3、复制 **app.zip 到 resources 目录**，将**app.zip解压到当前文件夹**会生成一个app目录，如上图所示；

4、**重启** **Postman** 即可看到已经汉化成功\~

## Mac 系统

1、下载对应版本的 **app.zip;**

2、**解压 app.zip；**

3、进入 访达/应用程序 **/Postman.app/Contents/Resources/:**

> 进入 **访达/应用程序** 找到 **Postman.app** 右键查看包内容，再进入 **Contents/Resources**

4、替换 **app** 文件夹

> 如果目录下没有 **app** 文件夹，那么直接解压 **app.zip** 得到 **app** 文件夹即可  
> 将**app.zip**解压出来的 **app** 文件夹复制到 **Resources** 目录，替换原本的**app**文件夹  
> 可以先删除或重命名原本的**app**文件夹

5、重启 Postman 就可以了\~

[Read on Omnivore](https://omnivore.app/me/postman-postman-csdn-1944b45946e)
[Read Original](https://blog.csdn.net/zxz_zxz_zxz/article/details/130867975)

