---
id: 63b8367d-f9e6-4a02-a9d0-d7346d6ffcac
---

# 20+小时后，我沉淀出Cursor开发微信小程序最佳桌面布局
#Omnivore

## 前言

最近一周，我一直在开发一个特别有意义的小程序 —— 一个通过拍照识别食物升糖指数的应用。这个小程序是为了帮助需要控制血糖的舅舅更好地管理饮食。

当看到舅舅能够在菜市场买菜时，只需简单拍照就能立即知道食物是否适合食用时，说实话，我第一次感受到了 AI 为生活带来的实际价值。

当然了，这次开发过程中我也沉淀出来了一个Cursor开发微信小程序的最佳桌面布局。

## 开发环境概述

在开发过程中，合理的工作区布局对提升开发效率至关重要。一个好的布局不仅能减少在不同窗口间的频繁切换，还能让我们更专注于代码本身。经过至少20+小时的Cursor开发微信小程序后，以下是我在实践中总结出的最佳布局方案。

如图：

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,s1sEg2sxeIbzQ51KWd4k6HPZMDaV34CktfQXOMRH0R0A/https://mmbiz.qpic.cn/mmbiz_jpg/RBgzkvDqanKRHXXMCv0Rqic4dBcIgfQicbft8fMlGibx6rdO1hbdfniaOicDO4reNnVXUKR4oxEe7twRoicIRxrhBFng/640?wx_fmt=jpeg&from=appmsg "null")

### 前期准备

在开始配置之前，请确保你已经：

* • 安装了最新版本的 Cursor
* • 安装了微信开发者工具
* • 熟悉基本的窗口管理操作

## 详细布局步骤

### 分屏

* • 将 Cursor 分屏到左侧（建议占据屏幕 70% 的宽度）
* • 将微信开发工具分屏到右侧

### 2\. 微信开发工具配置

* • 点击模拟器右上角的分离按钮

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sTQcEeI-JDWWeQRR35uualf9PZstzW4kCPKCTcfM3LtE/https://mmbiz.qpic.cn/mmbiz_png/RBgzkvDqanKRHXXMCv0Rqic4dBcIgfQicbz6b1tIuS9iad7bGMOicZiaYCRCSap2wI2ib7XIia8XQHr2S2xM52mO5VXicQ/640?wx_fmt=png&from=appmsg "null")

* • 将分离出的模拟器窗口拖动到cursor分屏处
* • 调整模拟器窗口大小，确保界面内容清晰可见

### 3\. Composer 设置优化

* • 启动 Composer（快捷键或菜单栏）
* • 找到右上角的 "Open Composer as Bar" 选项 （需要最新版本的cursor才支持）

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,sbtRoNshcTDUxXuM9Bi-55Ci0XFGGi1fWsrhErXChb8U/https://mmbiz.qpic.cn/mmbiz_png/RBgzkvDqanKRHXXMCv0Rqic4dBcIgfQicbLbS4GUzZxGWBmaBWC9vr5B3Pa9VQmZmVekj9NicItGWbXVRibKibibBv8w/640?wx_fmt=png&from=appmsg "null")

* • 将独立的 Composer 窗口移动到 Cursor 分屏的右上角
* • 调整 Composer 窗口大小，建议保持较小以不影响其他内容的显示

### 4\. 终端的合理布局

* • 打开 Cursor 的内置终端

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,s3no3KXVLivqHwsPHdqrw8wSbjJFwhyE-J-uEDX7nH3M/https://mmbiz.qpic.cn/mmbiz_png/RBgzkvDqanKRHXXMCv0Rqic4dBcIgfQicbulpibuoib5icUOZlwiaFibQ2vwRpy2udGc0OPzB4y2QDxahf9icuUIQiblomQ/640?wx_fmt=png&from=appmsg "null")

* • 找到右上角的终端拆分图标（形似书本）

![图片](https://proxy-prod.omnivore-image-cache.app/0x0,s3ky8ElYQ_mst4ePDVb4yFM8zckReGC-vVl9zW3iXDYk/https://mmbiz.qpic.cn/mmbiz_png/RBgzkvDqanKRHXXMCv0Rqic4dBcIgfQicbNKEJkkTzyOc5ygTRXtqzia8n1qcuyGqpgZ4icNzs4C5BrF8MB6KwPibrw/640?wx_fmt=png&from=appmsg "null")

* • 点击拆分，使终端显示在右侧下方
* • 建议将终端高度设置为屏幕高度的 30%

### 5\. 安装auto save插件

Cursor在最近的版本中虽然更新了自动保存能力，但是有时候还是没有做到自动保存，我们借助cursor插件来保证万无一失。

具体你可以看我这篇文章：[插件推荐](https://mp.weixin.qq.com/s?%5F%5Fbiz=MzU5NjMxOTE5OQ==&mid=2247487070&idx=1&sn=e37d5429ede2a69ddcc663783c26f90e&scene=21#wechat%5Fredirect) 中的auto save片段

## 布局优势详解

这种布局配置带来的优势是多方面的：

1. 1\. **即时反馈**
   * • 代码修改后无需切换窗口即可查看效果
   * • 编译错误能够立即在终端中显示
   * • AI 提示和代码建议随手可得
2. 2\. **效率提升**
   * • 减少窗口切换频率，提高开发专注度
   * • 所有工具一目了然，操作更加流畅
   * • 多任务协同变得更加简单
3. 3\. **调试便利**
   * • 错误信息实时可见
   * • 控制台输出直观明了
   * • 快速定位和解决问题

## 常见问题与解决方案

### 性能问题

* • **问题**：同时开启多个工具可能导致电脑卡顿
* • **解决**：适当调整各个工具的窗口大小，必要时关闭不常用功能

### 显示问题

* • **问题**：高分辨率屏幕可能出现显示比例不协调
* • **解决**：调整系统显示缩放，或使用工具的缩放功能

## 注意事项

* • 本教程基于 Mac 系统进行说明，Windows 用户可能需要使用其他窗口管理工具
* • 建议使用外接显示器获得更好的开发体验
* • 根据个人习惯适当调整各个窗口的大小和位置

## 结语

合理的工作区布局能够显著提升开发效率，节省大量在窗口切换上浪费的时间。

在实际开发智能食物识别小程序的过程中，这套布局帮助我更专注于业务逻辑的实现，而不是在工具操作上分心。

如果你也在使用 Cursor 开发微信小程序，不妨试试这种布局方式。同时，我也期待听到你的使用反馈和改进建议！

## 交流与反馈

* • 如果你需要更详细的视频教程，欢迎在评论区留言，我创建了一个免费社群，公众号后台留言「交流」扫码入群
* • 有任何布局优化的建议，也请随时分享
* • 如果你对食物升糖指数小程序感兴趣，也可以交流探讨这是我的小程序入口：
* ![图片](https://proxy-prod.omnivore-image-cache.app/0x0,s8PVYg0Fh8FvYN-QNQuuDYYQ_cVStqNWiao-0rxB_npE/https://mmbiz.qpic.cn/mmbiz_png/RBgzkvDqanKRHXXMCv0Rqic4dBcIgfQicbF9mW6a2bUxuD4a1tib1FbYr7TQict0uutdFp5zToj8dpWXlvibdYmO7SQ/640?wx_fmt=png)

#### 引用链接

`[1]` 插件推荐: _https://www.lookai.top/cn/cursor/tips/cursor\_plugins_  

[Read on Omnivore](https://omnivore.app/me/https-mp-weixin-qq-com-s-biz-mz-u-5-nj-mx-ote-5-oq-3-d-3-d-ascen-194460e634c)
[Read Original](https://mp.weixin.qq.com/s?__biz=MzU5NjMxOTE5OQ%3D%3D&ascene=3&chksm=ffa4d1e8df31feaa0cb2a7cfe8799234365c2e3895343dd4183ec274ff891cb768efb26e625d&clicktime=1736342114&countrycode=HR&devicetype=android-35&enterid=1736342114&exportkey=n_ChQIAhIQNbf530ZRkyclMKXb%2BULD0xLxAQIE97dBBAEAAAAAAF6HOAOzcV8AAAAOpnltbLcz9gKNyK89dVj0JYCp7Z7b3KFaraekPdDfFvz1U%2BFhQzq2so%2BO9Igh7WVHh59vfTomFInLveIpxIWGbqhBgonq59LG1sivqvhC%2B5eWpgW0eay8Uil3ZDRDpfJ0weQIDssoPSW3c2m9tH0qwDen0tlSdt61Z2w2dffUmOeIVwj7ws1y4fvP7L1DmYhEwp6YydNsS9Gt0neInFQ4GYeIy9Yr42FNHAvdjk3rIGOsfRiqtp5%2FC08hyCYQSY2D0pgX0VyLRfKn7YSbD2HNrAE69JjtWZBEQPk%3D&fasttmpl_flag=0&fasttmpl_fullversion=7550425-zh_CN-zip&fasttmpl_type=0&idx=1&lang=zh_CN&mid=2247487197&nettype=WIFI&pass_ticket=WSJvxo0h6LpwFAcesxmm8g%2BIv7unmkv2YfpdzCY4mHoqvh8EmdXs1AojsDHTej36&realreporttime=1736342114250&scene=126&session_us=gh_3da3a15b1833&sessionid=1736342055&sn=1ad129ce809818e422fdd39aeac4d892&subscene=90&version=2800373d&wx_header=3)

