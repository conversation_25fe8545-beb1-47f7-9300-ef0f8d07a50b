---
id: f75c8bc9-e698-47c7-b2c5-5a13d9fbac3d
---

# #Obsdian 当树形目录遇上层级缩进，它两一见钟情！美滋滋，真是美滋滋！
#Omnivore

🐤大家好，我是瓴上知更。知止而后定，遂有知更鸟在屋顶。🐤

🏡相逢是缘，点击上方蓝字关注公众号，或订阅合集：🏡

[📖笔记森林：为常青而作](https://mp.weixin.qq.com/mp/appmsgalbum?%5F%5Fbiz=MzI5MzMxMTU1OQ==&action=getalbum&album%5Fid=3215776886136635396#wechat%5Fredirect) | [生活写给我的热爱](https://mp.weixin.qq.com/mp/appmsgalbum?%5F%5Fbiz=MzI5MzMxMTU1OQ==&action=getalbum&album%5Fid=3479690560200245249#wechat%5Fredirect) | [油锅不饿📖](https://mp.weixin.qq.com/mp/appmsgalbum?%5F%5Fbiz=MzI5MzMxMTU1OQ==&action=getalbum&album%5Fid=3676739606096773127#wechat%5Fredirect)

---

![Image](https://proxy-prod.omnivore-image-cache.app/0x0,spj23ugM63zD7f-dFRmavrRdRtVLaQ5AsD-CjosIpGuM/https://mmbiz.qpic.cn/sz_mmbiz_png/hp9XO4U4GjibYAB9ga8ibqBkdvf4VejicAKQLa0YF15JvckXQVHhH3JVQu73Zp1AKicSOpJgiazKcOP8ibMCN9IrBmgg/640?wx_fmt=png&from=appmsg)

本轮 NoteChian 更新，深度绑定了 Confluence 缩进功能，因为这个功能实在太好用了！[Obsidian 柳暗花明又一村，心心念的功能终于实现了](https://mp.weixin.qq.com/s?%5F%5Fbiz=MzI5MzMxMTU1OQ==&mid=2247488510&idx=1&sn=be583f1441bb878d10a6e8ef4b17788b&scene=21#wechat%5Fredirect)

![Image](https://proxy-prod.omnivore-image-cache.app/0x0,sMLWwWYmTU6p0afzw7YbHYR8rgY8AUlxU7sswGLDauN4/https://mmbiz.qpic.cn/sz_mmbiz_png/hp9XO4U4Gj9gUOV5pxuoibpvCGl8E3x2PqibSJAsibiaYlvxXgNgU6pEordaBZ50y5O4gjuQ5XncS1Ec783f0eKUIA/640?wx_fmt=png&from=appmsg&tp=webp&wxfrom=5&wx_lazy=1&wx_co=1)

设置页面默认添加了层级属性，

![Image](https://proxy-prod.omnivore-image-cache.app/0x0,sbdTrlDEEDfkSjloS2eVDKQFRQ-QaRw7aFe4KBuUZJBs/https://mmbiz.qpic.cn/sz_mmbiz_png/hp9XO4U4GjibYAB9ga8ibqBkdvf4VejicAKTuoD4lASBRm3Sib6q3EibkILg6fBKicrkKr3u2ODRk0UaSST85QSyydHg/640?wx_fmt=png&from=appmsg)

插件添加了三个快捷键用于设置增加层级、降低层级和取消层级：

![Image](https://proxy-prod.omnivore-image-cache.app/0x0,s4CWC8By70jD3PPFXz2P4z-Ibdkiby7Wb6stUPXanp-8/https://mmbiz.qpic.cn/sz_mmbiz_png/hp9XO4U4GjibYAB9ga8ibqBkdvf4VejicAKicjVe97VibFSFD7RJSVX8MoqKMf2WaVpyUlFQCFW9xP9picOKTRSYhGog/640?wx_fmt=png&from=appmsg)  
  
为了将这个理念深入到骨髓中，以下几种情况会为新建笔记自动添加层级：

1）Note chain Creat new note：创建前置笔记或后置笔记时，创建的笔记使用当前笔记层级；  
2）Note chain Insert node of chain：为当前笔记插入前后置节点时，当前笔记使用节点笔记层级；  
3）文件列表右键创建后置笔记时，新建笔记使用当前层级；  
4）文件列表右键，Move as next note（移动为后置笔记）时，将要移动的笔记层级设置为目标笔记层级；

操作2和操作4，对多选文件同样生效。

视频加载失败，请刷新页面再试

 Refresh 

[本轮更新为每个命令添加了图标](https://mp.weixin.qq.com/s?%5F%5Fbiz=MzI5MzMxMTU1OQ==&mid=2247488694&idx=1&sn=f5cd79589d5366ab1108155c06d722c9&scene=21#wechat%5Fredirect)：

![Image](https://proxy-prod.omnivore-image-cache.app/0x0,senmH_39FEIVlXWS7RXp3XQ1vzLlJ43hfLIfNVMICLJ8/https://mmbiz.qpic.cn/sz_mmbiz_png/hp9XO4U4GjibYAB9ga8ibqBkdvf4VejicAKXibX9MVnxnB8MguZpylGBpfrYnxmVKus80BGMbValuEbdY9lOKGSFVg/640?wx_fmt=png&from=appmsg)

---

> 关注公众号，或点击合集，查看更多内容。

[Read on Omnivore](https://omnivore.app/me/https-mp-weixin-qq-com-s-biz-mz-i-5-mz-mx-mtu-1-oq-3-d-3-d-ascen-1943aaff155)
[Read Original](https://mp.weixin.qq.com/s?__biz=MzI5MzMxMTU1OQ%3D%3D&ascene=56&chksm=ec7551e1db02d8f7c0f83ee51285bda7ec24719f791b566292a45a215d2ee9f66e83b3fc602d&clicktime=1736151365&enterid=1736151365&exptype=timeline_unsubscribed_oftenread_card&fasttmpl_flag=0&fasttmpl_fullversion=7543278-zh_CN-zip&fasttmpl_type=0&finder_biz_enter_id=5&flutter_pos=0&idx=1&mid=2247488713&ranksessionid=1736151279&realreporttime=1736151365746&scene=169&sessionid=1736151364&sn=641766393a6a6b3d6e61afb9c6c54b87&subscene=90)

