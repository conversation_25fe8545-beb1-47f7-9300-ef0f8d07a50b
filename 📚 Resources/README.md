# 📚 Resources - 资源库

存放模板、参考资料和工具配置等支持性内容。

## 目录结构

### 📋 Templates/ - 模板
精简的核心模板文件
- Quick Note - 快速记录模板
- Full Note - 完整笔记模板
- Project Note - 项目笔记模板
- Daily Note - 日常笔记模板
- Review Note - 复习笔记模板

### 📖 References/ - 参考资料
- 技术文档
- 学习资源
- 书籍推荐
- 在线教程

### 🔧 Tools/ - 工具配置
- 开发工具配置
- 系统配置文件
- 脚本工具
- 插件设置

## 使用原则

- 保持资源的实用性
- 定期更新过时内容
- 分类清晰明确
- 便于快速查找

## 维护建议

- 每月检查一次资源有效性
- 及时添加新的有用资源
- 删除过时或无用的内容
- 保持目录结构整洁
