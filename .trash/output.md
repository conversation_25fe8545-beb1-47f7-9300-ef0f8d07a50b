l 随着社会的发展，用户对产品的要求也越来越高，以前看重功能，现在正在逐步转变为性能。

产品出现性能事故，后果都很严重。

ü 没有做性能测试

ü 性能测试做得不充分

ü 对并发/流量预估不正确

l 为什么做性能测试？

ü 最小化成本、最小化风险。

ü 交付高质量的系统。

l 性能测试是什么？

ü 性能测试就是通过特定的方式对被测系统按照一定测试策略施加压力，获取该系统的响应时间、

TPS、吞吐量、资源利用率等指标，来检测系统上线后能否满足用户需求的过程。

ü 负载测试与压力测试是最常用的两种性能测试策略。两者可以结合进行。

l 性能测试的目的是评估当前系统的性能，预测系统以后的性能，找到系统的瓶颈点，进行调优

优化。

评估当前系统

检测系统性能，评估系

统性能。类似体检，对

系统的性能状况有一个

了解。

寻找瓶颈
优化性能

预测未来性能

某业务操作响应时间比

用户数和业务量增加时

较长，上线一段时间后

能否及时应对？是增加

运行越来越慢，需要逐

服务器，还是数据库服

步分析并调优。

务器？还是优化代码逻

辑？

l 系统的性能与它所处的运行环境关系很大。如果把法拉利跑车放到一个乡村的山路上运行，它

可能跑不过一辆拖拉机，因为法拉利底盘低，可能陷到坑中无法运行。同样的道理，同一个软

件系统放到不同的环境下，表现出的性能也可能有天壤之别。

l 影响性能测试环境的环境因素是多方面的，如使用的浏览器、网络带宽、操作系统、Web服务

器、应用服务器、硬件服务器、数据库等。测试的时候任何一个环节都可能会出问题，都可能

会影响系统的性能。

l 软件的性能瓶颈可能不止一处，比如对交通系统来说，堵车是常见的性能问题，堵车的原因可

能是很多中：

ü 道路不够宽，拓宽道路

ü 立交桥设计不合理而引起堵塞

ü 红绿灯设计不合理

ü 交通事故频发路段导致拥堵

l 测试找到一个的瓶颈点，解决之后，可能会发现其他的瓶颈点。

分类

硬件

网络

子项

描述

磁盘空间

磁盘空间不足导致系统运行变慢，文件、日志等无法存放导致性能问题。

CPU

CPU的运行速度影响性能。影响CPU运行速度的性能指标包括：工作频
率、Cache容量、指令系统和逻辑结构等参数。

IO读写速率

输入和输出，主要考虑数据处理时的读写速度等情况。

内存

带宽

所有的程序都是运行在内存中的，内存不足会限制程序的数据处理速度。

高并发情况下，如果带宽不足，可能会导致网络资源竞争，超时等问题。

网络波动

性能测试的环境，需要一个稳定的网络环境。

CDN

延时

丢包

内容分发服务，有时候不同的CDN策略也会影响到“用户”感知到的系
统性能表现。

延时的值越大，对系统性能表现影响越大（比如格斗类的游戏），且性能
测试的结果也存在更大的偏差；

数据在网络上是以数据包的形式传输的，如果丢包，则可能造成报错或异
常的情况。

分类

应用

子项

JVM

描述

堆内存分配：根据系统硬件条件来进行合理的堆内存分配，一般来说JVM的堆内
存分配不要超过系统内存的25%较好；
垃圾回收机制(Garbage Collection)：JAVA的动态垃圾回收机制，是基于不同的
几种回收算法来进行的，根据具体的情况，选择合适的垃圾回收策略；
OOM：即内存溢出（out of memory），这个算是性能测试中很常见的一个问
题，通常是由于代码问题造成的内存泄漏、GC不够彻底、内存被耗尽引起；

代码逻辑

常见的情况有不合理的线程引用和内存分配。

配置

JDK版本

在性能测试过程中，一定要确保被测系统的版本和实际生产保持一致，否则由于
版本不同带来的些许差异可能会对性能测试带来很大的偏差和影响；

底层配置

涉及到操作系统、服务器等硬件的一些配置方式不合理，带来的性能瓶颈；
同一种操作系统，不同的发行版本对性能的影响也是比较大。

参数配置

系统架构设计中，各个不同的参数配置带来的性能瓶颈；

分类

数据库

子项

索引

锁

表空间

慢SQL

数据量

描述

缺少索引，没用的索引。
索引相当于书的目录，执行数据库操作时效率高，能减少磁盘IO操作和执行的数
据库系统时间；索引的维护是需要成本的，甚至使得数据库的性能变低。

死锁：两个或两个以上的进程在执行过程中,由于竞争资源或者彼此通信造成的
一种阻塞的现象,若无外力作用,它们都将无法推进下去。

不合理的表空间设计，导致的数据库性能问题；通过控制用户所占用的表空间配
额，比如gitee控制了某个用户最大的存储容量。

慢SQL会导致数据库操作时间变长，增加IO读写以及引起一些列的资源竞争等问
题。

对同一张表来说，1W条数据和1000W条数据，对其进行操作时的性能表现也是
不同的。

分类

子项

描述

中间件
(位于操作
系统与用
户软件的
中间，比
如Tomcat、
Jetty、
Nginx，
中间件有
很多配置，
设置不同
的参数对
性能影响
很大)

超时时间

设置合理的请求或响应超时时间，是很有必要的，这点要根据具体的业务场景和
系统架构来考虑，具体的超时时间，建议进行配置测试来设定。

线程池

线程池的大小，配置太小，很容易被使用完，太大又浪费资源。

缓存策略

前台请求，后台先从缓存中取数据，取到直接返回结果，取不到时从数据库中取，
数据库取到更新缓存，并返回结果，数据库也没取到，那直接返回空结果。缓存
的优点是减少请求响应过程中的传输时间，但有时候在高并发情况下，很容易导
致缓存穿透（缓存和数据库中都没有的数据）、缓存击穿（缓存中没有但数据库
中有的数据）、缓存雪崩（缓存中数据大批量到过期时间，而查询数据量巨大，
引起数据库压力过大），对服务端带来很大的压力；

最大连接数 合理的连接数配置是很重要的，否则连接数太少导致队列等待、超时，连接数太

通信实现方
式

负载均衡策
略

多则浪费了系统资源。

同步（sync）和异步（Async）。

现在很多的系统都进行了服务集群，随之而来的就是负载均衡策略的实现，如果
负载均衡不够“均衡”，在大数量的冲击下，容易导致某些服务的异常或者挂起。

响应时
间

点击查询后，要等多久才能看到响应？

用户

稳定性

为什么总是失败？

开发
人员

架构设计

架构设计是否合理？

数据库设
计

数据库设计是否合理？

代码是否存在性能问题？

是否有不合理的内存使用？

代码

是否有不合理的线程同步操作？

是否有不合理的资源竞争？

代码的算法是否可以优化？

系统
管理
员

资源
利用率

服务器硬件资源使用合理吗？

数据库使用合理吗？

系统是否可以扩展？

可扩展性

系统有哪些潜在的瓶颈？

更换哪些设置？新增哪些机器？

最多支持多少用户访问？

系统容量

最大业务处理量

稳定性

7*24小时连续不间断的业务访问？

l 测试人员通常是作为软件质量控制的一个角色，不仅仅是找

bug，需要对整个软件的质量负责，性能也属于质量的一部分，

因此测试人员眼中的性能应该是全面的，需要考虑的东西也需

用户

要全面。

l 测试人员需要考虑全面的性能，包括用户、开发、管理者等各

个视角的性能。

l 测试人员在做性能测试时除开要关注表面的现象，比如响应时

间，也需要关注本质，比如用户看不到的服务器资源利用率，

架构设计是否合理？代码是否合理等方面。

测试
人员

管理者

开发者

l 性能测试开始的必要条件是软件系统已经处于一个比较稳定的状态，系统架构、主要代码、中

间件等都不再有大的变化，否则会给性能测试带来很大的风险。

全新项目

功能稳定后；
一般建议在产品的3轮完
整功能测试后开始。

线上系统
大的升级

大的升级功能稳

定后。

l 并发

ü 狭义：所有用户在同一时刻做同一操作，主要是为了验证程序或数据库对并发处理能力。

ü 广义：多个用户对被测系统发起了多个请求，这些请求可以是同一种操作，也可以是不同操作，类似

于混合场景的概念。

l 用户数

ü 系统用户数：该系统的注册用户数，比如某论坛有10000个用户，这些用户可以是活跃的，也可能是

僵尸的。

ü 在线用户数：登录系统的用户，在线用户并不一定都会对服务器产生压力。

ü 并发用户数：系统可以同时承载的正常使用系统功能的用户数量，即对服务器产生压力的用户数量。

ü 网站系统用户数>网站在线用户数>网站并发用户数

l  平均并发用户数计算公式： C = nL/T

ü C是平均的并发用户数；

ü n是平均每天访问用户数；

ü L是一天内用户从登录到退出的平均时间；

ü T指考察的时间段长度，也就是一天内多长时间有用户使用系统。

2 C3
l  并发用户数峰值：                         ，该公式遵循泊松分布理论。

 C

'C

l 计算示例

ü 假设某网上银行的服务，每天访问用户数量为1000万，一天内用户从登陆到退出的平均时间为5分钟，

每天早8点到晚12点均有用户访问，时长为16小时，也就是960分钟。

ü C=10000000*5/960=52083.33/m （即52083.33每分钟）

ü

'C



52083.33


3

2

52083.33



52083.33



846



52767

l 集合点

ü 设置多个虚拟用户等待到一个点，同时触发一个事务，模拟真实环境中多个用户同时操作，同时产生

负载，实现性能测试的最终目的。

ü 让所有请求在不满足条件的时候处于等待状态。集合点设置为50，那么不满足50个请求的时候，这

些请求都会集合在一起，处于等待状态，当达到50的时候，就一起执行。

ü JMeter中可以通过同步定时器 Synchronizing Timer 来完成。

l 事务(Transactions)

ü 用户一个或一系列的操作，代表一定的功能，所有性能测试其实最终都是围绕着事务展开的，事务代

表用户的使用方法和结果，不同的操作组合成不同的事务。比如在线考试，主要的操作流程有登录系

统、进入考试页面、开始答题、保存答案和提交试卷。确保测试脚本中包含场景的各个步骤。

l 思考时间(Think Time)：用户进行操作时每个请求之间的时间间隔。

ü 场景1：为了更加真实的模拟用户的操作，引入了思考时间这个概念。如果想了解系统的最大承受能

力或者极端情况下系统的性能表现，可以设置0思考时间。如果要预估系统的性能，应该最大可能的

模拟真实的思考时间。

ü 场景2：某论坛连续两次发帖时间间隔不能小于15秒钟。为了满足发帖的测试场景，需要增加上思考

时间。

l 吞吐量(Throughput)

ü 单位时间内网络上传输的数据量，也可以指单位时间内处理客户请求数量，它是衡量网络性能的重

要指标。

ü 通常情况下，吞吐量用“字节数/秒”“请求数/秒”和“页面数/秒”“事务数/秒”来衡量。

l TPS(Transactions Per Second)每秒事务数

ü 服务器在单位时间内可以处理的事务数量，一般以request/second为单位；它是衡量系统处理能力

的重要指标。

ü TPS大时，系统性能会比较好，但是不可能无限大。例如：压力加大时，TPS曲线如果变化缓慢或有

平坦的趋势，很有可能是服务器开始出现瓶颈了。

ü 如果环境没有发生大的变化，系统最大处理事务能力不会随着并发用户数的多少而改变。比如说小

寨的地铁检票机，只有两台进站的机器，一次一台机器只能通过一个人，不论是10个人来，还是100

个人来。

l QPS(Queries Per Second)每秒查询率

ü 一台服务器每秒能够响应的查询次数，是对一个特定的查询服务器在规定时间内所处理流量多少的

衡量标准。

l RPS（Requests Per Second）每秒请求数

l TPS与QPS的区别

ü TPS和QPS都是衡量系统处理能力的重要指标，一般和并发结合起来判断系统的处理能力；

ü TPS即每秒处理事务数，比如如下的三个过程构成一个事务，每秒能够处理3个这样的过程，TPS为3。

• 用户请求服务器

• 服务器自己的内部处理

• 服务器返回给用户

ü 上面的一次事务，可能对服务器有多次请求，比如一次上面的事务过程有5个请求，那TPS为3时，

QPS就是15。

l RT/ART

ü Response Time/average Response Time：响应时间/平均响应时间，指一个事务花费多长时间完

成；

ü 一般来说，性能测试中平均响应时间更有代表意义。细分的话，还有最小最大响应时间，50%、

90%用户响应时间等；

l 响应时间：对请求作出响应所需要的时间。

ü 网络传输时间：N1+N2+N3+N4

ü 应用服务器处理时间：A1+A3

ü 数据库服务器处理时间：A2

ü 响应时间=N1+N2+N3+N4+A1+A3+A2

l 前端页面的解析展示时间一般不计算在响应时间之内，因为每个浏览器解析页面的方式不一样，

时间也不一样。

l 响应时间的2-5-8原则：

ü 当用户能够在2秒以内得到响应时，会感觉系统的响应很快；

ü 当用户在2-5秒之间得到响应时，会感觉系统的响应速度还可以；

ü 当用户在5-8秒以内得到响应时，会感觉系统的响应速度很慢，但是还可以接受；

ü 而当用户在超过8秒后仍然无法得到响应时，会感觉系统糟透了，或者认为系统已经失去响应，而选

择离开这个Web站点，或者发起第二次请求。

l Load Testing=Large amout of users

l 概念：在一定的软件、硬件和网络环境下，通过运行一种或多种业务在不同用户数量情况下，

测试服务器的性能指标是否在用户的要求范围内，用于确定系统所能承载的最大用户数、最大

有效用户数以及不同用户数下的系统响应时间及服务器的资源利用率。

l 通俗：通过逐渐增加系统负载，测试系统性能的变化，并最终确定在满足性能指标的情况下，

系统能承受的的最大负载。负载测试是最常用的一种性能测试方法。

l 目的：不断加压，找出系统能承受的最大负载量。确定并确保系统在超出最大预期工作量的情

况下仍能正常运行。

l 测试方法：从比较小的负载开始，逐渐增加模拟用户的数量，直到应用程序响应时间超时。观

察系统的各项性能指标。需要测试多次，用户从少到多。初始用户量，每次加多少用户，可以

咨询开发或SE。

轮次

用户量 指标

业务

第一轮 200

服务器：CPU 10%，内存 5%  响应时间：2s 错误率5% 功能正常

第二轮 220

服务器：CPU 15%，内存 8%

第三轮 240

服务器：CPU 35%，内存 20%

第四轮 260

服务器：CPU 55%，内存 40%

第五轮 280

服务器：CPU 75%，内存 75%

第六轮 300

服务器：CPU 80%，内存 80%

第七轮 320

服务器：CPU 100%，内存 90%

l 280是系统最大有效用户数量

l 300是系统最大用户数。

功能正常

功能正常

功能正常

功能正常

有些操作响应比较慢，
但是所有用户功能正常

部分功能不正常

l 测试对象：猪八戒人猪混合系统

l 负载测试：猪八戒背着高小姐走路，我们观察猪八戒的生理和心理指标是否存在异常，根据观

察的数据判断“猪八戒人猪混合系统”的瓶颈所在。

ü 如果猪八戒背着背着腰酸背疼腿抽筋，那么猪无能同志可能是缺钙了，需要补钙；

ü 如果他背着背着头晕眼花四肢麻木，那么猪同志可能是脂肪肝、酒精肝三高患者，这就证

明猪八戒需要减肥了。

ü 如果猪八戒背着媳妇身轻如燕、健步如飞，那么继续加压，再做一次测试。观察他的各种

反应，重复执行，直到找到他的瓶颈为止。

ü 以上并没有具体的测试标准，我们可以给出测试标准，即指标：背着体重为45公斤的高小

姐走上一段山路十八弯总长为10公里的羊肠小道，在此过程中猪八戒同志的平均时速不能

低于 8km/h，其心跳不能快于60次/秒。

l Stress Testing=Too many users,too much data, too little time and too little room.

l 概念：在一定的软件、硬件和网络环境下，通过模拟大量的虚拟用户向服务器产生负载，使服

务器资源处于极限状态下长时间持续运行，以测试服务器在高负载情况下能否稳定工作。

l 通俗：模拟用户在同一时间段，对服务器发送大量的请求，以此来看服务器的性能指标。

l 目的：测试系统在非正常、超负荷的条件下的运行情况，用来评估在超越最大负载的情况下系

统将如何运行，考验系统在正常情况下对某种负载强度的承受能力。

l Concurrent Test

l 概念：模拟多个用户同时访问同一个应用、同一个存储过程或者数据记录以及其他并发操作。

测试是否存在死锁、数据错误等故障。验证系统的并发能力。

l 目的：找出并发引起的问题。

l Benchmark Test，概念：一种测量和评估软件性能指标的活动。我们可以在某个时候通过基准

测试建立一个已知的性能水平(称为基准线)，当系统的软硬件环境发生变化之后再进行一次基

准测试以确定那些变化对性能的影响。

l 测试方法：基准测试通常都是些功能测试。比如选择5个用户执行，取交易的平均响应时间作为

衡量指标，并计算吞吐量。

l 应用场景：

ü 可以在制定的标准下通过基准测试建立一个性能基准，这样以后当系统的环境、参数发生变化之后，

再进行一次相同标准下的测试，即可看出变化对性能的影响。

ü 系统进行基准测试可以在较早的阶段发现性能问题。例如，如果对某网站进行10个用户并发测试时，

系统出现了死机的现象，那么就没有必要进行后续。

ü 某系统从来没有进行过任何性能测试，需要对该系统做一次性能评估作为后续开发调优的参考。这

是基准测试常见的一种场景。

l 测试对象：猪八戒人猪混合系统

l 压力测试：加大负载，极端负载下进行测试，如一次性背10个媳妇。找到全身最薄弱的部分,找

到系统瓶颈。压力测试一定要测出问题，否则认为负载太小。

l 并发测试：主要测试一次能背几个媳妇。假如我们的目标是一次能背4个媳妇的话，看下此系统

是否达标。

l 基准测试：如果猪八戒同志在背高小姐的时候没有服用任何的违禁药品，那么我们可以将此次

的测试结果作为一个基点。然后让猪八戒同志喝点红牛或者使用兴奋剂，然后进行同样的负载

测试，查看喝红牛对猪八戒背高小姐这个行为是否产生了利弊影响。当然我们也可以不让猪八

戒同志背高小姐，而换成是让孙悟空同学背高小姐，观察这两次测试的测试结果，从而确定究

竟哪一种系统更能胜任“背高小姐”这个重任。

l 概念：Stability Test ，给系统一定业务压力的情况下，使系统运行一段时间，以此检测系统是否稳定。

l 目的：验证系统是否存在内存泄漏等问题。

l 有些软件的问题只有在运行一天或一个星期甚至更长的时间才会暴露。这种问题一般是程序占用资源却不

能及时释放而引起的。比如，内存泄漏问题就是经过一段时间积累才会慢慢变得显著，在运行初期却很难

检测出来；还有客户端和服务器在负载运行一段时间后，建立了大量的连接通路，却不能有效地复用或及

时释放。

l 稳定性测试一般不用最大的并发来测试，最大并发用户量在一年的运行中极少出现或者根本就不会出现。

例如，淘宝的用户高峰是“光棍节”的营销，这个高峰一年可能也就出现一次，但是为了这次营销活动淘

宝系统必须要能支撑这个最大并发用户量。在淘宝其余的运行时间内常见的负载压力可能是最高峰的

20%~40%的用户量，研发人员应该更加关心的是这个负载压力下系统是否稳定。就像用户买车一样，用

户平时开车最常见的时速是60km/h、80km/h、100km/h，那么用户最关心的应该是这些时速下车的稳

定性、油耗、噪声等方面的参数。

l 注意点：一般进行7*24小时稳定性测试。场景的设计以模拟真实用户的实际操作为佳。

l 概念：失效恢复测试针对有冗余备份或者负载均衡的系统来说，检查如果系统内部发生故障，

系统对故障如何应付，保证系统可以正常启动，用户是否可以继续使用。

l 过失效恢复测试一般是对具有负载均衡的系统进行的，主要是为了测试当系统局部发生故障时，

是否会对全局产生大的影响，产生的影响是否在可以接受的范围内，以及用户能否继续使用系

统。在实际应用过程中，可以模拟一台或几台负载均衡机器出现故障来进行失效恢复测试，但

需要注意的是，不仅要关心失效后，用户是否可以正常访问或者恢复后系统是否可以正常工作，

也要关注失效后，系统还能支持多少并发用户，以及采用哪些备选方案来快速响应。

l 失效恢复测试重在关注系统出现问题后能否根据预先制定的策略恢复，且恢复后能否正常运行。

以跑马拉松为例，为了预防出现跑不动的情况，预先准备了一瓶红牛，当选手累得躺下后，拿

出这瓶红牛一口气喝了，然后有力量了，恢复了原来的状态，站起来继续跑。

l 稳定性测试：让猪八戒背高小姐背上七七四十九天，观察猪同学的表现。如果系统设置是要背

49天，他只背了36天就受不了了。那就认为不达标，要继续优化。

l 可恢复性测试：让猪八戒背孙悟空走上半天，猪八戒已累得接近崩溃，然后再换成背高小姐，

看它是否能从疲劳中恢复。

同一时刻向系统提交
请求的用户数。

系统处理事务响应时间的平
均值，一般响应时间为3秒。

并发
用户数

每秒事务数
TPS

响应时间

事务
成功率

Transaction per second 系统单位时间处理的
事务数。eg：1分钟之内1000次交易，吞吐量为
1000/60=16.7

事务成功的比
例。

CPU像人的大脑，能反映出系统的繁忙程度。
系统CPU是处理系统本身所占用的资源。
用户CPU是处理程序所占用的资源。
一般观察用户进程与系统进程消耗的CPU时间百分比。
一般可接受的上限为85%。

磁盘主要用于存取数据，存数据的时候对应的是写IO
操作，取数据的时候对应的是是读IO操作。主要是度
量磁盘读写性能。

CPU
使用率

内存
利用率

磁盘I/O

网络I/O

内存就像人脑的记忆区域，将各种
信息收集起来存放。
数据从内存读取比从硬盘读取快。
内存经常发生内存泄漏或内存溢出。

表示为发送和接收字节的速率。判断网络
连接速度是否是瓶颈，可以用该计数器的
值和目前网络的带宽比较。看是否有带宽
的瓶颈。

l 性能测试曲线模型是一条随着测试时间不断变化的曲线，与服务器资源，用户数或其他的性能

指标密切相关的曲线。

l 坐标轴横轴，从左到右表现了并发用户数（Number of Concurrent Users）的不断增长。

l 2个点

ü The Optimum Number of Concurrent Users最佳并发用户数

ü The Maximum Number of Concurrent Users最大并发用户数

l 3条曲线

ü Utilization(U)：表示资源的利用情况，包括硬件资源和软件资源。在第一区域稳定增长，在第二区

域小幅增长，在第三个区，呈直线，表示饱和。

ü Throughput(X)：吞吐量，每秒事务数。随着并发用户数的增加，在前两个区，并发用户数的增加，

请求增加，吞吐量增加，中间的区域，处理达到顶点。

ü Response Time(R)：响应时间。随着并发用户数的增加，在前两个区，响应时间基本平稳，小幅递

增。在第三个区域，急剧递增。在第三个区的点为拐点。

l 3个区

ü Light Load：轻压力区，等于最佳并发用户数时，系统的整体效率最高，没有资源被浪费，用户也

不需要等待。

ü Heavy Load：重压力区，也就是系统负载处于最佳并发用户数和最大并发用户数之间时，系统可以

继续工作，但是用户的等待时间延长，满意度开始降低，并且如果负载一直持续，将最终会导致有

些用户无法忍受而放弃。

ü Bockle Load：超负荷区，当系统负载大于最大并发用户数时，将注定会导致某些用户无法忍受超长

的响应时间而放弃。

l 怎么找到系统的拐点？所谓性能测试拐点，就是指并发用户达到一定数量，平均响应

时间递增，TPS不增反降，报错率递增。

ü 阶梯式加压法

• 先设定一个预估值进行测试，观察系统的响应情况，然后增加一定的数量，

观察系统的变化，直到系统超出我们所预估的值。比如，在并发测试的时候，

我们先预估设置并发用户为2000，然后以200的速度递增，检查系统的响应

时间是否小与3秒，从而找出并发测试的系统拐点

ü 二分逼近法

• 先预估两个值m和n

• 先用m来进行测试，如果测试不通过，以m/2 继续测试。

• 如果m通过测试了，就用n值来进行测试，如果n值测试不通过，我们可以确

定拐点在m与n之间，于是取（m+n）/2继续测试。

• 如果n值测试通过了，拐点比n大，找一个比n大的数字x继续测试。

• 当最大值与最小值在500内，认为找到拐点

l 某地铁站进站只有3个刷卡机。人少的情况下，每位乘客很快就可以刷卡进站，假设进站需要

1s。乘客耐心有限，如果等待超过30min，就会暴躁、唠叨，甚至选择放弃。

ü 场景1：1名乘客进站时，该乘客在1s时间内完成进站，且只利用了一台刷卡机，剩余2台。

ü 场景2：2名乘客进站时，2名乘客在1s的时间内完成进站，且利用了2台刷卡机，剩余1台。

ü 场景3：3名乘客进站时，3名乘客在1s的时间内完成进站，且利用了3台刷卡机，资源得到充分利用。

ü 场景4：随着上班高峰的到来，乘客也越来越多，6名乘客进站，A、B、C乘客进站时间为1s，而D、

E、F乘客进站的时间是2s（1s等待时间+1s进站时间），响应时间延长了。

ü 场景5：10名乘客进站，有3名的“响应时间”为1s，有3名的“响应时间”为2s（等待1s+进站1s），

还有3名的“响应时间”为3s（等待2s+进站1s），1名乘客的“响应时间”为4s（等待3s+进站1s），

如果随着大量的人流涌入进站，可想而知就会达到乘客的忍耐极限。

ü 场景6：如果地铁正好在火车站，比如西安北客站。每名乘客拿着大小不同的包，有的乘客拿的包太

大导致卡在刷卡机那堵塞，这样每名乘客的进站时间会又不一样。

l 解决办法

ü 地铁进站的刷卡机有加宽的和正常宽度的两种类型，那么拿大包的乘客可以通过加宽的刷卡机快速进

站（增加带宽）。

ü 多开几个刷卡机，增加进站的人流与速度（提升TPS）。

ü 通过增加发车频率（加快应用/数据库的处理速度）

ü 增加车厢数量（增加内存、增大吞吐量）

ü 增加线路（增加服务的线程）

ü 限流、分流等多种措施来解决问题。

l 性能测试需求分析

ü 必要性评估

ü 性能测试需求分析

ü 性能指标分析与定义

ü 工具选型

ü 性能测试需求评审

l 性能测试计划制定

l 性能测试设计

ü 测试模型构建

ü 测试场景设计

ü 测试用例设计

l 性能测试环境搭建

ü 业务环境搭建

ü 监控服务器、数据库

l 性能测试脚本开发

ü 测试数据构造

ü 录制脚本

ü 场景设置

l 性能测试执行以及结果收集

ü 阶梯式加压

l 分析结果与性能调优

l 性能测试报告

l 性能测试从用户应用、系统架构设计、硬件配置等多个维度分析可能存在性能瓶颈的业务。

l 必要性评估

ü 被测对象需要经过主管部门或监管部门的审查、认可，需要提供性能测试报告。

ü 涉及财产、生命安全的系统。比如电商系统、金融业务系统、医疗健康评估，涉及用户等资金交易，

生命安全类的。

ü 首次投产的大型系统，具有大量用户使用的核心业务。

ü 与历史系统对比，系统核心数据库、业务逻辑、软硬件有大的升级。

ü 业务量、用户量、节点增长30%以上，具体数值可以根据实际情况调整。业务节点增长一般是因为

业务需求，增加应用节点，比如银行拓展分行，分中心，分公司等。

ü 系统架构发生重大变化。

ü 生产环境严重缺陷修复后，需要开展性能测试活动，验证修改是否对生产环境产生不良影响。

l 从最终用户的角度，分析需要进行性能测试点，分析业务模型，提取性能测试业务。系统支持哪几类用户，每种用户

有哪几种典型的场景。用户频繁使用，且存在大量用户使用的业务流程。特殊交易日、特殊业务场景、发生重大流程

调整的业务流程。

l 比如某编程等级考试网站，典型的用户以及各类用户的使用场景：

ü 用户-考生（考试）：

• 考试场景：登录系统->打开考卷->答题->保存试卷->提交试卷->退出登录

• 查询成绩场景：登录系统->查询考试成绩->退出登录

• 模拟考试场景：登录系统->练习->退出登录

ü 用户-监考老师（监考、改卷）：

• 登录系统->监考->退出登录

• 登录系统->改卷->退出登录

ü 用户-官方运维（发布试题、给考生分配监考老师）

• 登录系统->发布试题->退出登录

• 登录系统->给考生分配监考老师->退出登录

l 从项目的角度，分析需要进行性能测试点：

ü 性能测试后调整了架构的业务。

ü 逻辑复杂，关键的业务。

ü 可能消耗大量资源的业务。

ü 与外部系统存在接口调用，有大量数据交互的业务。

l 用户响应时间

l 系统配置(内存、CPU、缓存、是配

并发数

置等)对性能的影响。

l 可靠性：系统在连续高负载下的稳定

性级别。

ü 参考历史版本的数据

ü 参考同行竞品

响应
时间

吞吐量

资源
耗用

业务
成功率

TPS

指标分析
与定义

测试项

响应时间 业务成功率

业务量

并发测试

CPU使用率

内存使用率

登录

<=5秒

随机购买商品

<=5秒

100%

100%

2小时5万次

2小时5万次

100

100

<=80%

<=80%

<=80%

<=80%

l 测试工具选型

ü 能否自研，自研的工具更有针对性。

ü 商用工具所需的成本，企业是否能承受。如果购买一套价格几十万的性能测试工具只是为了去做一

个几万元预算的性能测试项目，得不偿失。

ü 采用的功能是否提供了完善的服务与培训。

ü 团队人员是否掌握了测试活动所需的技能。熟悉并使用一个性能测试工具，是需要花费人力和时间

等资源的。

l 工具优劣

ü JMeter开源工具，应用比较多，不依赖界面，对测试工程师要求不高，提供了参数化、函数、关联

等功能便于脚本的优化与扩展。

ü LoadRunner市场占有率比较高，与JMeter相比，LoadRunner有很强大的脚本开发功能，完善的

函数库以及结果分析功能。对测试工程师要求比较高，资料比较多，便于学习。

l 可测性

ü 性能测试尽可能模拟真实的运行环境。性能测试环境与生产环境差异比较大时，性能测试的结果不

可信。

ü 如果无法搭建与生产环境相似的环境，则认为不具备性能的可测性。

l 一致性

ü 本次测试的需求是否满足用户需求规格说明书明确列出的性能需求项。

ü 以历史性能数据以及现今的运行数据为基础，规划未来业务发展的可能性，确保测试指标具有一定

的冗余度。

l 正确性

ü 保证SE/BA、开发、测试、项目经理等角色，对关注的性能需求、性能指标的正确理解，从而减少

返工、重新设计的风险。

l 测试计划包括但不限于

ü 明确性能测试范围

ü 性能测试环境

ü 性能测试所需的资料规划以及筹备计划

ü 性能测试工作项目以及进度安排

ü 性能测试出口标准

ü 性能测试风险管理

l 根据之前的测试分析，设计测试用例，单业务测试场景设计，比如租车系统中：

ü 单业务基准测试：测试某个具体的业务是否满足系统设计或用户期望的指标。查询车辆的接口1个用

户时，响应时间多久。

ü 单业务压力测试：测试某个具体业务在最大负载下的稳定性。比如查询车辆的接口50个并发用户，

长时间运行，是否稳定。

ü 单业务负载测试：测试某个具体业务能够承受的最大负载。比如基准负载为50个，通过多次测试，

逐步增加负载，最终获得被测业务的最佳负载。在最佳负载下，系统仍满足各项性能指标。

l 综合业务场景设计，

ü 综合业务基准测试

ü 综合业务压力测试

ü 综合业务负载测试

ü 综合业务稳定性测试

l 测试用例主要用于指导脚本开发工程师如何

开发一个性能测试脚本，应该明确操作流程、

开发方式（录制、手工编码、外部封装）、

脚本优化方式等内容。

用例编号

脚本编号

约束条件

测试数据

ü 基准测试、压力测试和负载测试，只是测试

操作步骤

目的不同，往往只需设计一个脚本。

定时器

集合点

参数化

关联

断言

测试执行人

测试日期

脚本用例设计——登录

PerformanceTesting_Login_001

PerformanceTesting_Login_001.jmx

用户名不能重复，需要做参数化

60000次

1.打开网址(get)
2.输入用户名和密码，提交登录信息
(post)
3.退出登录(get)
输入账号信息5秒，登录成功等待返回主
页3秒，退出登录返回主页3秒。

不涉及，本次不设置集合点。

登录用户名和密码，调用csv

不涉及

登录成功设置断言

l 定义系统指标：响应时间小于3s，内存、CPU使用小于80%，失败率在2%以内。

l 单业务测试

ü 场景：查询车辆的接口

ü 基准测试：查询车辆的接口，单个用户执行耗时。

ü 负载测试：查询车辆的接口，在自己的测试环境上，最佳并发多少？最大并发多少？

ü 压力测试：查询车辆的接口，最佳并发下，能稳定运行多久？

l 综合业务测试

ü 场景：租车系统，十来个页面，多用户并发访问这些页面

ü 稳定性测试：该场景，并发100个用户，是否能稳定运行2小时。

l 多个接口组成一个事务

ü 场景：添加车辆->查询车辆->删除车辆->查询车辆

ü 负载测试：该场景，在自己的测试环境上，最佳并发多少？最大并发多少？

l 性能测试环境搭建

ü 硬件环境搭建，建立近似真实的环境，服务器、数据库以及中间件的真实。

ü 业务环境搭建

ü 预置数据，最好将生产环境的数据导入进去

l 部署监控软件

ü 服务器监控

•

ServerAgent：服务端性能监控，部署到服务上并启动，可以收集服务器上的资源信息，CPU，内存，磁盘，网络。

• Windows性能计数器：能实时采集、分析系统内的应用程序、服务、驱动程序等的性能数据。

•

NMon：Linux系统监控

ü Java 性能监控（略）

•

•

JConsole：Java进程的监控工具，查看堆内存，线程，类以及CPU占用率等。

JVisualVM：监控内存泄露，跟踪垃圾回收，执行时内存、cpu分析，线程分析。

ü 数据库监控（略）

•

Spotlight：监控数据库运行情况。

l PerfMon是JMeter用来监控系统资源的一款插件，可以用来监控系统的CPU、内存、I/O等性

能指标。

l 安装方式

ü https://jmeter-plugins.org/wiki/PluginsManager/下载JMeter插件管理包，放到\lib\ext路径下，

重新启动JMeter。

ü 选项->Plugins Manager，打开插件管理器，选择PerfMon安装。

l 依赖的工具

ü PerfMon的使用需要ServerAgent服务的支持，https://jmeter-plugins.org/wiki/PerfMonAgent

下载ServerAgent-2.2.1.zip。

ü ServerAgent与JMeter通信，使用TCP协议，4444端口。

ü Windows下启动startAgent.bat。

ü Linux下启动startAgent.sh。

ü 在需要监控的服务器上运行ServerAgent。

l 指标

ü CPU：各指标项，数值都是代表百分比

ü Memory：usedperc(默认)和freeperc两项的数值代表与总内存的百分比，其余指标项的数值都是指

内存大小

ü Disks I/O：各指标项中，queue(默认)的数值代表等待I/O队列长度，reads、writes分别代表每秒处

理的读/写次数，readbytes、writebytes顾名思义，代表每秒读/写的数据量，单位同样在Metric

Unit区域配置，通常Mb会比较适合观察。

ü 用不到的指标可以暂时不去了解。

l 注意

ü 如果数据量大，建议将各个监控指标项单独使用一个PerfMon监听器，从而配置不同的指标项数据存

储到不同的文件中，测试执行完毕后，载入数据和查看数据都会更快。

ü 如果数据量不会太大，可以以服务器为单位来划分PerfMon监听器。

1.从CSV文件读取
2.从数据库读取
3.函数生成数据

1.按照不同的测试场景、测
试类型进行设置
2.设置线程数，持续时间等

构造测试数据

录制脚本

场景设置

1.使用badboy录制脚本
2.修改脚本
3.必要时添加计时器
4.增加断言

l 监听器(Listener)负责收集测试结果，支持将结果数据写入文件。同时也被告知了结果显示的方

式。常用的包括：

ü 聚合报告

ü 汇总报告

ü 察看结果树（性能测试时一般不用）

ü jp@gc - Active Threads Over Time，线程数和时间的关系图

ü jp@gc - Response Times Over Time，响应时间和时间的关系图

ü jp@gc - Transactions per Second，TPS

ü jp@gc - PerfMon Metrics Collector，CPU、内存等信息

l 线程数：设置发送请求的用户数，也就是并发数。

l Ramp-Up时间：线程启动时间，单位是秒。也就是线程在多少时间内全部启动。如果是0，则同时启动。

假设配置线程数为20，Ramp-Up时间配置为5，则会每隔5/20=0.25s启动一个线程，1秒启动4个线程。

l 循环次数：请求的重复次数，如果选择"永远"，那么请求将一直继续；如果输入数字，那么重复指定的次

数。

l 这个可以模拟递增式并发(可以递增，也还可以递减)，并可设置递增次数、递增启动延迟、递增时长、到

达目标递增数量保持时长等。

ü This Group will start 100 threads：这次的测试总共100个线程。

ü First , wait for 0 seconds：等待0s后开始起线程，也就是不等待直接起线程。

ü Then start 0 threads：从0个线程开始持续增加。

ü Next，add 10 threads every 30 seconds：每增加10个线程后会运行30s，再起余下的10个线程，再运行30s，

以此类推。

ü Using ramp-up 5 seconds：前面每起10个线程的时候花5s，与上面结合起来即5s内起10个线程，运行30s，然

后再5s内再起10个线程，再运行30s，以此类推。

ü Then hold load for 60 seconds. ：全部的线程起来后，运行60s 后开始停止。

ü Finally , stop 5 threads every 1 seconds：最后停止线程，5个线程停一次，等1s再停5个线程。

l 对比

ü Stepping Thread Group ：设置并发用户数，持续时间等，每隔多少时间自动增加多少个用户

ü Concurrency Thread Group：预设一个目标并发数，每隔一段时间增加一部分并发数，直到 TPS 达到目标并发

数，然后持续运行一段时间。

测试项 结果属性 响应时间 业务成功率 并发测试 CPU使用率 内存使用率

随机购
买产品

预期结果

<=5

实际结果

2.302

通过/失败

Y

100%

100%

Y

100

100

Y

<=80%

<=80%

>90%

N

20%

Y

l 通常情况下，系统出现性能问题的表象特征有以下几种，一旦出现以下情况，基本可以判定系

统存在性能问题：

ü 响应时间平稳但较长：测试一开始，响应时间就很长，即使减少线程数量，减少负载，场景快执行

结束，响应时间仍然很长。负载的增加与否响应时间都很长。

ü 响应时间逐步变长：测试时，负载不变，但是运行时间越长，响应时间越长，直至出现很多错误。

ü 数据积累导致错误：开始运行正常，数据量增加到一定规模，出现错误，无法消除，只能重启系统。

ü 应用程序崩溃：特定场景或运行周期很长以后，突然发生错误，系统运行缓慢、挂死、重启等问题。

ü 内存溢出：运行一段时间，内存耗尽，内存不足。

ü 服务器压力不均衡：多台服务器，只有一台CPU超过60%，其他都在60%以下。

ü TPS波动较大：

ü 并发数不断增加，TPS上不去，CPU使用率较低：

ü 压测过程中TPS不断下降，CPU使用率不断降低：

l CPU比较高，怎么分析？

ü 查看是哪个进程占用的CPU高。

• 如果无关进程，则kill掉。

• 如果是目前测试的程序，则CPU可能为瓶颈。

ü 验证是否CPU是否真的是瓶颈。

• 尝试降低并发数，查看各指标。

• 找个干净环境或者配置高的环境继续测。

l 性能测试是一个严谨的推理过程，一切以数据说话，在没有明确证据证明系统存在性能问题时，

千万不可随意调整代码、配置、甚至是架构。因为一旦调整，就必须重新开展功能以及性能回

归测试，而且可能影响现网业务。

l 性能调优后，需做功能及性能回归测试，确保调优活动正确完成，且未造成额外的影响。

l 解决一个性能瓶颈，往往又会出现另外的瓶颈或者其他问题，所以性能优化更加切实的目标是

做到在一定范围内使系统的各项资源使用趋向合理和保持一定的平衡。

l 开发该测试场景的脚本

ü 添加一辆车：/carRental/car/addCar.action

ü 获取车的列表：/carRental/car/loadAllCar.action?page=1&limit=1000

ü 等待10s

ü 删除添加的这辆车：/carRental/car/deleteCar.action

ü 再次获取车的列表：/carRental/car/loadAllCar.action?page=1&limit=1000

l 完善脚本

ü 在涉及参数的地方，进行参数化

ü 增加断言

ü 合适的地方增加定时器

l 进行压测并记录压测过程数据，找出最优的并发用户数

