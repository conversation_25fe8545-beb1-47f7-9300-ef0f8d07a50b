---
created: 2025-01-08
type: quick-note
tags: [automation, ai, software-development, workflow]
---

# 三体协同自动化软件工厂

## 快速记录

这是一个关于"三体协同"自动化软件开发工厂的设计想法，通过三个AI实体的协同工作来实现自动化软件开发。

## 核心架构

### 三个核心组件

#### 1. 模拟人类操作员 (BabyAGI)
- **核心职责**: 发起对话、传递信息、监控流程
- **角色定位**: 扮演不知疲倦的"人类操作员"
- **主要工作**:
  - 阅读需求文档，理解项目蓝图
  - 发起初始指令给Augment插件
  - 持续监控和推进开发流程

#### 2. 编码与工具调用中枢 (Augment插件)
- **核心职责**: 理解指令、调用工具、生成代码
- **角色定位**: 强大的"前台接待"和"总机"
- **主要工作**:
  - 接收来自BabyAGI的指令
  - 调用MCP决策大脑
  - 利用免费额度生成最终代码

#### 3. 决策与记忆大脑 (DevWorkflowEngine MCP)
- **核心职责**: 管理记忆、分解任务、遵循规范、提供上下文
- **角色定位**: 纯粹的高性能后端服务
- **主要工作**:
  - 响应Augment的调用
  - 执行项目初始化逻辑
  - 进行"四层套娃"的提示组合

## 工作流程

### 初始化阶段
1. BabyAGI读取7个需求文档
2. 理解项目全貌和细节
3. 向Augment发起初始化指令

### 开发阶段
1. Augment接收指令并调用MCP
2. MCP执行决策逻辑并返回完美指令
3. Augment基于指令生成代码

### 协同特点
- 三个实体各司其职
- 无缝的信息传递
- 自动化的流程推进

## 相关链接

<!-- 添加相关的笔记链接 -->
- [[自动化开发流程]]
- [[AI协同工作]]

## 后续行动

- [ ] 详细设计各组件接口
- [ ] 实现MCP核心逻辑
- [ ] 测试三体协同流程
- [ ] 优化工作流程

---
*这是一个快速笔记，需要后续整理和完善*
*原始位置: 总览.md*
