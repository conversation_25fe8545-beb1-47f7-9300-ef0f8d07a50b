# 📖 Knowledge - 知识库

存放经过整理和完善的正式笔记，是知识体系的核心。

## 目录分类

### Testing/ - 测试技术
- 自动化测试
- 性能测试
- 接口测试
- 测试框架
- 测试理论

### Development/ - 开发技术
- 编程语言 (Python, Java, etc.)
- 框架和库
- 数据库技术
- API开发

### DevOps/ - 运维技术
- Linux系统管理
- 容器技术 (Docker, K8s)
- CI/CD
- 监控和日志

### Personal/ - 个人相关
- 学习计划
- 职业发展
- 生活记录
- 其他个人内容

## 笔记标准

### 内容要求
- 结构完整清晰
- 内容准确详实
- 包含实用示例
- 逻辑性强

### 格式规范
- 使用统一模板
- 标题层次分明
- 代码格式规范
- 适当的标签和链接

## 维护原则

- 定期更新内容
- 检查链接有效性
- 补充新的知识点
- 删除过时信息
