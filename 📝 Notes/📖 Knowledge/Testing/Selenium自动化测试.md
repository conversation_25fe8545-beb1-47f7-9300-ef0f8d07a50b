---
created: 2024-11-30
updated: 2025-01-08
type: full-note
status: migrated
tags: [selenium, automation-testing, web-testing, ui-testing, testing-framework]
aliases: [Selenium测试, Web自动化测试]
---

# Selenium自动化测试

## 概述

Selenium是一个强大的Web自动化测试框架，支持多种浏览器和编程语言。本笔记涵盖Selenium的核心概念和实践方法。

## CSS选择器详解

CSS选择器是一种强大的元素定位方式，速度快且功能强大。

### 基本选择器
- **标签选择器**: `input`、`div`、`a`
- **ID选择器**: `#id`
- **类选择器**: `.class`
- **组合选择器**:
  - 标签+ID: `input#kw`
  - 标签+类: `input.s_ipt`
  - 属性选择器: `[属性名=属性值]`

### 模糊匹配
- **开头匹配**: `[属性名^=属性值]`
- **结尾匹配**: `[属性名$=属性值]`
- **包含匹配**: `[属性名*=属性值]`
- **分隔匹配**: `[属性名~=属性值]`

### 层级选择器
- **子元素选择器**: `>`
- **后代选择器**: 空格
- **兄弟选择器**: `~`
- **相邻选择器**: `+`

### 特殊选择器
- **第一个子元素**: `:first-child`
- **最后一个子元素**: `:last-child`
- **第n个子元素**: `:nth-child(n)`

## 浏览器操作

### 基本操作
```python
driver.get(url)          # 打开网页
driver.back()            # 后退
driver.forward()         # 前进
driver.refresh()         # 刷新
```

### 窗口操作
```python
driver.set_window_size() # 设置窗口大小
driver.minimize_window() # 最小化窗口
driver.maximize_window() # 最大化窗口
```

## 元素定位

### 常用定位方法
```python
# 通过ID定位
element = driver.find_element(By.ID, "element_id")

# 通过CSS选择器定位
element = driver.find_element(By.CSS_SELECTOR, ".class_name")

# 通过XPath定位
element = driver.find_element(By.XPATH, "//div[@class='example']")
```

## 实践要点

### 等待机制
- 使用显式等待而非隐式等待
- 合理设置超时时间
- 等待元素可见或可点击

### 最佳实践
- 优先使用ID和CSS选择器
- 避免使用绝对XPath
- 合理使用Page Object模式
- 添加适当的异常处理

## 相关资源

### 参考资料
- Selenium官方文档
- WebDriver API文档

### 相关笔记
- [[Web测试基础]]
- [[自动化测试框架]]
- [[Python测试工具]]

### 外部链接
- [Selenium官网](https://selenium.dev/)

## 总结

Selenium是Web自动化测试的首选工具，掌握CSS选择器和基本操作是使用Selenium的基础。合理的等待机制和定位策略是编写稳定测试脚本的关键。

---
*最后更新: 2025-01-08*
*原始位置: 5-Full Notes/Selenium自动化测试.md*
