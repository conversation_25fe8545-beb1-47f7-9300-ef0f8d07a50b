---
created: 2024-11-04
updated: 2025-01-08
type: full-note
status: migrated
tags: [python, programming, backend, development]
aliases: [Python基础, Python编程]
---

# Python语言基础

## 概述

Python是一种高级编程语言，以其简洁的语法和丰富的生态系统而闻名。本笔记涵盖Python的基础语法和核心概念。

## 基本概念

### 注释
- **单行注释**: 使用 `#` 进行单行注释
- **多行注释**: 使用 `'''` 或 `"""` 进行多行注释

### 输入输出
- **print()**: 用于输出内容到屏幕，支持多个参数，参数间默认以空格分隔
- **input()**: 用于获取用户输入

## 数据类型

### 数字类型
- **int**: 整型，支持多种进制表示（二进制、八进制、十六进制）
- **float**: 浮点型，注意浮点数运算可能存在精度损失

### 字符类型
- **str**: 字符串，使用单引号或双引号定义，支持拼接(+)和重复(*) 操作
- **bool**: 布尔类型，True和False，用于逻辑判断

### 特殊值
- **None**: 表示空值

## 变量与类型转换

### 变量命名规则
- 变量名区分大小写
- 可以包含字母、数字和下划线
- 不能以数字开头

### 类型转换函数
- `int()`: 转换为整型
- `float()`: 转换为浮点型
- `str()`: 转换为字符串
- `bool()`: 转换为布尔型

## 运算符

### 算术运算符
- `+`: 加法
- `-`: 减法
- `*`: 乘法
- `/`: 除法
- `//`: 整除
- `%`: 取余
- `**`: 幂运算

## 相关资源

### 参考资料
- Python官方文档
- Python教程

### 相关笔记
- [[Python进阶特性]]
- [[Python Web开发]]
- [[Python数据处理]]

### 外部链接
- [Python官网](https://www.python.org/)

## 总结

Python的基础语法简洁明了，是编程入门的理想选择。掌握基本的数据类型、变量和运算符是学习Python的第一步。

---
*最后更新: 2025-01-08*
*原始位置: 5-Full Notes/Python语言基础.md*
