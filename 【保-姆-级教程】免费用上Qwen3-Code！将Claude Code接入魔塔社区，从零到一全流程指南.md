【保-姆-级教程】免费用上Qwen3-Code！将Claude Code接入魔塔社区，从零到一全流程指南

你是否也曾体验过 AI 编程助手的强大，却又对其高昂的费用望而却步？你是否也羡慕 Claude Code 丝滑的交互体验，但又想利用魔塔社区（ModelScope）提供的免费、强大的 Qwen3-Code 模型？

别担心，本文将手把手带你走完所有流程，将这两者完美结合，让你拥有一个 **免费、强大、稳定、开机自启** 的专属 AI 编程助手。

### 最终效果

*   在终端里直接使用 `claude` 命令（或者一个你喜欢的自定义命令），体验原生的 Claude Code 交互。
*   背后实际提供支持的，是魔塔社区免费的、强大的 Qwen3-Code 模型。
*   整个服务在后台静默运行，不占用终端窗口，并且电脑重启后会自动恢复。

### 准备工作

在开始之前，请确保你已经准备好以下几样东西：

1.  **一台 Mac 电脑**：本教程基于 macOS 环境。
2.  **Homebrew**：macOS 的包管理器，如果你还没有，请访问 [Homebrew 官网](https://brew.sh/index_zh-cn) 安装。
3.  **Node.js 和 npm**：用于安装进程管理工具 `pm2`。同样可以通过 Homebrew 安装：`brew install node`。
4.  **魔塔社区 (ModelScope) 账号**：你需要注册并登录魔塔社区，获取一个免费的 API Key。
5.  **一个好用的终端工具**：例如系统自带的“终端”或 iTerm2。

### 第一步：获取核心代理项目

我们要利用 GitHub 上的一个开源项目 `claude-code-proxy`，它像一个翻译官，能将 Claude Code 的请求转换后发给其他模型。

```bash
git clone https://github.com/1rgs/claude-code-proxy.git
cd claude-code-proxy
```

### 第二步：安装项目管理工具 `uv`

项目使用 `uv` 这个新兴的 Python 包管理器，它速度飞快。官方推荐的 `curl` 安装方式在国内网络环境下很容易卡住，**最佳实践是使用 Homebrew 安装**。

```bash
brew install uv
```
安装后，可以运行 `uv --version` 检查一下是否成功。

### 第三步：修改项目配置，适配你的环境

这是最关键的一步！我们需要让项目使用我们自己的 Python 版本和指定的 Qwen3-Code 模型。

1.  **适配你的 Python 版本**：
    该项目默认需要 Python 3.10，但我们没必要专门去装旧版本。假设你的电脑上已经有了 Python 3.12，我们直接让项目来适应你。
    ```bash
    # 将你的 Python 版本号写入配置文件，覆盖旧的
    echo "3.12.7" > .python-version # 请替换成你自己的版本号

    # 删除旧的依赖锁定文件，非常重要！
    rm uv.lock
    ```

2.  **配置 API 和模型信息**：
    我们需要创建一个 `.env` 文件来存放我们的密钥和模型地址。
    ```bash
    # 复制模板文件
    cp .env.example .env

    # 使用文本编辑器打开 .env 文件，比如用 VS Code 或系统自带的
    open .env
    ```
    将文件内容修改为如下所示：

    ```ini
    # ANTHROPIC 和 GEMINI 的 Key 我们用不到，留空即可
    ANTHROPIC_API_KEY=
    GEMINI_API_KEY=

    # ‼️ 这一项必须是 "openai"，因为魔塔的 API 兼容 OpenAI 标准
    PREFERRED_PROVIDER="openai"

    # ‼️ 填入你从魔塔社区获取的 API Key (那个 "ms-..." 开头的)
    OPENAI_API_KEY="你的ModelScope API Key"

    # ‼️ 填入魔塔社区的 API 地址
    OPENAI_API_BASE="https://api-inference.modelscope.cn/v1/"

    # ‼️ 将大小模型都指向 Qwen3-Code 的模型 ID
    BIG_MODEL="Qwen/Qwen3-Coder-480B-A35B-Instruct"
    SMALL_MODEL="Qwen/Qwen3-Coder-480B-A35B-Instruct"
    ```
    **保存并关闭文件。**

### 第四步：安装依赖（并解决国内网络问题）

现在，我们使用 `uv` 来安装项目所需的所有 Python 包。同样，为了避免网络问题，我们先为 `uv` 配置国内的镜像源。

1.  **配置国内镜像源（一劳永逸）**：
    打开你的 Shell 配置文件（通常是 `~/.zshrc`），在末尾添加下面这行，然后 `source ~/.zshrc` 重启终端使其生效。
    ```bash
    # 为 uv/pip 配置清华大学的 PyPI 镜像源
    export UV_INDEX_URL="https://pypi.tuna.tsinghua.edu.cn/simple"
    ```

2.  **开始安装**：
    在 `claude-code-proxy` 项目目录下，运行：
    ```bash
    uv sync
    ```
    由于我们已经做好了所有铺垫，这次 `uv` 会顺利地使用你的 Python 版本，并从国内镜像飞速下载所有依赖。看到一长串绿色的 `+` 号就代表成功了。

### 第五步：让服务在后台运行并开机自启

我们不希望代理服务一直占着一个终端窗口。这里我们使用专业的进程管理工具 `pm2`。

1.  **安装 pm2**：
    ```bash
    npm install -g pm2
    ```

2.  **使用 pm2 启动服务**：
    我们给这个服务起一个好记的名字，比如 `claude-qwen`。
    ```bash
    pm2 start "uv run uvicorn server:app --host 0.0.0.0 --port 8082" --name "claude-qwen"
    ```
    运行 `pm2 status`，你就能看到 `claude-qwen` 已经 `online` 了。

3.  **设置开机自启**：
    这是最激动人心的一步，让你的服务变成永久的。
    ```bash
    # 1. 生成启动脚本
    pm2 startup

    # 2. 复制并执行 pm2 给出的那条 sudo 命令
    #    (它会让你输入电脑密码)

    # 3. 保存当前正在运行的服务列表，让 pm2 记住它
    pm2 save
    ```
    好了！现在你可以重启电脑试试，重启后运行 `pm2 status`，会发现 `claude-qwen` 已经自动在后台为你运行了。

### 第六步：最后的魔法——创建一个快捷命令

服务虽然在后台了，但每次我们还是需要输入 `ANTHROPIC_BASE_URL=http://localhost:8082 claude` 来连接，太麻烦了。

我们可以创建一个**别名 (Alias)**，实现输入自定义命令就能启动的效果。

1.  **再次打开你的 Shell 配置文件** (`~/.zshrc`)。
2.  **在文件末尾添加一行**：
    ```bash
    # 为连接本地 Qwen 代理的 Claude Code 创建一个快捷别名
    alias claude-qwen="ANTHROPIC_BASE_URL=http://localhost:8082 claude"
    ```
3.  **`source ~/.zshrc` 或重启终端使其生效**。

### 享受成果！

现在，所有工作都已完成！

你只需要打开任何一个终端窗口，输入你刚刚创建的命令：

```bash
claude-qwen
```

然后你就可以开始与一个拥有 Claude 前端交互体验和 Qwen3 内核的、完全免费的、属于你自己的 AI 编程助手对话了！

快把这个教程转发给你的朋友，让他们也一起享受这份自由和强大吧！