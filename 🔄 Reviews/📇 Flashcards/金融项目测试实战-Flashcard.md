# 金融项目测试实战闪卡

### 金融项目的用户管理模块包含哪些接口？
1. 注册接口（register）：
   - URL路径：/futureloan/mvc/api/member/register
   - 参数：url, br（baseRequests实例）, data（注册数据）
   - 请求方式：POST

2. 登录接口（login）：
   - URL路径：/futureloan/mvc/api/member/login
   - 参数：url, br, data
   - 请求方式：POST

3. 充值接口（recharge）：
   - URL路径：/futureloan/mvc/api/member/recharge
   - 参数：url, br, data
   - 请求方式：POST

4. 添加项目接口（add_project）：
   - URL路径：/futureloan/mvc/api/loan/add
   - 参数：url, br, data
   - 请求方式：POST

### BaseRequest类的主要功能是什么？
1. 初始化：
   - 创建session实例

2. GET请求方法：
   - 发送GET请求
   - 打印请求和响应信息
   - 异常处理

3. POST请求方法：
   - 发送POST请求
   - 支持data和json参数
   - 打印请求和响应信息
   - 异常处理

### 项目中的文件读取功能包括哪些？
1. 项目路径获取：
   - get_project_path()函数
   - 获取当前文件的绝���路径
   - 获取项目根目录

2. YAML文件读取：
   - read_yaml()函数
   - 读取并解析YAML文件
   - 返回解析后的数据

3. INI文件读取：
   - read_ini()函数
   - 读取配置文件的指定section和key
   - 返回对应的value

### 数据库操作包括哪些功能？
1. 用户数据清理：
   - delete_all_customer()函数
   - 清除member表中的所有数据
   - 保证测试环境无干扰

2. 用户ID获取：
   - get_member_id()函数
   - 根据手机号查询用户ID
   - 返回查询结果

### 项目的数据文件包含哪些内容？
1. data_add.yaml：
   - 测试用例数据
   - 包含注册数据、项目数据
   - 预期结果

2. env.ini：
   - 全局配置文件
   - URL配置
   - 数据库配置

3. setup.yaml：
   - 前置数据
   - 用户信息配置

### 项目的测试框架如何设计？
1. 测试前置（conftest）：
   - prepare fixture
   - 环境准备（清理用户数据）
   - 执行注册和登录
   - 测试后清理环境

2. 测试用例：
   - 使用pytest参数化
   - 读取YAML测试数据
   - 执行接口测试
   - 验证测试结果 