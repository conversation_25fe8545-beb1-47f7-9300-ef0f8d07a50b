# Docker闪卡

### 应用程序部署方式有哪些演进过程？
1. 传统部署方式：
   - 开发人员交付安装包（exe、msi、tar、war）
   - 缺点：
     * 对运行环境有依赖
     * 计算机资源难以合理分配
     * 程序之间互相影响

2. 虚拟化部署方式：
   - 一台物理机运行多个虚拟机
   - 缺点：
     * 每个虚拟机都需要操作系统
     * 虚拟机维护耗费精力

3. 容器化部署方式：
   - 共享操作系统
   - 优点：
     * 应用程序互相隔离
     * 系统资源需求低
     * 包含应用及其依赖环境

### Docker的核心概念有哪些？
1. 仓库（Repository）：
   - 存放镜像文件的地方
   - 分为公有仓库和私有仓库

2. 镜像（Image）：
   - 包含应用程序及依赖的文件
   - 常用命令：
     * docker images：查看镜像
     * docker pull：下载镜像
     * docker search：搜索镜像
     * docker inspect：查看详细信息
     * docker rmi：删除镜像

3. 容器（Container）：
   - 通过镜像创建
   - 可理解为简易版Linux系统+应用程序
   - 常用操��：启动、停止、重启、删除

### Docker如何进行数据挂载？
1. 数据挂载作用：
   - 保留容器删除后的数据
   - 实现多个容器共享数据

2. 实现方式：
   - 使用-v参数
   - 示例：docker run -id --name abc -p 88:80 -v=nginx_abc:/usr/share/nginx/html/ nginx:1.20.2

### 如何使用Dockerfile创建镜像？
1. 创建镜像：
   - docker build -t 镜像名:版本 -f Dockerfile .

2. 使用镜像：
   - docker images：检查创建的镜像
   - docker run：创建容器
   - docker exec：进入容器

### 如何搭建容器化的租车系统？
1. 网络配置：
   - 创建自定义网络：docker network create carrental

2. MySQL容器：
   - 创建容器：docker run -di --name car_mysql --net carrental
   - 配置数据库名和密码
   - 端口映射

3. Tomcat容器：
   - 创建容器：docker run -id --name car_tom
   - 端口映射
   - 网络配置

4. 应用部署：
   - 上传war包
   - 修改配置文件
   - 配置数据库连接
   - 配置图片上传路径

### Docker常用命令有哪些？
1. 系统命令：
   - docker version：查看版本
   - systemctl status/start/stop/restart docker：服务管理
   - ps -ef | grep docker：查看进程

2. 容器操作：
   - docker logs：查看日志
   - docker top：查看容器进程
   - docker stats：查看统计信息
   - docker kill：强制停止容器 