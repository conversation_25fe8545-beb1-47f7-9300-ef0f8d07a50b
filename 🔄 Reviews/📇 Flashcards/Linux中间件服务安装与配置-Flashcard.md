# Linux中间件服务安装与配置闪卡

### RabbitMQ如何安装和配置？
1. 安装步骤：
   - 注意主机名不能含有数字
   - yum install erlang
   - yum install rabbitmq-server

2. 服务管理：
   - 启动服务：systemctl start rabbitmq-server
   - 启动管理端：rabbitmq-plugins enable rabbitmq_management
   - 重启服务

3. 账号配置：
   - 添加用户：rabbitmqctl add user admin 123456
   - 设置权限：rabbitmqctl set_user_tags admin administrator
   - 设置访问权限：rabbitmqctl set_permissions p/ admin ".*" ".*" ".*"

### Redis如何安装和配置？
1. 安装步骤：
   - 安装源
   - yum install redis
   - systemctl start redis
   - systemctl status redis

2. 配置文件修改：
   - 编辑/etc/redis.conf文件
   - 注释bind 127.0.0.1（允许远程访问）
   - 设置密码：requirepass 123456
   - 设置后台运行：daemonize yes

### MinIO如何安装和配置？
1. 安装步骤：
   - 下载MinIO并上传到/opt/minio目录
   - 给予执行权限：chmod +x minio
   - 创建数据目录：/var/lib/minio

2. 启动服务：
   - 首次启动：/opt/minio/minio server /var/lib/minio
   - 后台运行：nohup /opt/minio/minio server /var/lib/minio > /var/log/minio.log &

### 如何部署项目前端文件？
1. 文件上传：
   - 上传dist.zip到/var/www目录
   - 解压文件：unzip dist.zip

2. 文件检查：
   - 进入dist目录
   - 检查文件列表：
     * favicon.ico
     * getAuthorization.js
     * index.html
     * static目录
   - 确认路径：/var/www/dist 