# 安全测试基础闪卡

### 有哪些典型的安全问题案例？
1. 用户信息泄露：
   - 用户邮箱信息泄露
   - 优衣库用户个人信息、电子邮件、地址、银行卡等资料泄露
   - Facebook用户信息任由下载
   - 雅虎邮箱信息泄露

2. 代码泄露：
   - B站后台源码被上传到GitHub上

3. 勒索攻击：
   - 2017年勒索病毒事件，威胁删除文件除非支付赎金

### 什么是OWASP？它的作用是什么？
1. 基本信息：
   - Open Web Application Security Project
   - 非盈利机构
   - 定期公布TopN安全问题

2. 提供的功能：
   - 提供学习平台
   - 提供含有漏洞的应用用于学习

### 文件上传漏洞是什么？如何防范？
1. 漏洞描述：
   - 大部分Web应用都有文件上传功能
   - 攻击者可能上传木马文件、病毒、恶意脚本
   - 在服务器上执行获取非法数据

2. 漏洞原理：
   - 开发人员处理文件时未做合法性校验
   - 直接将文件上传到服务器中

### 什么是XSS攻击？
1. 基本概念：
   - 跨站脚本攻击（Cross-Site Scripting）
   - 是一���网页安全漏洞

### 什么是SQL注入？如何防范？
1. 漏洞原理：
   - 对用户输入的数据没有进行检查
   - 直接将用户输入的内容与后台SQL语句拼接执行
   - 可能导致拖库，用户信息泄露

2. 攻击类型：
   - 基于错误的注入
   - 通过输入错误信息与后台SQL语句拼接成非法SQL语句

### 什么是DDoS攻击？
1. 基本概念：
   - 分布式阻断服务（Distributed Denial of Service）
   - 通过大量请求使服务瘫痪 