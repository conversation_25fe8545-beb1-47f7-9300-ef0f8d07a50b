好的，根据您提供的 API 文档（testApi.md 和 openapi.json），并基于 UniApp (Vue 2) 的标准结构，我为您生成一个详细的前端项目目录结构。

这个结构将各个功能模块（认证、用户、词汇、听力、阅读、口语、社区、统计）清晰地分开，便于开发和维护。

```markdown
```
├── README.md             // 项目说明文件
├── package.json          // npm 包管理文件
├── .gitignore            // Git 忽略文件
├── uni.scss              // UniApp 全局 Sass/SCSS 变量和样式
├── main.js               // Vue 应用入口文件，初始化 Vue、Vuex、挂载全局方法等
├── App.vue               // Vue 根组件，用于设置全局样式、监听应用生命周期
├── manifest.json         // UniApp 应用配置，包括应用名称、图标、权限等
├── pages.json            // UniApp 页面路由、导航栏、 tabBar 等配置
├── static/               // 静态资源目录，如图片、字体文件等
│   ├── logo.png
│   └── ...其他静态资源
├── components/           // 全局组件目录
│   ├── common/           // 通用基础组件，如按钮、输入框等
│   │   ├── BaseButton.vue
│   │   ├── BaseInput.vue
│   │   └── BaseModal.vue
│   ├── layout/           // 布局相关组件
│   │   ├── Header.vue
│   │   └── Footer.vue
│   ├── vocabulary/       // 词汇模块相关组件
│   │   ├── VocabCard.vue       // 词汇列表项组件
│   │   ├── VocabDetailsDisplay.vue // 词汇详情展示组件
│   │   └── VocabQuestion.vue   // 词汇测试题目组件
│   ├── listening/      // 听力模块相关组件
│   │   ├── ListeningCard.vue   // 听力材料列表项组件
│   │   ├── AudioPlayer.vue     // 音频播放器组件
│   │   ├── ListeningTextDisplay.vue // 听力原文展示组件
│   │   └── ListeningQuestion.vue // 听力题目组件
│   ├── reading/        // 阅读模块相关组件
│   │   ├── ReadingCard.vue     // 阅读材料列表项组件
│   │   ├── ReadingContentDisplay.vue // 阅读内容展示组件
│   │   └── ReadingQuestion.vue // 阅读题目组件
│   ├── speaking/       // 口语模块相关组件
│   │   ├── SpeakingCard.vue    // 口语材料列表项组件
│   │   ├── SpeakingPromptDisplay.vue // 口语提示内容展示组件
│   │   └── SpeakingRecorder.vue  // 录音及播放组件
│   ├── community/      // 社区模块相关组件
│   │   ├── PostCard.vue        // 帖子列表项组件
│   │   ├── PostDetailsDisplay.vue // 帖子详情展示组件
│   │   ├── CommentList.vue     // 评论列表组件
│   │   └── CommentForm.vue     // 发表评论组件
│   ├── user/           // 用户模块相关组件
│   │   ├── UserInfoDisplay.vue // 用户信息展示组件
│   │   └── SettingsForm.vue    // 用户设置表单组件
│   └── statistics/     // 统计模块相关组件
│       ├── StatsCard.vue       // 单个统计项卡片
│       └── ProgressChart.vue   // 学习进度图表组件 (可能需要引入第三方库)
├── pages/                // 业务页面目录
│   ├── index/            // 首页
│   │   └── index.vue
│   ├── auth/             // 认证模块页面
│   │   ├── login.vue     // 登录页
│   │   └── register.vue  // 注册页
│   ├── user/             // 用户模块页面
│   │   ├── profile.vue   // 用户详情/个人中心页 (`/users/me`)
│   │   ├── settings.vue  // 用户设置页 (`/users/me/settings`)
│   │   └── checkin.vue   // 签到记录页 (`/users/me/checkin` GET)
│   ├── vocabulary/       // 词汇模块页面
│   │   ├── list.vue      // 词汇列表页 (`/vocabulary`)
│   │   ├── detail.vue    // 词汇详情页 (`/vocabulary/{vocabId}`)
│   │   └── test.vue      // 词汇测试页 (包含提交逻辑 `/users/me/vocabulary/test/submit`)
│   ├── listening/      // 听力模块页面
│   │   ├── list.vue      // 听力材料列表页 (`/listening`)
│   │   ├── detail.vue    // 听力材料详情页 (`/listening/{listeningId}`)
│   │   └── practice.vue  // 听力练习页 (包含播放、题目、提交 `/users/me/listening/submit`)
│   ├── reading/        // 阅读模块页面
│   │   ├── list.vue      // 阅读材料列表页 (`/reading`)
│   │   ├── detail.vue    // 阅读材料详情页 (`/reading/{readingId}`)
│   │   └── practice.vue  // 阅读练习页 (包含内容、题目、提交 `/users/me/reading/submit`)
│   ├── speaking/       // 口语模块页面
│   │   ├── list.vue      // 口语材料列表页 (`/speaking`)
│   │   ├── detail.vue    // 口语材料详情页 (`/speaking/{speakingId}`)
│   │   └── practice.vue  // 口语练习页 (包含提示、录音、提交 `/users/me/speaking/submit`)
│   ├── community/      // 社区模块页面
│   │   ├── postList.vue    // 社区帖子列表页 (`/community/posts`)
│   │   ├── postDetail.vue  // 社区帖子详情页 (包含评论列表/发表 `/community/posts/{postId}`, `/community/posts/{postId}/comments`)
│   │   ├── createPost.vue  // 发布新帖子页 (`/community/posts`)
│   │   └── userPosts.vue   // 用户发布的帖子列表页 (`/users/{authorId}/posts`, `/users/me/posts`)
│   └── statistics/     // 统计模块页面
│       └── dashboard.vue   // 学习统计仪表盘页 (整合 `/users/me/vocabulary/statistics`, `/listening/statistics`, `/users/me/reading/statistics`, `/users/me/speaking/statistics`)
├── api/                  // API 请求模块
│   ├── request.js        // 基础请求封装 (统一处理 baseUrl, token, 错误码等)
│   ├── auth.js           // 认证相关 API (login, register)
│   ├── user.js           // 用户相关 API (me, settings, checkin)
│   ├── vocabulary.js     // 词汇相关 API (list, detail, submit test)
│   ├── listening.js      // 听力相关 API (list, detail, submit practice)
│   ├── reading.js        // 阅读相关 API (list, detail, submit practice)
│   ├── speaking.js       // 口语相关 API (list, detail, submit practice)
│   ├── community.js      // 社区相关 API (posts, post detail, comments, like)
│   └── statistics.js     // 统计相关 API (vocab, listening, reading, speaking stats)
├── store/                // Vuex 状态管理目录
│   ├── index.js          // Vuex store 入口
│   └── modules/          // 模块化 store
│       ├── user.js       // 用户模块 (存放 token, user info, settings)
│       └── ...           // 其他模块 (如 stats 等如果需要全局状态)
└── utils/                // 工具函数目录
    ├── index.js          // 工具函数导出
    └── ...               // 其他工具函数文件 (如 date.js, validator.js 等)
```

### 目录结构说明

1.  **`uni.scss`**: 用于定义全局的样式变量、混入(mixin)或公共样式。
2.  **`main.js`**: UniApp 项目的入口文件，在这里创建 Vue 实例，配置 Vuex store，挂载全局方法等。
3.  **`App.vue`**: 应用根组件，主要用于监听应用的生命周期，设置全局样式等。
4.  **`manifest.json`**: UniApp 项目的核心配置文件，用于打包生成各端应用时的配置，如应用名称、图标、权限等。
5.  **`pages.json`**: UniApp 项目的页面路由配置，包括页面路径、窗口样式、顶部导航、底部 tabBar 等。**所有在 `pages` 目录下新建的页面都必须在这里注册才能被访问。**
6.  **`static/`**: 存放项目中的静态资源，打包时会被直接复制到各平台的相应目录。
7.  **`components/`**: 存放可复用的 Vue 组件。按照功能模块进行分组，可以进一步细分为 `common`（通用组件）、`layout`（布局组件）和各个业务模块的组件。
8.  **`pages/`**: 存放应用的各个页面。UniApp 要求页面文件必须存放在此目录下，并且在 `pages.json` 中进行注册。同样按照功能模块进行分组。
9.  **`api/`**: 存放所有与后端 API 交互相关的代码。
    *   `request.js`: 封装 `uni.request`，处理请求头（特别是 `Authorization: Bearer {{token}}`）、请求参数的默认设置、响应数据的统一处理（如检查 `error` 码）、错误处理等。
    *   其他文件按业务模块划分，每个文件负责对应模块的 API 调用函数。这些函数内部会调用 `request.js` 封装的方法。
10. **`store/`**: 存放 Vuex 状态管理相关的代码。
    *   `index.js`: 导入各个模块并创建 Vuex store 实例。
    *   `modules/`: 按照业务模块划分的 Vuex store 模块，例如 `user.js` 用于管理用户的登录状态、token、用户信息等。
11. **`utils/`**: 存放项目中使用到的各种工具函数，例如日期格式化、数据验证、权限判断等。

### `pages.json` 示例片段

为了让页面能够正确工作，你需要在 `pages.json` 中配置它们。

```json
{
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "英语学习应用"
      }
    },
    {
      "path": "pages/auth/login",
      "style": {
        "navigationBarTitleText": "登录"
      }
    },
    {
      "path": "pages/auth/register",
      "style": {
        "navigationBarTitleText": "注册"
      }
    },
    {
      "path": "pages/user/profile",
      "style": {
        "navigationBarTitleText": "个人中心"
      }
    },
     {
      "path": "pages/user/settings",
      "style": {
        "navigationBarTitleText": "设置"
      }
    },
    {
      "path": "pages/user/checkin",
      "style": {
        "navigationBarTitleText": "签到记录"
      }
    },
    // 词汇模块
    {
      "path": "pages/vocabulary/list",
      "style": {
        "navigationBarTitleText": "词汇本"
      }
    },
    {
      "path": "pages/vocabulary/detail",
      "style": {
        "navigationBarTitleText": "词汇详情"
      }
    },
    {
      "path": "pages/vocabulary/test",
      "style": {
        "navigationBarTitleText": "词汇测试"
      }
    },
    // 听力模块
     {
      "path": "pages/listening/list",
      "style": {
        "navigationBarTitleText": "听力练习"
      }
    },
    {
      "path": "pages/listening/detail",
      "style": {
        "navigationBarTitleText": "听力材料"
      }
    },
    {
      "path": "pages/listening/practice",
      "style": {
        "navigationBarTitleText": "听力练习"
      }
    },
    // 阅读模块
     {
      "path": "pages/reading/list",
      "style": {
        "navigationBarTitleText": "阅读理解"
      }
    },
    {
      "path": "pages/reading/detail",
      "style": {
        "navigationBarTitleText": "阅读材料"
      }
    },
    {
      "path": "pages/reading/practice",
      "style": {
        "navigationBarTitleText": "阅读练习"
      }
    },
    // 口语模块
     {
      "path": "pages/speaking/list",
      "style": {
        "navigationBarTitleText": "口语练习"
      }
    },
    {
      "path": "pages/speaking/detail",
      "style": {
        "navigationBarTitleText": "口语材料"
      }
    },
    {
      "path": "pages/speaking/practice",
      "style": {
        "navigationBarTitleText": "口语练习"
      }
    },
    // 社区模块
     {
      "path": "pages/community/postList",
      "style": {
        "navigationBarTitleText": "社区"
      }
    },
    {
      "path": "pages/community/postDetail",
      "style": {
        "navigationBarTitleText": "帖子详情"
      }
    },
    {
      "path": "pages/community/createPost",
      "style": {
        "navigationBarTitleText": "发布帖子"
      }
    },
    {
      "path": "pages/community/userPosts",
      "style": {
        "navigationBarTitleText": "我的帖子"
      }
    },
    // 统计模块
     {
      "path": "pages/statistics/dashboard",
      "style": {
        "navigationBarTitleText": "学习统计"
      }
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "英语学习",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8"
  },
  "tabBar": {
    "color": "#7A7E83",
    "selectedColor": "#3cc51f",
    "borderStyle": "black",
    "backgroundColor": "#ffffff",
    "list": [
      {
        "pagePath": "pages/index/index",
        "iconPath": "static/tabbar/home.png",
        "selectedIconPath": "static/tabbar/home-active.png",
        "text": "首页"
      },
      {
        "pagePath": "pages/vocabulary/list", // 或者将首页设为tab，其他页面通过首页进入
        "iconPath": "static/tabbar/vocabulary.png",
        "selectedIconPath": "static/tabbar/vocabulary-active.png",
        "text": "词汇"
      },
       {
        "pagePath": "pages/community/postList",
        "iconPath": "static/tabbar/community.png",
        "selectedIconPath": "static/tabbar/community-active.png",
        "text": "社区"
      },
      {
        "pagePath": "pages/user/profile",
        "iconPath": "static/tabbar/profile.png",
        "selectedIconPath": "static/tabbar/profile-active.png",
        "text": "我的"
      }
    ]
  }
}
```

### 核心文件示例 (伪代码)

#### `api/request.js`

```javascript
import store from '../store'; // 假设使用了 Vuex

const BASE_URL = 'http://localhost:8080/api/v1'; // 从环境变量或配置文件读取

function request(options) {
  const token = store.state.user.token; // 从 store 获取 token

  return new Promise((resolve, reject) => {
    uni.request({
      url: BASE_URL + options.url,
      method: options.method || 'GET',
      data: options.data,
      header: {
        'Content-Type': options.contentType || 'application/json',
        ...(token ? { 'Authorization': `Bearer ${token}` } : {}) // 动态添加 token
      },
      success: (res) => {
        // 统一处理后端返回的 error 码
        if (res.statusCode === 200 && res.data.error === 0) {
          resolve(res.data.body); // 成功时只返回 body 数据
        } else {
          // 统一处理错误
          const errorMsg = res.data.message || `请求失败: ${res.statusCode}`;
          uni.showToast({
            title: errorMsg,
            icon: 'none'
          });
          // 如果是认证失败，可以跳转到登录页
          if (res.statusCode === 401) {
             store.dispatch('user/logout'); // 清除本地 token
             uni.navigateTo({ url: '/pages/auth/login' });
          }
          reject({ statusCode: res.statusCode, message: errorMsg, data: res.data });
        }
      },
      fail: (err) => {
        // 处理网络错误等
        uni.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
        reject(err);
      }
    });
  });
}

// 导出常用的请求方法
export default {
  get: (url, data, options) => request({ url, data, method: 'GET', ...options }),
  post: (url, data, options) => request({ url, data, method: 'POST', ...options }),
  put: (url, data, options) => request({ url, data, method: 'PUT', ...options }),
  delete: (url, data, options) => request({ url, data, method: 'DELETE', ...options }),
  // 特殊处理 multipart/form-data，例如口语上传
  upload: (url, filePath, name, data, options) => {
     const token = store.state.user.token;
     return new Promise((resolve, reject) => {
        uni.uploadFile({
            url: BASE_URL + url,
            filePath: filePath,
            name: name, // 文件参数名，对应后端接口的字段名
            formData: data, // 其他附带的表单数据
            header: {
               ...(token ? { 'Authorization': `Bearer ${token}` } : {})
            },
            success: (uploadRes) => {
               // uploadFile 的 success 回调是服务器响应，需要手动解析
               const resData = JSON.parse(uploadRes.data); // 假设后端返回 JSON 字符串
               if (uploadRes.statusCode === 200 && resData.error === 0) {
                  resolve(resData.body);
               } else {
                   const errorMsg = resData.message || `上传失败: ${uploadRes.statusCode}`;
                   uni.showToast({ title: errorMsg, icon: 'none' });
                   if (uploadRes.statusCode === 401) {
                       store.dispatch('user/logout');
                       uni.navigateTo({ url: '/pages/auth/login' });
                   }
                   reject({ statusCode: uploadRes.statusCode, message: errorMsg, data: resData });
               }
            },
            fail: (uploadErr) => {
                uni.showToast({ title: '上传失败', icon: 'none' });
                reject(uploadErr);
            }
        });
     });
  }
};
```

#### `store/modules/user.js`

```javascript
import api from '../../api/request'; // 引入封装好的请求方法

const user = {
  namespaced: true, // 开启命名空间
  state: {
    token: uni.getStorageSync('token') || '', // 尝试从缓存读取 token
    userInfo: uni.getStorageSync('userInfo') || null, // 尝试从缓存读取用户信息
  },
  mutations: {
    SET_TOKEN(state, token) {
      state.token = token;
      uni.setStorageSync('token', token); // 同步保存到缓存
    },
    SET_USER_INFO(state, userInfo) {
      state.userInfo = userInfo;
      uni.setStorageSync('userInfo', userInfo); // 同步保存到缓存
    },
    CLEAR_AUTH(state) {
      state.token = '';
      state.userInfo = null;
      uni.removeStorageSync('token');
      uni.removeStorageSync('userInfo');
    },
  },
  actions: {
    // 登录
    async login({ commit }, credentials) {
      try {
        const response = await api.post('/auth/login', credentials);
        commit('SET_TOKEN', response.token);
        // 登录成功后通常还会返回用户信息，或者需要额外调用接口获取
        // 假设login接口返回了部分用户信息
        commit('SET_USER_INFO', {
             userId: response.userId,
             username: response.username,
             // 其他可以在此处或单独调用getUserInfo获取并SET_USER_INFO
         });
        return response; // 返回登录成功响应
      } catch (error) {
        // 交给 request.js 中的错误处理或在这里进一步处理
        return Promise.reject(error); // 抛出错误
      }
    },
    // 注册
     async register({ commit }, userData) {
       try {
         const response = await api.post('/auth/register', userData);
         // 注册成功后是否直接登录？如果需要，可以在这里调用 login action
         // await this.dispatch('user/login', { username: userData.username, password: userData.password });
         return response;
       } catch (error) {
         return Promise.reject(error);
       }
     },
    // 获取当前用户信息
    async getUserInfo({ commit }) {
      try {
        const userInfo = await api.get('/users/me');
        commit('SET_USER_INFO', userInfo);
        return userInfo;
      } catch (error) {
        return Promise.reject(error);
      }
    },
    // 更新用户信息
     async updateUserInfo({ commit, state }, userData) {
        try {
            // 注意：更新接口通常只需要发送需要修改的字段，而不是整个 User schema
            const updatedInfo = await api.put('/users/me', userData);
            // 更新 store 中的用户信息
            commit('SET_USER_INFO', { ...state.userInfo, ...updatedInfo });
            return updatedInfo;
        } catch (error) {
            return Promise.reject(error);
        }
     },
    // 登出
    logout({ commit }) {
      commit('CLEAR_AUTH');
      // 可能还需要调用后端登出接口
      // api.post('/auth/logout');
    },
    // 检查用户是否已登录（根据token是否存在）
    checkLogin({ state }) {
       return !!state.token;
    }
  },
  getters: {
     isLoggedIn(state) {
        return !!state.token;
     },
     currentUser(state) {
        return state.userInfo;
     }
  }
};

export default user;
```

#### `api/vocabulary.js`

```javascript
import request from './request';

export default {
  // 获取词汇列表
  getVocabularyList(params) {
    return request.get('/vocabulary', params);
  },
  // 获取词汇详情
  getVocabularyDetail(vocabId) {
    return request.get(`/vocabulary/${vocabId}`);
  },
  // 提交词汇测试结果
  submitVocabularyTest(testData) {
    // testData 结构应符合 SubmitVocabularyTestRequest schema
    return request.post('/users/me/vocabulary/test/submit', testData);
  }
  // 可以根据需要添加获取随机词汇接口
  // getRandomVocabulary(params) {
  //    return request.get('/vocabulary/random', params);
  // }
};
```

以此类推，您可以为听力、阅读、口语、社区、统计等模块创建相应的 API 和页面/组件，并在 Vuex store 中管理必要的状态。

这个目录结构提供了一个清晰、模块化的基础，您可以根据项目的具体需求和规模进行调整和扩展。记得在使用 Vuex 和封装的请求方法时，在各个页面的 `script` 部分导入并使用它们。